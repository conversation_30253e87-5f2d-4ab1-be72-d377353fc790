import 'package:cloud_firestore/cloud_firestore.dart';
import 'message_model.dart';

class ConversationModel {
  final String id;
  final String title;
  final String category;
  final DateTime createdAt;
  final String createdBy;
  final List<MessageModel> messages;
  final Map<String, dynamic> readBy;
  final String difficulty; // سهل، متوسط، صعب
  final String level; // المستوى (1، 2، 3)

  ConversationModel({
    required this.id,
    required this.title,
    required this.category,
    required this.createdAt,
    required this.createdBy,
    required this.messages,
    required this.readBy,
    required this.difficulty,
    this.level = '1', // المستوى الافتراضي هو 1
  });

  // Constructor para crear desde Firestore
  factory ConversationModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    // Convertir la lista de mensajes
    List<MessageModel> messagesList = [];
    if (data['messages'] != null) {
      messagesList = List<MessageModel>.from(
        (data['messages'] as List).map(
          (message) => MessageModel.fromMap(message),
        ),
      );
    }

    return ConversationModel(
      id: doc.id,
      title: data['title'] ?? '',
      category: data['category'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      messages: messagesList,
      readBy: data['readBy'] ?? {},
      difficulty: data['difficulty'] ?? 'medium',
      level: data['level'] ?? '1',
    );
  }

  // Método para convertir a Map para Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'category': category,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'messages': messages.map((message) => message.toMap()).toList(),
      'readBy': readBy,
      'difficulty': difficulty,
      'level': level,
    };
  }

  // Método para verificar si la conversación ha sido leída por el usuario actual
  bool isReadByCurrentUser(String userId) {
    return readBy.containsKey(userId);
  }

  // Método para obtener el progreso de lectura del usuario actual
  double getReadProgress(String userId) {
    if (!readBy.containsKey(userId)) return 0.0;

    Map<String, dynamic> userProgress = readBy[userId];
    int readCount = userProgress['readCount'] ?? 0;

    // Si no hay mensajes, devolver 0
    if (messages.isEmpty) return 0.0;

    // Calcular el progreso
    return readCount / messages.length;
  }

  // Método para obtener la puntuación promedio de pronunciación
  double getPronunciationScore(String userId) {
    if (!readBy.containsKey(userId)) return 0.0;

    Map<String, dynamic> userProgress = readBy[userId];
    return userProgress['pronunciationScore'] ?? 0.0;
  }

  // Método para actualizar el progreso de lectura
  ConversationModel updateReadProgress(
      String userId, int messageIndex, double pronunciationScore) {
    Map<String, dynamic> newReadBy = Map.from(readBy);

    if (!newReadBy.containsKey(userId)) {
      newReadBy[userId] = {
        'readCount': 0,
        'lastReadAt': Timestamp.now(),
        'pronunciationScore': 0.0,
        'readMessages': [],
      };
    }

    Map<String, dynamic> userProgress = Map.from(newReadBy[userId]);
    List<dynamic> readMessages = List.from(userProgress['readMessages'] ?? []);

    // Añadir el mensaje a la lista de leídos si no está ya
    if (!readMessages.contains(messageIndex)) {
      readMessages.add(messageIndex);
      userProgress['readCount'] = readMessages.length;
    }

    // Actualizar la puntuación de pronunciación (promedio)
    double currentScore = userProgress['pronunciationScore'] ?? 0.0;
    int readCount = userProgress['readCount'] ?? 0;

    if (pronunciationScore > 0) {
      // Calcular el nuevo promedio
      double newScore =
          ((currentScore * (readCount - 1)) + pronunciationScore) / readCount;
      userProgress['pronunciationScore'] = newScore;
    }

    userProgress['lastReadAt'] = Timestamp.now();
    userProgress['readMessages'] = readMessages;
    newReadBy[userId] = userProgress;

    return ConversationModel(
      id: id,
      title: title,
      category: category,
      createdAt: createdAt,
      createdBy: createdBy,
      messages: messages,
      readBy: newReadBy,
      difficulty: difficulty,
      level: level,
    );
  }

  // Método para crear una copia con cambios específicos
  ConversationModel copyWith({
    String? id,
    String? title,
    String? category,
    DateTime? createdAt,
    String? createdBy,
    List<MessageModel>? messages,
    Map<String, dynamic>? readBy,
    String? difficulty,
    String? level,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      messages: messages ?? this.messages,
      readBy: readBy ?? this.readBy,
      difficulty: difficulty ?? this.difficulty,
      level: level ?? this.level,
    );
  }

  // Método para actualizar la puntuación de pronunciación global
  ConversationModel updatePronunciationScore(String userId) {
    // Calcular la puntuación promedio de todos los mensajes
    if (messages.isEmpty || userId.isEmpty) return this;

    double totalScore = 0.0;
    int testedCount = 0;

    for (var message in messages) {
      double score = message.getTestScore(userId);
      if (score > 0) {
        totalScore += score;
        testedCount++;
      }
    }

    // Si no hay mensajes probados, mantener la puntuación actual
    if (testedCount == 0) return this;

    // Calcular el nuevo promedio
    double newScore = totalScore / testedCount;

    // Actualizar el readBy
    Map<String, dynamic> newReadBy = Map.from(readBy);
    if (!newReadBy.containsKey(userId)) {
      newReadBy[userId] = {
        'readCount': 0,
        'lastReadAt': Timestamp.now(),
        'pronunciationScore': 0.0,
        'readMessages': [],
      };
    }

    Map<String, dynamic> userProgress = Map.from(newReadBy[userId]);
    userProgress['pronunciationScore'] = newScore;
    newReadBy[userId] = userProgress;

    return copyWith(readBy: newReadBy);
  }
}
