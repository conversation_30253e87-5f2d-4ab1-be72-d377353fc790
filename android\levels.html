<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Summary Table of Groups and Points</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            vertical-align: middle;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .notes {
            white-space: pre-wrap;
            text-align: right;
        }
        .total-row {
            font-weight: bold;
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <h1>ملخص عدد المجموعات والنقاط لكل مستوى</h1>
    <table>
        <thead>
            <tr>
                <th>المستوى</th>
                <th>المجموعات</th>
                <th>عدد الجمل</th>
                <th>النشاط/المهمة</th>
                <th>عدد النقاط</th>
                <th>نوع النقاط</th>
                <th>ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            <!-- المستوى 1 -->
            <tr>
                <td rowspan="11">المستوى 1</td>
                <td rowspan="3">دفعة 1</td>
                <td rowspan="3">10 جمل</td>
                <td>قراءة جملة جديدة (دقة ≥80%)</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">تُمنح عند اجتياز اختبارات القراءة/النطق/الحفظ</td>
            </tr>
            <tr>
                <td>اختبار الحفظ (قراءة من الذاكرة)</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة بنسبة دقة ≥80%</td>
            </tr>
            <tr>
                <td>إكمال الدفعة (10 جمل)</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال الدفعة بالكامل</td>
            </tr>
            <tr>
                <td rowspan="3">محادثة 1</td>
                <td rowspan="3">10 جمل</td>
                <td>قراءة جملة في المحادثة (دقة ≥80%)</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">نفس آلية الدفعة</td>
            </tr>
            <tr>
                <td>اختبار الحفظ (قراءة من الذاكرة)</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة بنسبة دقة ≥80%</td>
            </tr>
            <tr>
                <td>إكمال المحادثة (10 جمل)</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال المحادثة</td>
            </tr>
            <tr>
                <td rowspan="3">دفعة 2</td>
                <td rowspan="3">10 جمل</td>
                <td>قراءة جملة جديدة (دقة ≥80%)</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">تُمنح عند اجتياز اختبارات القراءة/النطق/الحفظ</td>
            </tr>
            <tr>
                <td>اختبار الحفظ (قراءة من الذاكرة)</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة بنسبة دقة ≥80%</td>
            </tr>
            <tr>
                <td>إكمال الدفعة (10 جمل)</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال الدفعة بالكامل</td>
            </tr>
            <tr>
                <td>مراجعة</td>
                <td>30 جملة (من الدفعات/المحادثة)</td>
                <td>مراجعة جملة (نطق صحيح ≥80%)</td>
                <td>3</td>
                <td>تعليمية</td>
                <td class="notes">تُمنح لكل جملة يتم نطقها بشكل صحيح</td>
            </tr>
            <tr class="total-row">
                <td colspan="3">الإجمالي اليومي (المستوى 1)</td>
                <td>إجمالي النقاط التعليمية اليومية</td>
                <td>690</td>
                <td>تعليمية</td>
                <td class="notes">تفاصيل:<br> - دفعة 1: (10 جمل × 10) + (10 حفظ × 5) + 50 = 200<br> - محادثة: (10 جمل × 10) + (10 حفظ × 5) + 50 = 200<br> - دفعة 2: (10 جمل × 10) + (10 حفظ × 5) + 50 = 200<br> - مراجعة: 30 × 3 = 90</td>
            </tr>
            <tr>
                <td colspan="3">التقدم للمستوى التالي</td>
                <td>إكمال 210 جمل</td>
                <td>-</td>
                <td>تعليمية</td>
                <td class="notes">فتح المستوى 2 بعد إكمال 210 جملة</td>
            </tr>
            <!-- المستوى 2 -->
            <tr>
                <td rowspan="13">المستوى 2</td>
                <td rowspan="3">دفعة 1</td>
                <td rowspan="3">15 جملة</td>
                <td>قراءة جملة جديدة (دقة ≥80%)</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">زيادة عدد الجمل لزيادة التحدي</td>
            </tr>
            <tr>
                <td>اختبار الحفظ</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة</td>
            </tr>
            <tr>
                <td>إكمال الدفعة</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال الدفعة</td>
            </tr>
            <tr>
                <td rowspan="3">محادثة 1</td>
                <td rowspan="3">10 جمل</td>
                <td>قراءة جملة</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">كما في المستوى 1</td>
            </tr>
            <tr>
                <td>اختبار الحفظ</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة</td>
            </tr>
            <tr>
                <td>إكمال المحادثة</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال المحادثة</td>
            </tr>
            <tr>
                <td rowspan="3">محادثة 2</td>
                <td rowspan="3">10 جمل</td>
                <td>قراءة جملة</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">محادثة إضافية لزيادة التنوع</td>
            </tr>
            <tr>
                <td>اختبار الحفظ</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة</td>
            </tr>
            <tr>
                <td>إكمال المحادثة</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال المحادثة</td>
            </tr>
            <tr>
                <td rowspan="3">دفعة 2</td>
                <td rowspan="3">15 جملة</td>
                <td>قراءة جملة</td>
                <td>10</td>
                <td>تعليمية</td>
                <td class="notes">كما في دفعة 1</td>
            </tr>
            <tr>
                <td>اختبار الحفظ</td>
                <td>5</td>
                <td>تعليمية</td>
                <td class="notes">لكل جملة</td>
            </tr>
            <tr>
                <td>إكمال الدفعة</td>
                <td>50</td>
                <td>تعليمية</td>
                <td class="notes">مكافأة إكمال الدفعة</td>
            </tr>
            <tr>
                <td>مراجعة</td>
                <td>50 جملة</td>
                <td>مراجعة جملة</td>
                <td>3</td>
                <td>تعليمية</td>
                <td class="notes">تُمنح لكل جملة يتم نطقها بشكل صحيح</td>
            </tr>
            <tr class="total-row">
                <td colspan="3">الإجمالي اليومي (المستوى 2)</td>
                <td>إجمالي النقاط التعليمية اليومية</td>
                <td>1050</td>
                <td>تعليمية</td>
                <td class="notes">تفاصيل:<br> - دفعة 1: (15 × 10) + (15 × 5) + 50 = 275<br> - محادثة 1: (10 × 10) + (10 × 5) + 50 = 200<br> - محادثة 2: (10 × 10) + (10 × 5) + 50 = 200<br> - دفعة 2: (15 × 10) + (15 × 5) + 50 = 275<br> - مراجعة: 50 × 3 = 150</td>
            </tr>
            <tr>
                <td colspan="3">التقدم للمستوى التالي</td>
                <td>إكمال 350 جملة</td>
                <td>-</td>
                <td>تعليمية</td>
                <td class="notes">فتح المستوى 3 بعد إكمال 350 جملة</td>
            </tr>
            <!-- أنشطة المكافأة -->
            <tr>
                <td rowspan="5">أنشطة المكافأة (جميع المستويات)</td>
                <td colspan="2">تسجيل الدخول اليومي</td>
                <td>تسجيل دخول يومي</td>
                <td>20</td>
                <td>مكافأة</td>
                <td class="notes">يومي، مع مكافآت تسلسل:<br> - 3 أيام: 50<br> - 7 أيام: 100<br> - 14 يوم: 200</td>
            </tr>
            <tr>
                <td colspan="2">مشاهدة إعلان</td>
                <td>مشاهدة إعلان (بحد أقصى 5 يوميًا)</td>
                <td>50</td>
                <td>مكافأة</td>
                <td class="notes">تُستخدم لمحاولات إضافية أو شارات</td>
            </tr>
            <tr>
                <td colspan="2">مشاركة رابط إحالة</td>
                <td>مشاركة رابط (بحد أقصى 3 يوميًا)</td>
                <td>100</td>
                <td>مكافأة</td>
                <td class="notes">تشجيع التفاعل الاجتماعي</td>
            </tr>
            <tr>
                <td colspan="2">إحالة ناجحة</td>
                <td>مستخدم جديد يسجل عبر الرابط</td>
                <td>500</td>
                <td>مكافأة</td>
                <td class="notes">مكافأة كبيرة لزيادة الانتشار</td>
            </tr>
            <tr class="total-row">
                <td colspan="3">الإجمالي اليومي (نقاط المكافأة)</td>
                <td>إجمالي نقاط المكافأة اليومية</td>
                <td>1270</td>
                <td>مكافأة</td>
                <td class="notes">تفاصيل:<br> - تسجيل دخول: 20-200<br> - إعلانات: 5 × 50 = 250<br> - مشاركة: 3 × 100 = 300<br> - إحالة: 1 × 500 = 500</td>
            </tr>
        </tbody>
    </table>
</body>
</html>






توضيحات إضافية
عدد المجموعات:
المستوى 1:
دفعة 1 (10 جمل)، محادثة 1 (10 جمل)، دفعة 2 (10 جمل).
مراجعة يومية لـ30 جملة (من الدفعات والمحادثة).
إجمالي الجمل المطلوبة لفتح المستوى 2: 210 جمل (يتم تحقيقها عبر أيام متعددة).
المستوى 2 (افتراضي):
دفعة 1 (15 جملة)، محادثة 1 (10 جمل)، محادثة 2 (10 جمل)، دفعة 2 (15 جملة).
مراجعة يومية لـ50 جملة.
إجمالي الجمل المطلوبة لفتح المستوى 3: 350 جملة.
التحكم: يمكن للمدير تعديل عدد الجمل، المحادثات، والدفعات لكل مستوى عبر صفحة الإدارة في Firebase (كما وصفنا سابقًا).
أنواع النقاط:
نقاط تعليمية:
تُمنح للأنشطة التعليمية (قراءة جملة، اختبار حفظ، إكمال دفعة/محادثة، مراجعة).
تُحسب للتقدم في المستويات (مثل إكمال 210 جمل للمستوى 1).
تُستخدم لفتح مستويات جديدة ومنح شارات تعليمية.
نقاط مكافأة:
تُمنح للأنشطة غير التعليمية (تسجيل دخول، إعلانات، إحالات).
تُستخدم لمكافآت إضافية (مثل محاولات إضافية، شارات تحفيزية، تخصيص الملف الشخصي).
لا تُحسب للتقدم في المستويات.
إدارة النقاط عبر Firebase:
جميع القيم (عدد الجمل، نقاط لكل نشاط) تُخزن في Firestore (مجموعة levels).
المدير يمكنه تعديل هذه القيم عبر صفحة إدارية، وتُحدث التغييرات في Firestore.
عند تسجيل الدخول أو المزامنة، يتم تحميل الإعدادات الجديدة إلى التخزين المحلي  لتحديث التطبيق.
عرض النقاط:
في صفحة التقدم (Timeline):
النقاط التعليمية تُعرض في بطاقة الإحصائيات العامة (مثل "690 نقطة تعليمية") مع شريط تقدم.
نقاط المكافأة تُعرض بشكل منفصل (مثل "1270 نقطة مكافأة") مع أيقونة نجمة.
في صفحة الجمل/المحادثات:
كل بطاقة تعرض النقاط المكتسبة (مثل "10/10 نقاط" لجملة مكتملة).
تأثير العملات (ذهبية للتعليمية، فضية للمكافأة) يتحرك إلى الدائرة العائمة.
في ملف المتعلم:
تفاصيل النقاط مقسمة إلى تعليمية (مع مصادرها: جمل، اختبارات، مراجعة) ومكافأة (إعلانات، إحالات).
ملاحظات نهائية
المرونة: يمكن للمدير تغيير أي قيمة (عدد الجمل، النقاط) عبر Firebase، مما يتيح التحكم الكامل في هيكلية المستويات والمكافآت.
التحفيز: النقاط التعليمية تشجع التعلم، بينما نقاط المكافأة تحفز التفاعل المستمر والانتشار الاجتماعي.
التكامل: الجدول متوافق مع التصميم السابق (صفحة التقدم مع timeline_tile, تأثير العملات, التخزين المحلي والمزامنة).
التخصيص: إذا أردت تغيير عدد الجمل أو النقاط لمستوى معين، يمكنني إعادة تصميم الجدول بناءً على قيم جديدة.