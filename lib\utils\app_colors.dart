import 'package:flutter/material.dart';

/// ألوان التطبيق
class AppColors {
  // اللون الرئيسي
  static const Color primaryColor = Color(0xFF2196F3); // أزرق فاتح

  // لون ثانوي
  static const Color accentColor = Color(0xFFFF9800); // برتقالي

  // ألوان الخلفية
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Colors.white;

  // ألوان النص
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textLightColor = Color(0xFFBDBDBD);

  // ألوان الحالة
  static const Color successColor = Color(0xFF4CAF50); // أخضر
  static const Color errorColor = Color(0xFFF44336); // أحمر
  static const Color warningColor = Color(0xFFFFEB3B); // أصفر
  static const Color infoColor = Color(0xFF2196F3); // أزرق

  // ألوان المستويات
  static const Color level1Color = Color(0xFF2196F3); // أزرق فاتح
  static const Color level2Color = Color(0xFF4CAF50); // أخضر
  static const Color level3Color = Color(0xFFFF9800); // برتقالي
  static const Color level4Color = Color(0xFF9C27B0); // بنفسجي
  static const Color level5Color = Color(0xFFF44336); // أحمر

  // ألوان النقاط
  static const Color educationalPointsColor = Color(0xFF4CAF50); // أخضر
  static const Color rewardPointsColor = Color(0xFFFFD700); // ذهبي

  // ألوان أخرى
  static const Color dividerColor = Color(0xFFE0E0E0);
  static const Color shadowColor = Color(0x1A000000);

  // الحصول على لون المستوى بناءً على رقم المستوى
  static Color getLevelColor(int level) {
    switch (level) {
      case 1:
        return level1Color;
      case 2:
        return level2Color;
      case 3:
        return level3Color;
      case 4:
        return level4Color;
      case 5:
        return level5Color;
      default:
        return primaryColor;
    }
  }
}
