import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';
import '../models/sentence_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shimmer/shimmer.dart';
import '../widgets/network_aware_widget.dart';
import '../viewmodels/auth_view_model.dart';
import 'package:share_plus/share_plus.dart';
import 'profile_details_screen.dart';
import 'edit_profile_screen.dart';
import '../theme/app_theme.dart';
import '../widgets/app_card.dart';
import '../widgets/flashing_button.dart';
import '../services/connectivity_service.dart';
import '../widgets/sync_status_widget.dart';
import '../widgets/quiz_dialog.dart';
import 'speech_test_page.dart';
import 'favorites_screen.dart';
import 'test_cycles_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  // إنشاء PageController مرة واحدة فقط واستخدامه في كل مرة
  late PageController _pageController;

  // متغير للتحكم في عرض الإشعار
  bool _showProfileNotification = false;

  @override
  void initState() {
    super.initState();
    // إضافة مراقب دورة حياة التطبيق
    WidgetsBinding.instance.addObserver(this);

    // إنشاء PageController مع الإعدادات الافتراضية
    _pageController = PageController(
      viewportFraction: 0.9,
      keepPage: true,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
      final authViewModel = Provider.of<AuthViewModel>(context, listen: false);

      // إضافة مستمع للتغييرات في SentenceViewModel
      viewModel.addListener(_handleViewModelChanges);

      if (authViewModel.isAuthenticated) {
        try {
          // تحميل الجمل اليومية من التخزين المحلي أولاً
          if (viewModel.adapter != null) {
            debugPrint('محاولة تحميل الجمل اليومية من التخزين المحلي');

            // الحصول على جميع الجمل اليومية من التخزين المحلي (بما في ذلك المقروءة)
            final allDailySentences = viewModel.adapter!.getDailySentences();

            if (allDailySentences.isNotEmpty) {
              debugPrint(
                  'تم العثور على ${allDailySentences.length} جملة يومية في التخزين المحلي');

              // تعيين جميع الجمل اليومية في viewModel
              viewModel.setDailyRandomSentences(allDailySentences);

              // التحقق من وجود جمل غير مقروءة
              final unreadSentences = allDailySentences
                  .where((s) => !s.isReadByCurrentUser)
                  .toList();
              debugPrint('منها ${unreadSentences.length} جملة غير مقروءة');
            } else {
              debugPrint('لا توجد جمل يومية في التخزين المحلي، جلب جمل جديدة');
            }
          }

          // التحقق من اليوم الجديد وتحديث الجمل تلقائيًا إذا لزم الأمر
          await viewModel.checkForNewDayAndUpdate();

          // تحميل الجمل اليومية دون فرض التحديث
          await viewModel.loadRandomDailySentences();

          // تحديث عدد الجمل المعروضة والمقروءة مرة واحدة فقط عند بدء التطبيق
          await Future.delayed(const Duration(milliseconds: 500));
          await viewModel.loadTodayCountsFromStorage();

          // طباعة تصحيح للتأكد من تحميل البيانات
          debugPrint(
              'تم تحميل البيانات الأولية: قراءة=${viewModel.todayReadCount}, عرض=${viewModel.todayShownCount}');
        } catch (e) {
          debugPrint('خطأ في تحميل البيانات الأولية: $e');
        }
      }

      // التحقق من اكتمال معلومات الملف الشخصي
      // إضافة تأخير قصير للتأكد من تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));
      await _checkProfileCompletion();
    });
  }

  // دالة لمعالجة التغييرات في SentenceViewModel
  void _handleViewModelChanges() {
    // التحقق مما إذا كان الويدجت لا يزال مثبتًا
    if (!mounted) return;

    // تحديث واجهة المستخدم عند تغيير البيانات
    // لا نستدعي setState هنا لتجنب إعادة البناء المتكررة
    // لأن Consumer في _buildStatisticsSection سيقوم بإعادة البناء تلقائيًا
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // التحقق من حالة استئناف التطبيق
    if (state == AppLifecycleState.resumed) {
      debugPrint('App resumed, checking for new day...');
      // التحقق من اليوم الجديد وتحديث الجمل تلقائيًا إذا لزم الأمر
      if (mounted) {
        final viewModel =
            Provider.of<SentenceViewModel>(context, listen: false);
        viewModel.checkForNewDayAndUpdate();
      }
    }
  }

  // دالة للتحقق من اكتمال معلومات الملف الشخصي
  Future<void> _checkProfileCompletion() async {
    final authVM = Provider.of<AuthViewModel>(context, listen: false);
    if (authVM.isAuthenticated) {
      // تحديث بيانات المستخدم من Firebase أولاً
      await authVM.refreshUserData();

      // الآن نتحقق من البيانات المحدثة
      final user = authVM.user;
      final userModel = authVM.userModel;

      if (user == null) return;

      // التحقق من وجود اسم المستخدم وصورة الملف الشخصي
      final hasDisplayName =
          user.displayName != null && user.displayName!.isNotEmpty;
      final hasPhotoURL = user.photoURL != null || userModel?.photoURL != null;

      // التحقق من وجود معلومات إضافية في userModel
      final hasAdditionalInfo =
          userModel?.country != null && userModel!.country!.isNotEmpty;

      // طباعة معلومات التصحيح
      debugPrint('Profile Completion Check:');
      debugPrint('hasDisplayName: $hasDisplayName (${user.displayName})');
      debugPrint(
          'hasPhotoURL: $hasPhotoURL (${user.photoURL ?? userModel?.photoURL})');
      debugPrint(
          'hasAdditionalInfo: $hasAdditionalInfo (${userModel?.country})');

      // عرض الإشعار إذا كانت أي من المعلومات غير مكتملة
      if (!hasDisplayName || !hasPhotoURL || !hasAdditionalInfo) {
        if (mounted) {
          setState(() {
            _showProfileNotification = true;
          });
        }
      } else {
        // إذا كانت جميع المعلومات مكتملة، تأكد من إخفاء الإشعار
        if (mounted && _showProfileNotification) {
          setState(() {
            _showProfileNotification = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    // إزالة مراقب دورة حياة التطبيق
    WidgetsBinding.instance.removeObserver(this);
    // التخلص من PageController عند إغلاق الشاشة
    _pageController.dispose();
    // إزالة المستمع عند التخلص من الويدجت
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
    viewModel.removeListener(_handleViewModelChanges);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('الرئيسية'),
            // إضافة مؤشر حالة الاتصال بالإنترنت
            Consumer<ConnectivityService>(
              builder: (context, connectivityService, _) {
                if (!connectivityService.isOnline) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.cloud_off,
                            color: Colors.white,
                            size: 14,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'غير متصل',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          // زر المفضلة
          IconButton(
            icon: const Icon(Icons.favorite),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FavoritesScreen(),
                ),
              );
            },
            tooltip: 'المفضلة',
          ),

          // زر اختبار التعرف على الكلام
          IconButton(
            icon: const Icon(Icons.mic),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SpeechTestPage(),
                ),
              );
            },
            tooltip: 'اختبار الصوت',
          ),

          // زر اختبار الدورات
          IconButton(
            icon: const Icon(Icons.science),
            onPressed: () {
              // استخدام MaterialPageRoute بدلاً من pushNamed
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TestCyclesScreen(),
                ),
              );
            },
            tooltip: 'اختبار الدورات',
          ),

          // إضافة أيقونة حالة المزامنة
          const SyncStatusWidget(),

          Consumer<AuthViewModel>(
            builder: (context, authVM, _) {
              if (!authVM.isAuthenticated) {
                return IconButton(
                  icon: const Icon(Icons.person),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfileDetailsScreen(),
                      ),
                    );
                  },
                  tooltip: 'الملف الشخصي',
                );
              }

              // عرض صورة المستخدم إذا كان مسجل الدخول
              return Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfileDetailsScreen(),
                      ),
                    );
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: const Color(0xFF3DBBFF), // اللون الأزرق
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: authVM.user?.photoURL != null
                          ? authVM.user!.photoURL!.startsWith('assets/')
                              ? Image.asset(
                                  authVM.user!.photoURL!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const CircleAvatar(
                                      backgroundColor: Colors.grey,
                                      child: Icon(
                                        Icons.person,
                                        color: Colors.white,
                                      ),
                                    );
                                  },
                                )
                              : Image.network(
                                  authVM.user!.photoURL!,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const CircleAvatar(
                                      backgroundColor: Colors.grey,
                                      child: Icon(
                                        Icons.person,
                                        color: Colors.white,
                                      ),
                                    );
                                  },
                                )
                          : const CircleAvatar(
                              backgroundColor: Colors.grey,
                              child: Icon(
                                Icons.person,
                                color: Colors.white,
                              ),
                            ),
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Stack(
        children: [
          // المحتوى الرئيسي
          NetworkAwareWidget(
            onlineChild: _buildOnlineContent(),
            offlineChild: _buildOfflineContent(),
          ),

          // إشعار استكمال الملف الشخصي (يظهر فوق المحتوى)
          if (_showProfileNotification)
            Positioned(
              top: 8,
              left: 8,
              right: 8,
              child: _buildProfileNotification(),
            ),
        ],
      ),
    );
  }

  Widget _buildOnlineContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب ارتفاعات البطاقات بناءً على النسب المطلوبة
        final totalHeight = constraints.maxHeight;

        // تحديد ما إذا كانت الشاشة صغيرة
        final isSmallScreen = MediaQuery.of(context).size.height < 600;

        // تعديل النسب للشاشات الصغيرة مع مراعاة عدم تجاوز الحدود
        // تقليل النسب قليلاً لترك مساحة للهوامش
        final welcomeCardHeightRatio = isSmallScreen
            ? 0.14
            : 0.18; // 14% للشاشات الصغيرة، 18% للشاشات العادية
        final statsCardHeightRatio = isSmallScreen
            ? 0.14
            : 0.18; // 14% للشاشات الصغيرة، 18% للشاشات العادية

        // حساب المساحة المتبقية للجمل مع ترك هامش للمسافات بين العناصر
        final spacingRatio = isSmallScreen ? 0.04 : 0.06; // 4-6% للمسافات
        final sentencesCardHeightRatio =
            1.0 - welcomeCardHeightRatio - statsCardHeightRatio - spacingRatio;

        // حساب الارتفاعات الفعلية
        final welcomeCardHeight = totalHeight * welcomeCardHeightRatio;
        final sentencesCardHeight = totalHeight * sentencesCardHeightRatio;
        final statsCardHeight = totalHeight * statsCardHeightRatio;

        // تقليل المسافات بين العناصر في الشاشات الصغيرة
        final spacingHeight = isSmallScreen ? 4.0 : 8.0;

        return Column(
          mainAxisSize: MainAxisSize.min, // تقليل الحجم للحد الأدنى المطلوب
          children: [
            // بطاقة الترحيب - 14-18% من الارتفاع
            SizedBox(
              height: welcomeCardHeight,
              child: _buildWelcomeCard(),
            ),
            SizedBox(height: spacingHeight),
            // بطاقة الجمل اليومية - ~64-68% من الارتفاع
            SizedBox(
              height: sentencesCardHeight,
              child: Consumer<SentenceViewModel>(
                builder: (context, viewModel, child) {
                  if (viewModel.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  final sentences = viewModel.dailyRandomSentences;

                  // عرض رسالة مختلفة اعتمادًا على سبب عدم وجود جمل
                  if (sentences.isEmpty) {
                    // إذا كان المستخدم بحاجة إلى تحديث يدوي (قرأ كل الجمل)
                    if (viewModel.needsManualRefresh) {
                      return Column(
                        children: [
                          // عنوان وزر التحديث - نبقيه ظاهرًا
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 8.0 : 16.0,
                              vertical: isSmallScreen ? 4.0 : 8.0,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'جمل اليوم',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Row(
                                  children: [
                                    // زر جمل جديدة تمامًا
                                    ElevatedButton.icon(
                                      icon: Icon(
                                        Icons.refresh,
                                        size: isSmallScreen ? 14.0 : 20.0,
                                      ),
                                      label: Text(
                                        'جمل جديدة',
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 10.0 : 12.0,
                                        ),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.orange,
                                        foregroundColor: Colors.white,
                                        padding: isSmallScreen
                                            ? const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 3)
                                            : const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                      ),
                                      onPressed: () async {
                                        // تحديث الجمل بشكل قسري مع تجاهل الجمل الحالية
                                        final hasError = await viewModel
                                            .forceRefreshWithNewSentences();

                                        if (hasError) {
                                          // استخدام دالة مساعدة لعرض الإشعار
                                          _showSnackBarAsync(
                                              viewModel.errorMessage);
                                        } else {
                                          _showSnackBarAsync(
                                            'تم تحميل جمل جديدة بنجاح',
                                            backgroundColor: Colors.green,
                                          );
                                        }
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    // Restored 10 Again button
                                    FlashingButton(
                                      icon: Icons.repeat_rounded,
                                      label: '10 again',
                                      isSmallScreen: isSmallScreen,
                                      onPressed: () {
                                        // تحديث الجمل بدون إظهار مؤشر التحميل
                                        viewModel.forceRefreshRandomSentences();
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          // رسالة إتمام القراءة
                          const Expanded(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 64,
                                    color: Colors.green,
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    'لقد قرأت جميع الجمل اليومية',
                                    style: TextStyle(fontSize: 18),
                                    textAlign: TextAlign.center,
                                  ),
                                  Text(
                                    'اضغط على زر "10 again" للحصول على جمل جديدة أو "جمل جديدة" لتحديث كامل',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.grey),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    }

                    // إذا لم تكن هناك جمل متاحة على الإطلاق
                    if (viewModel.errorMessage
                        .contains('لا توجد جمل جديدة للقراءة')) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.sentiment_satisfied_alt,
                              size: 64,
                              color: Colors.amber,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'أحسنت! لقد قرأت جميع الجمل المتاحة',
                              style: TextStyle(fontSize: 18),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'سيتم إضافة المزيد من الجمل قريبًا',
                              style:
                                  TextStyle(fontSize: 14, color: Colors.grey),
                            ),
                          ],
                        ),
                      );
                    }

                    // الحالة الافتراضية - لا توجد جمل لليوم
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.menu_book_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد جمل لليوم',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return Column(
                    children: [
                      // عنوان وزر التحديث
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 8.0 : 16.0,
                          vertical: isSmallScreen ? 4.0 : 8.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'جمل اليوم',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                // تحقق مما إذا كانت جميع الجمل مقروءة
                                Builder(
                                  builder: (context) {
                                    // تحقق مما إذا كانت جميع الجمل مقروءة
                                    final allSentencesRead =
                                        sentences.isEmpty ||
                                            sentences.every(
                                                (s) => s.isReadByCurrentUser);

                                    // إذا كانت جميع الجمل مقروءة، نعرض زر وامض
                                    if (allSentencesRead) {
                                      return Row(
                                        children: [
                                          // زر جمل جديدة تمامًا
                                          ElevatedButton.icon(
                                            icon: Icon(
                                              Icons.refresh,
                                              size: isSmallScreen ? 14.0 : 20.0,
                                            ),
                                            label: Text(
                                              'جمل جديدة',
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 10.0 : 12.0,
                                              ),
                                            ),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                              padding: isSmallScreen
                                                  ? const EdgeInsets.symmetric(
                                                      horizontal: 6,
                                                      vertical: 3)
                                                  : const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                            onPressed: () async {
                                              // تحديث الجمل بشكل قسري مع تجاهل الجمل الحالية
                                              final hasError = await viewModel
                                                  .forceRefreshWithNewSentences();

                                              if (hasError) {
                                                // استخدام دالة مساعدة لعرض الإشعار
                                                _showSnackBarAsync(
                                                    viewModel.errorMessage);
                                              } else {
                                                _showSnackBarAsync(
                                                  'تم تحميل جمل جديدة بنجاح',
                                                  backgroundColor: Colors.green,
                                                );
                                              }
                                            },
                                          ),
                                          const SizedBox(width: 8),
                                          // زر 10 Again
                                          FlashingButton(
                                            icon: Icons.repeat_rounded,
                                            label: '10 again',
                                            isSmallScreen: isSmallScreen,
                                            onPressed: () async {
                                              // تحقق مما إذا كانت هناك جمل غير مقروءة
                                              final unreadSentences = viewModel
                                                  .dailyRandomSentences
                                                  .where((s) =>
                                                      !s.isReadByCurrentUser)
                                                  .toList();

                                              if (unreadSentences.isNotEmpty) {
                                                // إذا كانت هناك جمل غير مقروءة، عرض إشعار برتقالي
                                                _showSnackBarAsync(
                                                    'يرجى إكمال قراءة الجمل الحالية أولاً');
                                                debugPrint(
                                                    'Showing SnackBar: يرجى إكمال قراءة الجمل الحالية أولاً');
                                                return;
                                              }

                                              // تحديث الجمل بدون إظهار مؤشر التحميل
                                              final hasError = await viewModel
                                                  .forceRefreshRandomSentences();

                                              if (hasError) {
                                                // استخدام دالة مساعدة لعرض الإشعار
                                                _showSnackBarAsync(
                                                    viewModel.errorMessage);
                                              }
                                            },
                                          ),
                                        ],
                                      );
                                    } else {
                                      // إذا كانت هناك جمل غير مقروءة، نعرض زر عادي
                                      return Row(
                                        children: [
                                          // زر جمل جديدة تمامًا
                                          ElevatedButton.icon(
                                            icon: Icon(
                                              Icons.refresh,
                                              size: isSmallScreen ? 14.0 : 20.0,
                                            ),
                                            label: Text(
                                              'جمل جديدة',
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 10.0 : 12.0,
                                              ),
                                            ),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange,
                                              foregroundColor: Colors.white,
                                              padding: isSmallScreen
                                                  ? const EdgeInsets.symmetric(
                                                      horizontal: 6,
                                                      vertical: 3)
                                                  : const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                            ),
                                            onPressed: () async {
                                              // تحديث الجمل بشكل قسري مع تجاهل الجمل الحالية
                                              final hasError = await viewModel
                                                  .forceRefreshWithNewSentences();

                                              if (hasError) {
                                                // استخدام دالة مساعدة لعرض الإشعار
                                                _showSnackBarAsync(
                                                    viewModel.errorMessage);
                                              } else {
                                                _showSnackBarAsync(
                                                  'تم تحميل جمل جديدة بنجاح',
                                                  backgroundColor: Colors.green,
                                                );
                                              }
                                            },
                                          ),
                                          const SizedBox(width: 8),
                                          // زر 10 Again
                                          ElevatedButton.icon(
                                            icon: Icon(
                                              Icons.repeat_rounded,
                                              size: isSmallScreen ? 16.0 : 24.0,
                                            ),
                                            label: Text(
                                              '10 again',
                                              style: TextStyle(
                                                fontSize:
                                                    isSmallScreen ? 12.0 : 14.0,
                                              ),
                                            ),
                                            style: ElevatedButton.styleFrom(
                                              padding: isSmallScreen
                                                  ? const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4)
                                                  : null,
                                            ),
                                            onPressed: () async {
                                              // تحقق مما إذا كانت هناك جمل غير مقروءة
                                              final unreadSentences = viewModel
                                                  .dailyRandomSentences
                                                  .where((s) =>
                                                      !s.isReadByCurrentUser)
                                                  .toList();

                                              if (unreadSentences.isNotEmpty) {
                                                // إذا كانت هناك جمل غير مقروءة، عرض إشعار برتقالي
                                                _showSnackBarAsync(
                                                    'يرجى إكمال قراءة الجمل الحالية أولاً');
                                                debugPrint(
                                                    'Showing SnackBar: يرجى إكمال قراءة الجمل الحالية أولاً');
                                                return;
                                              }

                                              // تحديث الجمل بدون إظهار مؤشر التحميل
                                              final hasError = await viewModel
                                                  .forceRefreshRandomSentences();

                                              if (hasError) {
                                                // استخدام دالة مساعدة لعرض الإشعار
                                                _showSnackBarAsync(
                                                    viewModel.errorMessage);
                                              }
                                            },
                                          ),
                                        ],
                                      );
                                    }
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // بطاقات الجمل
                      Expanded(
                        child: Consumer<AuthViewModel>(
                          builder: (context, authVM, _) {
                            // تصفية الجمل المقروءة بالفعل
                            final unreadSentences = sentences
                                .where(
                                    (sentence) => !sentence.isReadByCurrentUser)
                                .toList();

                            // طباعة معلومات تصحيح
                            debugPrint(
                                'إجمالي الجمل: ${sentences.length}, غير مقروءة: ${unreadSentences.length}');

                            if (unreadSentences.isEmpty ||
                                viewModel.needsManualRefresh) {
                              // عرض إشعار أخضر عند قراءة جميع الجمل
                              _showSnackBarAsync(
                                'أحسنت! لقد أكملت قراءة جميع الجمل اليومية',
                                backgroundColor: Colors.green,
                              );

                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      size: isSmallScreen ? 48.0 : 64.0,
                                      color: Colors.green,
                                    ),
                                    SizedBox(
                                        height: isSmallScreen ? 8.0 : 16.0),
                                    const Text(
                                      'لقد قرأت جميع الجمل اليومية',
                                      style: TextStyle(fontSize: 18),
                                      textAlign: TextAlign.center,
                                    ),
                                    const Text(
                                      'اضغط على زر "10 again" للحصول على جمل جديدة أو "جمل جديدة" لتحديث كامل',
                                      style: TextStyle(
                                          fontSize: 14, color: Colors.grey),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              );
                            }

                            // الشكل الجديد - عرض أفقي مع تأثيرات حركية

                            // استخدام PageController الذي أنشأناه مسبقًا
                            // لا يمكن تعديل viewportFraction لأنها final
                            // لذلك نستخدم PageController كما هو

                            // استخدام Key لإجبار PageView على إعادة البناء عند تغيير البيانات
                            return PageView.builder(
                              key: ValueKey<int>(unreadSentences.length),
                              controller: _pageController,
                              itemCount: unreadSentences.length,
                              scrollDirection: Axis.horizontal,
                              padEnds: true,
                              physics: const BouncingScrollPhysics(),
                              onPageChanged: (page) {
                                // تسجيل الصفحة الحالية في سجل التصحيح
                                debugPrint('Page changed to: $page');
                              },
                              itemBuilder: (context, index) {
                                return AnimatedSentenceCard(
                                  key: ValueKey<String>(
                                      unreadSentences[index].id),
                                  sentence: unreadSentences[index],
                                  index: index,
                                  pageController: _pageController,
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            SizedBox(height: spacingHeight),
            // بطاقة الإحصائيات - 14-18% من الارتفاع
            SizedBox(
              height: statsCardHeight,
              child: _buildStatisticsSection(),
            ),
            // إضافة مساحة صغيرة في الأسفل لمنع التجاوز
            const SizedBox(height: 2.0),
          ],
        );
      },
    );
  }

  // إشعار استكمال الملف الشخصي
  Widget _buildProfileNotification() {
    return Container(
      margin: const EdgeInsets.all(4.0),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade100, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(180),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.person_outline, color: Colors.purple),
          ),
          const SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'أكمل ملفك الشخصي',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.0,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2.0),
                const Text(
                  'قم بإضافة معلوماتك الشخصية وصورة ملفك الشخصي',
                  style: TextStyle(fontSize: 12.0, color: Colors.black54),
                ),
                const SizedBox(height: 4.0),
                GestureDetector(
                  onTap: () {
                    // توجيه المستخدم مباشرة إلى صفحة تعديل البيانات
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EditProfileScreen(),
                      ),
                    ).then((_) async {
                      // التحقق من اكتمال الملف الشخصي بعد العودة من صفحة التعديل
                      // إضافة تأخير قصير للتأكد من تحديث البيانات
                      await Future.delayed(const Duration(milliseconds: 500));
                      await _checkProfileCompletion();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade600,
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: const Text(
                      'تعديل الملف الشخصي',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 11.0,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(15),
              onTap: () {
                setState(() {
                  _showProfileNotification = false;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(4.0),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(180),
                  shape: BoxShape.circle,
                ),
                child:
                    const Icon(Icons.close, size: 16.0, color: Colors.black54),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // حساب ارتفاعات البطاقات بناءً على النسب المطلوبة
        final totalHeight = constraints.maxHeight;

        // تحديد ما إذا كانت الشاشة صغيرة
        final isSmallScreen = MediaQuery.of(context).size.height < 600;

        // تعديل النسب للشاشات الصغيرة مع مراعاة عدم تجاوز الحدود
        final welcomeCardHeightRatio = isSmallScreen ? 0.14 : 0.18;
        final statsCardHeightRatio = isSmallScreen ? 0.14 : 0.18;
        final spacingRatio = isSmallScreen ? 0.04 : 0.06;
        final sentencesCardHeightRatio =
            1.0 - welcomeCardHeightRatio - statsCardHeightRatio - spacingRatio;

        // حساب الارتفاعات الفعلية
        final welcomeCardHeight = totalHeight * welcomeCardHeightRatio;
        final sentencesCardHeight = totalHeight * sentencesCardHeightRatio;
        final statsCardHeight = totalHeight * statsCardHeightRatio;
        final spacingHeight = isSmallScreen ? 4.0 : 8.0;

        return Stack(
          children: [
            // المحتوى الرئيسي - نفس محتوى الوضع المتصل
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // بطاقة الترحيب
                SizedBox(
                  height: welcomeCardHeight,
                  child: _buildWelcomeCard(),
                ),
                SizedBox(height: spacingHeight),
                // بطاقة الجمل اليومية
                SizedBox(
                  height: sentencesCardHeight,
                  child: Consumer<SentenceViewModel>(
                    builder: (context, viewModel, child) {
                      final sentences = viewModel.dailyRandomSentences;

                      // عرض شريط العنوان وزر "10 Again" دائمًا
                      return Column(
                        children: [
                          // عنوان وزر التحديث - نبقيه ظاهرًا دائمًا
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 8.0 : 16.0,
                              vertical: isSmallScreen ? 4.0 : 8.0,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'جمل اليوم',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Row(
                                  children: [
                                    // تحقق مما إذا كانت جميع الجمل مقروءة
                                    Builder(
                                      builder: (context) {
                                        // لا نحتاج للتحقق من حالة القراءة هنا لأننا سنعرض الزر دائمًا

                                        // دائمًا نعرض زر 10 Again حتى في وضع عدم الاتصال
                                        return FlashingButton(
                                          icon: Icons.repeat_rounded,
                                          label: '10 again',
                                          isSmallScreen: isSmallScreen,
                                          onPressed: () async {
                                            // تحقق مما إذا كانت هناك جمل غير مقروءة
                                            final unreadSentences = sentences
                                                .where((s) =>
                                                    !s.isReadByCurrentUser)
                                                .toList();

                                            if (unreadSentences.isNotEmpty) {
                                              // إذا كانت هناك جمل غير مقروءة، عرض إشعار برتقالي
                                              _showSnackBarAsync(
                                                  'يرجى إكمال قراءة الجمل الحالية أولاً');
                                              debugPrint(
                                                  'Showing SnackBar: يرجى إكمال قراءة الجمل الحالية أولاً');
                                              return;
                                            }

                                            // في وضع عدم الاتصال، نعيد استخدام الجمل المحلية
                                            final sentenceViewModel =
                                                Provider.of<SentenceViewModel>(
                                                    context,
                                                    listen: false);
                                            final hasError = await sentenceViewModel
                                                .forceRefreshWithNewSentences();

                                            if (hasError) {
                                              // استخدام دالة مساعدة لعرض الإشعار
                                              _showSnackBarAsync(
                                                  sentenceViewModel
                                                      .errorMessage);
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // محتوى الجمل
                          Expanded(
                            child: Builder(
                              builder: (context) {
                                if (sentences.isEmpty) {
                                  return Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.check_circle,
                                          size: isSmallScreen ? 48.0 : 64.0,
                                          color: Colors.green,
                                        ),
                                        SizedBox(
                                            height: isSmallScreen ? 8.0 : 16.0),
                                        const Text(
                                          'لقد قرأت جميع الجمل اليومية',
                                          style: TextStyle(fontSize: 18),
                                          textAlign: TextAlign.center,
                                        ),
                                        const Text(
                                          'سيتم تحميل جمل جديدة تلقائيًا عند عودة الاتصال',
                                          style: TextStyle(
                                              fontSize: 14, color: Colors.grey),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  );
                                }

                                // تصفية الجمل المقروءة بالفعل
                                final unreadSentences = sentences
                                    .where((sentence) =>
                                        !sentence.isReadByCurrentUser)
                                    .toList();

                                if (unreadSentences.isEmpty) {
                                  // عرض إشعار أخضر عند قراءة جميع الجمل
                                  _showSnackBarAsync(
                                    'أحسنت! لقد أكملت قراءة جميع الجمل اليومية',
                                    backgroundColor: Colors.green,
                                  );

                                  return Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.check_circle,
                                          size: isSmallScreen ? 48.0 : 64.0,
                                          color: Colors.green,
                                        ),
                                        SizedBox(
                                            height: isSmallScreen ? 8.0 : 16.0),
                                        const Text(
                                          'لقد قرأت جميع الجمل اليومية',
                                          style: TextStyle(fontSize: 18),
                                          textAlign: TextAlign.center,
                                        ),
                                        const Text(
                                          'سيتم تحميل جمل جديدة تلقائيًا عند عودة الاتصال',
                                          style: TextStyle(
                                              fontSize: 14, color: Colors.grey),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  );
                                }

                                // عرض البطاقات بنفس الطريقة
                                return PageView.builder(
                                  key: ValueKey<int>(unreadSentences.length),
                                  controller: _pageController,
                                  itemCount: unreadSentences.length,
                                  scrollDirection: Axis.horizontal,
                                  padEnds: true,
                                  physics: const BouncingScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    return AnimatedSentenceCard(
                                      key: ValueKey<String>(
                                          unreadSentences[index].id),
                                      sentence: unreadSentences[index],
                                      index: index,
                                      pageController: _pageController,
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
                SizedBox(height: spacingHeight),
                // بطاقة الإحصائيات
                SizedBox(
                  height: statsCardHeight,
                  child: _buildStatisticsSection(),
                ),
                const SizedBox(height: 2.0),
              ],
            ),

            // تم نقل إشعار عدم الاتصال بالإنترنت إلى الشريط العلوي
          ],
        );
      },
    );
  }

  Widget _buildStatisticsSection() {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        // استخدام Consumer لمراقبة تغييرات SentenceViewModel
        return Consumer<SentenceViewModel>(
          builder: (context, sentenceViewModel, _) {
            // لا نقوم بتحديث العدادات في كل مرة يتم فيها بناء الواجهة
            // لأن ذلك يؤدي إلى استدعاءات متكررة وبطء في التطبيق

            // إنشاء Map للإحصائيات
            final stats = <String, dynamic>{
              'todayReadCount': sentenceViewModel.todayReadCount,
              'todayShownCount': sentenceViewModel.todayShownCount,
            };

            // التحقق من وجود بيانات
            if (sentenceViewModel.isLoading) {
              return _buildShimmerStats();
            }

            return _buildStatsCard(stats);
          },
        );
      },
    );
  }

  Widget _buildShimmerStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[800]!
            : Colors.grey[300]!,
        highlightColor: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[700]!
            : Colors.grey[100]!,
        child: Column(
          children: [
            Container(
              height: 20,
              color: Colors.white,
            ),
            const SizedBox(height: 8),
            Container(
              height: 20,
              color: Colors.white,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(Map<String, dynamic> stats) {
    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // الحصول على عدد الجمل المقروءة اليوم
    final todayReadCount = stats['todayReadCount'] ?? 0;
    // الحصول على عدد الجمل التي ظهرت اليوم - يتم تحديثه تلقائيًا عند بداية يوم جديد إذا كانت القائمة فارغة
    final todayShownCount =
        stats['todayShownCount'] ?? 10; // افتراضي 10 إذا لم يكن متوفر

    // حساب النسبة المئوية بناءً على عدد الجمل المقروءة بالنسبة لعدد الجمل التي ظهرت اليوم
    int progressPercentage = 0;
    if (todayShownCount > 0) {
      progressPercentage = ((todayReadCount / todayShownCount) * 100).toInt();
    }

    return AppCard(
      margin: EdgeInsets.symmetric(
          horizontal: isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 16.0),
          vertical: isVerySmallScreen ? 2.0 : (isSmallScreen ? 4.0 : 8.0)),
      padding: EdgeInsets.symmetric(
          horizontal: isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 16.0),
          vertical: isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 12.0)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // قراءة اليوم
              _buildStatItem('قراءة اليوم', todayReadCount, isSmallScreen,
                  isVerySmallScreen),

              // ظهرت اليوم
              _buildStatItem('ظهرت اليوم', todayShownCount, isSmallScreen,
                  isVerySmallScreen),
            ],
          ),
          SizedBox(
              height: isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 12.0)),
          // شريط التقدم
          Row(
            children: [
              Text(
                'نسبة التقدم',
                style: TextStyle(
                  fontSize:
                      isVerySmallScreen ? 10.0 : (isSmallScreen ? 12.0 : 14.0),
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(
                  width: isVerySmallScreen ? 2.0 : (isSmallScreen ? 4.0 : 8.0)),
              Expanded(
                child: LinearProgressIndicator(
                  value: progressPercentage / 100,
                  minHeight:
                      isVerySmallScreen ? 3.0 : (isSmallScreen ? 4.0 : 6.0),
                  backgroundColor: Colors.grey[200],
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                  borderRadius: BorderRadius.circular(
                      isVerySmallScreen ? 1.5 : (isSmallScreen ? 2.0 : 3.0)),
                ),
              ),
              SizedBox(
                  width: isVerySmallScreen ? 2.0 : (isSmallScreen ? 4.0 : 8.0)),
              Text(
                '$progressPercentage%',
                style: TextStyle(
                  fontSize:
                      isVerySmallScreen ? 10.0 : (isSmallScreen ? 12.0 : 14.0),
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int value,
      [bool isSmallScreen = false, bool isVerySmallScreen = false]) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: isVerySmallScreen ? 14.0 : (isSmallScreen ? 18.0 : 24.0),
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: isVerySmallScreen ? 8.0 : (isSmallScreen ? 10.0 : 12.0),
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // دالة للحصول على صورة البروفايل
  DecorationImage? _getProfileImage(String? photoURL) {
    if (photoURL == null || photoURL.isEmpty) return null;

    if (photoURL.startsWith('assets/')) {
      return DecorationImage(
        image: AssetImage(photoURL),
        fit: BoxFit.cover,
      );
    } else if (photoURL.startsWith('http')) {
      // التأكد من أن الرابط يبدأ بـ http أو https
      return DecorationImage(
        image: NetworkImage(photoURL),
        fit: BoxFit.cover,
      );
    } else if (photoURL == 'default-avatar.png') {
      // استخدام صورة افتراضية من الأصول
      return const DecorationImage(
        image: AssetImage('assets/images/default_avatar.png'),
        fit: BoxFit.cover,
      );
    }

    // إذا لم يكن أي من الحالات السابقة، نعيد null
    return null;
  }

  // دالة مساعدة لعرض الإشعارات بطريقة آمنة عبر فجوات غير متزامنة
  void _showSnackBarAsync(String message,
      {Color backgroundColor = Colors.orange}) {
    // استخدام WidgetsBinding.instance.addPostFrameCallback لضمان تنفيذ الكود بعد بناء الإطار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // إلغاء أي إشعارات سابقة
        final messenger = ScaffoldMessenger.of(context);
        messenger.clearSnackBars();

        // عرض الإشعار الجديد
        messenger.showSnackBar(
          SnackBar(
            content: Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 8,
          ),
        );

        // طباعة رسالة تصحيح للتأكد من تنفيذ الكود
        debugPrint('Showing SnackBar: $message');
      }
    });
  }

  Widget _buildWelcomeCard() {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final user = snapshot.data!;
        // استخدام listen: true لتحديث الواجهة عند تغيير بيانات المستخدم
        final authVM = Provider.of<AuthViewModel>(context);

        // تحديد ما إذا كانت الشاشة صغيرة
        final isSmallScreen = MediaQuery.of(context).size.height < 600;

        // تحديد رسالة الترحيب حسب الوقت
        final hour = DateTime.now().hour;
        String greeting;

        if (hour < 12) {
          greeting = 'صباح الخير';
        } else if (hour < 17) {
          greeting = 'مساء الخير';
        } else {
          greeting = 'مساء الخير';
        }

        // الحصول على إجمالي عدد الجمل - استخدام StreamBuilder بدلاً من FutureBuilder للتحديث المباشر
        return StreamBuilder<DocumentSnapshot>(
          stream: FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('stats')
              .doc('total')
              .snapshots(),
          builder: (context, statsSnapshot) {
            int readCount = 0;
            if (statsSnapshot.hasData && statsSnapshot.data!.exists) {
              final data = statsSnapshot.data!.data() as Map<String, dynamic>?;
              readCount = data?['readCount'] ?? 0;
            }

            // تحديد ما إذا كانت الشاشة صغيرة جدًا
            final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

            return AppCard(
              margin: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8.0 : 16.0,
                  vertical:
                      isSmallScreen ? 2.0 : 4.0), // تقليل الهوامش العمودية
              padding: EdgeInsets.all(
                  isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 16.0)),
              child: Row(
                mainAxisSize:
                    MainAxisSize.min, // تقليل العرض للحد الأدنى المطلوب
                children: [
                  // صورة البروفيل
                  Container(
                    width: isVerySmallScreen
                        ? 32.0
                        : (isSmallScreen ? 40.0 : 60.0),
                    height: isVerySmallScreen
                        ? 32.0
                        : (isSmallScreen ? 40.0 : 60.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.primaryColor.withAlpha(30),
                      image: _getProfileImage(authVM.userModel?.photoURL),
                    ),
                    child: authVM.userModel?.photoURL == null
                        ? Icon(Icons.person,
                            size: isVerySmallScreen
                                ? 16.0
                                : (isSmallScreen ? 20.0 : 30.0),
                            color: AppTheme.primaryColor)
                        : null,
                  ),
                  SizedBox(
                      width: isVerySmallScreen
                          ? 4.0
                          : (isSmallScreen ? 8.0 : 16.0)),
                  // معلومات الترحيب
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          greeting,
                          style: TextStyle(
                            fontSize: isVerySmallScreen
                                ? 10.0
                                : (isSmallScreen ? 12.0 : 16.0),
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        SizedBox(
                            height: isVerySmallScreen
                                ? 1.0
                                : (isSmallScreen ? 2.0 : 4.0)),
                        Text(
                          authVM.userModel?.displayName ??
                              user.displayName ??
                              'المستخدم',
                          style: TextStyle(
                            fontSize: isVerySmallScreen
                                ? 14.0
                                : (isSmallScreen ? 16.0 : 20.0),
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(
                            height: isVerySmallScreen
                                ? 1.0
                                : (isSmallScreen ? 2.0 : 4.0)),
                        Text(
                          'جاهز للتعلم؟',
                          style: TextStyle(
                            fontSize: isVerySmallScreen
                                ? 10.0
                                : (isSmallScreen ? 12.0 : 14.0),
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // عدد الجمل الإجمالي
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: isVerySmallScreen
                            ? 6.0
                            : (isSmallScreen ? 8.0 : 16.0),
                        vertical: isVerySmallScreen
                            ? 2.0
                            : (isSmallScreen ? 4.0 : 8.0)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(isVerySmallScreen
                          ? 10.0
                          : (isSmallScreen ? 12.0 : 16.0)),
                      color: const Color(0xFF22C55E), // اللون الأخضر
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '$readCount',
                          style: TextStyle(
                            fontSize: isVerySmallScreen
                                ? 16.0
                                : (isSmallScreen ? 20.0 : 28.0),
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'مقروءة',
                          style: TextStyle(
                            fontSize: isVerySmallScreen
                                ? 8.0
                                : (isSmallScreen ? 10.0 : 14.0),
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

// الشكل القديم للبطاقة
class DailySentenceCard extends StatefulWidget {
  final SentenceModel sentence;

  const DailySentenceCard({
    super.key,
    required this.sentence,
  });

  @override
  State<DailySentenceCard> createState() => _DailySentenceCardState();
}

class _DailySentenceCardState extends State<DailySentenceCard> {
  bool _showTranslation = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              widget.sentence.englishText,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (_showTranslation) ...[
              const Divider(),
              Text(
                widget.sentence.arabicText,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .primaryColor
                        .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'الفئة: ${widget.sentence.category}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showTranslation = !_showTranslation;
                    });
                  },
                  child: Text(
                      _showTranslation ? 'إخفاء الترجمة' : 'إظهار الترجمة'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    // زر المفضلة
                    IconButton(
                      icon: Icon(
                        widget.sentence.isFavoriteByCurrentUser
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: widget.sentence.isFavoriteByCurrentUser
                            ? Colors.red
                            : null,
                      ),
                      onPressed: () {
                        final viewModel = Provider.of<SentenceViewModel>(
                            context,
                            listen: false);
                        viewModel.toggleFavorite(widget.sentence);
                      },
                    ),
                    // زر المشاركة
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () {
                        Share.share(
                            '${widget.sentence.arabicText}\n\n${widget.sentence.englishText}');
                      },
                    ),
                  ],
                ),
                // زر تعليم كمقروءة
                Consumer<AuthViewModel>(
                  builder: (context, authVM, _) {
                    final isRead = widget.sentence.isReadByCurrentUser;
                    return TextButton.icon(
                      icon: Icon(
                        isRead
                            ? Icons.check_circle
                            : Icons.check_circle_outline,
                        color: isRead ? Colors.green : null,
                      ),
                      label: Text(isRead ? 'تمت القراءة' : 'تعليم كمقروءة'),
                      onPressed: () {
                        if (!isRead && authVM.isAuthenticated) {
                          final viewModel = Provider.of<SentenceViewModel>(
                              context,
                              listen: false);
                          viewModel.markAsRead(widget.sentence);
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// الشكل الجديد للبطاقة مع تأثيرات حركية
class AnimatedSentenceCard extends StatefulWidget {
  final SentenceModel sentence;
  final int index;
  final PageController pageController;

  const AnimatedSentenceCard({
    super.key,
    required this.sentence,
    required this.index,
    required this.pageController,
  });

  @override
  State<AnimatedSentenceCard> createState() => _AnimatedSentenceCardState();
}

class _AnimatedSentenceCardState extends State<AnimatedSentenceCard>
    with SingleTickerProviderStateMixin {
  bool _showTranslation = false;
  bool _isMarkedAsRead = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  // دالة مساعدة لعرض نص مستوى الصعوبة
  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  // دالة مساعدة للحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // دالة مساعدة للحصول على أيقونة مستوى الصعوبة
  IconData _getDifficultyIcon(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Icons.sentiment_satisfied_alt;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  @override
  void initState() {
    super.initState();

    // إعداد التأثيرات الحركية
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    // تأخير بدء التأثير الحركي حسب ترتيب البطاقة
    Future.delayed(Duration(milliseconds: 100 * widget.index), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Card(
              margin: EdgeInsets.symmetric(
                  vertical: isSmallScreen ? 8.0 : 16.0,
                  horizontal: isSmallScreen ? 4.0 : 8.0),
              elevation: 4,
              color: (_isMarkedAsRead || widget.sentence.isReadByCurrentUser)
                  ? Theme.of(context).brightness == Brightness.dark
                      ? const Color(0xFF1A5D3A) // أخضر داكن للوضع المظلم
                      : const Color(0xFFE6F9EF) // أخضر فاتح للوضع العادي
                  : null,
              shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(isSmallScreen ? 12.0 : 16.0),
                side: (_isMarkedAsRead || widget.sentence.isReadByCurrentUser)
                    ? const BorderSide(color: Color(0xFF22C55E), width: 2)
                    : BorderSide.none,
              ),
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // الجزء العلوي القابل للتمرير
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // فئة الجملة ومستوى الصعوبة في المنتصف
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // فئة الجملة
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: isSmallScreen ? 8.0 : 12.0,
                                      vertical: isSmallScreen ? 4.0 : 6.0),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor.withAlpha(25),
                                    borderRadius: BorderRadius.circular(
                                        isSmallScreen ? 16.0 : 20.0),
                                  ),
                                  child: Text(
                                    widget.sentence.category,
                                    style: TextStyle(
                                      fontSize: isSmallScreen ? 10.0 : 12.0,
                                      color: AppTheme.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                // مستوى الصعوبة
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: isSmallScreen ? 8.0 : 12.0,
                                      vertical: isSmallScreen ? 4.0 : 6.0),
                                  decoration: BoxDecoration(
                                    color: _getDifficultyColor(
                                        widget.sentence.difficulty),
                                    borderRadius: BorderRadius.circular(
                                        isSmallScreen ? 16.0 : 20.0),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getDifficultyIcon(
                                            widget.sentence.difficulty),
                                        size: isSmallScreen ? 12.0 : 14.0,
                                        color: Colors.white,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        _getDifficultyText(
                                            widget.sentence.difficulty),
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 10.0 : 12.0,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: isSmallScreen ? 8.0 : 16.0),
                            // نص الجملة الإنجليزي
                            Text(
                              widget.sentence.englishText,
                              style: TextStyle(
                                fontSize: isSmallScreen ? 16.0 : 18.0,
                                fontWeight: FontWeight.bold,
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: isSmallScreen ? 8.0 : 16.0),
                            // زر إظهار الترجمة
                            Center(
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    _showTranslation = !_showTranslation;
                                  });
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: isSmallScreen ? 12.0 : 16.0,
                                      vertical: isSmallScreen ? 6.0 : 8.0),
                                  decoration: BoxDecoration(
                                    color: _showTranslation
                                        ? Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.grey.withAlpha(70)
                                            : Colors.grey.withAlpha(51)
                                        : Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? AppTheme.primaryColor
                                                .withAlpha(70)
                                            : AppTheme.primaryColor
                                                .withAlpha(25),
                                    borderRadius: BorderRadius.circular(
                                        isSmallScreen ? 16.0 : 20.0),
                                    border: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Border.all(
                                            color: _showTranslation
                                                ? Colors.grey.withAlpha(100)
                                                : AppTheme.primaryColor
                                                    .withAlpha(100),
                                            width: 1.0,
                                          )
                                        : null,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _showTranslation
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        size: isSmallScreen ? 16.0 : 18.0,
                                        color: _showTranslation
                                            ? Colors.grey
                                            : AppTheme.primaryColor,
                                      ),
                                      SizedBox(
                                          width: isSmallScreen ? 4.0 : 8.0),
                                      Text(
                                        _showTranslation
                                            ? 'إخفاء الترجمة'
                                            : 'إظهار الترجمة',
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 12.0 : 14.0,
                                          color: _showTranslation
                                              ? Colors.grey
                                              : AppTheme.primaryColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            // الترجمة العربية
                            AnimatedCrossFade(
                              firstChild: const SizedBox(height: 0),
                              secondChild: Padding(
                                padding: EdgeInsets.only(
                                    top: isSmallScreen ? 8.0 : 16.0),
                                child: Text(
                                  widget.sentence.arabicText,
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 14.0 : 16.0,
                                    color: Colors.grey,
                                    height: 1.4,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              crossFadeState: _showTranslation
                                  ? CrossFadeState.showSecond
                                  : CrossFadeState.showFirst,
                              duration: const Duration(milliseconds: 300),
                            ),
                            SizedBox(height: isSmallScreen ? 8.0 : 16.0),
                          ],
                        ),
                      ),
                    ),
                    // أزرار التفاعل - دائما في الأسفل
                    SizedBox(height: isSmallScreen ? 4.0 : 8.0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // زر المفضلة
                        IconButton(
                          icon: Icon(
                            widget.sentence.isFavoriteByCurrentUser
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: widget.sentence.isFavoriteByCurrentUser
                                ? Colors.red
                                : Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.white
                                    : Colors.grey.shade600,
                            size: isSmallScreen ? 24.0 : 28.0,
                          ),
                          padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 32.0 : 48.0,
                            minHeight: isSmallScreen ? 32.0 : 48.0,
                          ),
                          onPressed: () {
                            final viewModel = Provider.of<SentenceViewModel>(
                                context,
                                listen: false);

                            // استخدام toggleFavorite فقط - سيقوم بتحديث الحالة محليًا وفي جميع القوائم
                            viewModel.toggleFavorite(widget.sentence);
                          },
                        ),
                        // زر الاختبار
                        IconButton(
                          icon: Icon(
                            Icons.school,
                            color: AppTheme.primaryColor,
                            size: isSmallScreen ? 24.0 : 28.0,
                          ),
                          padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 32.0 : 48.0,
                            minHeight: isSmallScreen ? 32.0 : 48.0,
                          ),
                          onPressed: () {
                            // فتح نافذة الاختبار
                            showDialog(
                              context: context,
                              builder: (context) => QuizDialog(
                                sentence: widget.sentence,
                              ),
                            ).then((result) {
                              // التحقق من نتيجة الاختبار
                              if (result != null &&
                                  result is Map<String, dynamic>) {
                                // تحديث حالة الاختبار في نموذج الجملة
                                if (result
                                    .containsKey('isPronunciationTested')) {
                                  widget.sentence.isPronunciationTested =
                                      result['isPronunciationTested'];

                                  // تحديث واجهة المستخدم
                                  setState(() {});
                                }
                              }
                            });
                          },
                          tooltip: 'اختبار الفهم',
                        ),
                        // زر تعليم كمقروءة
                        Consumer<AuthViewModel>(
                          builder: (context, authVM, _) {
                            final isRead =
                                widget.sentence.isReadByCurrentUser ||
                                    _isMarkedAsRead;
                            return IconButton(
                              icon: Icon(
                                isRead
                                    ? Icons.check_circle
                                    : Icons.check_circle_outline,
                                color: isRead ? Colors.green : null,
                                size: isSmallScreen ? 24.0 : 28.0,
                              ),
                              padding:
                                  EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
                              constraints: BoxConstraints(
                                minWidth: isSmallScreen ? 32.0 : 48.0,
                                minHeight: isSmallScreen ? 32.0 : 48.0,
                              ),
                              onPressed: () {
                                if (!isRead && authVM.isAuthenticated) {
                                  final viewModel =
                                      Provider.of<SentenceViewModel>(context,
                                          listen: false);

                                  // تعليم الجملة كمقروءة
                                  viewModel.markAsRead(widget.sentence);

                                  // تغيير حالة البطاقة وإخفاؤها بعد فترة
                                  setState(() {
                                    _isMarkedAsRead = true;
                                  });

                                  // الحصول على عدد البطاقات المتبقية قبل الإخفاء
                                  final sentenceViewModel =
                                      Provider.of<SentenceViewModel>(context,
                                          listen: false);
                                  // نحسب عدد البطاقات المتبقية بعد تعليم البطاقة الحالية كمقروءة
                                  // نحتاج إلى التأكد من عدم احتساب البطاقة الحالية فقط
                                  final totalUnreadCards = sentenceViewModel
                                      .dailyRandomSentences
                                      .where((s) =>
                                          !s.isReadByCurrentUser &&
                                          s.id != widget.sentence.id)
                                      .length;

                                  debugPrint(
                                      'Remaining unread cards: $totalUnreadCards');

                                  // عرض رسالة للمستخدم فقط إذا كانت هذه هي البطاقة الأخيرة فعلاً
                                  // نتحقق من أن عدد البطاقات المتبقية هو صفر
                                  if (totalUnreadCards == 0) {
                                    // عرض الرسالة مباشرة بدون تأخير
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                            'لقد قرأت جميع الجمل اليومية. اضغط على زر "10 again" للحصول على جمل جديدة.'),
                                        backgroundColor: Color(0xFF22C55E),
                                        duration: Duration(seconds: 3),
                                      ),
                                    );
                                  }

                                  // إخفاء البطاقة بعد ثانية واحدة
                                  Future.delayed(const Duration(seconds: 1),
                                      () {
                                    if (mounted) {
                                      // تحريك البطاقات الأخرى إلى الوسط
                                      final currentPage =
                                          widget.pageController.page?.round() ??
                                              0;

                                      // معالجة تحريك البطاقات بعد تعليم الجملة كمقروءة
                                      debugPrint(
                                          'Current page: $currentPage, Widget index: ${widget.index}, Total unread cards: $totalUnreadCards');

                                      // حساب الصفحة التالية
                                      int nextPage;

                                      if (totalUnreadCards == 0) {
                                        // إذا كانت هذه هي البطاقة الأخيرة، لا نقوم بأي تحريك
                                        // لأنه لا توجد بطاقات أخرى للتحريك إليها
                                        debugPrint(
                                            'Last card marked as read, no more cards to show');

                                        // تأكد من تحديث واجهة المستخدم قبل إخفاء البطاقة
                                        viewModel.refreshRandomSentences();

                                        // لا نقوم بأي تحريك، فقط نخفي البطاقة الحالية
                                        _animationController.reverse();
                                        return;
                                      } else {
                                        // تحديد ما إذا كانت البطاقة الحالية هي الأولى في القائمة
                                        final isFirstCard = widget.index == 0;

                                        // تحديد ما إذا كانت البطاقة الحالية هي الأخيرة في القائمة
                                        final isLastCard = widget.index ==
                                            sentenceViewModel
                                                    .dailyRandomSentences
                                                    .length -
                                                1;

                                        debugPrint(
                                            'Is first card: $isFirstCard, Is last card: $isLastCard');

                                        if (widget.index == currentPage) {
                                          // إذا كانت البطاقة الحالية هي المعروضة حاليًا

                                          if (isLastCard) {
                                            // إذا كانت البطاقة الحالية هي الأخيرة، نتحرك للبطاقة السابقة
                                            nextPage = currentPage > 0
                                                ? currentPage - 1
                                                : 0;
                                            debugPrint(
                                                'Last card in list, moving to previous card: $nextPage');
                                          } else {
                                            // إذا لم تكن البطاقة الحالية هي الأخيرة، نبقى في نفس المكان
                                            // لأن البطاقة التالية ستتحرك تلقائيًا إلى موضع البطاقة الحالية
                                            nextPage = currentPage;
                                            debugPrint(
                                                'Not last card, staying at same position: $nextPage');
                                          }
                                        } else if (widget.index < currentPage) {
                                          // إذا كانت البطاقة الحالية قبل البطاقة المعروضة حاليًا، نقوم بتحريك البطاقات للخلف
                                          nextPage = currentPage - 1;
                                          debugPrint(
                                              'Card before current page, moving back to: $nextPage');
                                        } else {
                                          // إذا كانت البطاقة الحالية بعد البطاقة المعروضة حاليًا، نبقى في نفس المكان
                                          nextPage = currentPage;
                                          debugPrint(
                                              'Card after current page, staying at: $nextPage');
                                        }

                                        // تحريك البطاقات - تحقق أولاً من أن PageController لا يزال صالحًا
                                        if (widget.pageController.hasClients) {
                                          try {
                                            widget.pageController
                                                .jumpToPage(nextPage);

                                            // تحديث واجهة المستخدم فورًا بعد تعليم الجملة كمقروءة
                                            // بدلاً من الانتظار لإعادة تحميل الجمل
                                            setState(() {
                                              // تحديث حالة البطاقة المحلية
                                            });

                                            // إضافة تأخير قصير قبل إعادة تحميل الجمل
                                            Future.delayed(
                                                const Duration(
                                                    milliseconds: 300), () {
                                              try {
                                                // إعادة تحميل الجمل بعد تحريك البطاقات
                                                viewModel
                                                    .refreshRandomSentences();
                                              } catch (e) {
                                                debugPrint(
                                                    'Error refreshing sentences: $e');
                                              }
                                            });
                                          } catch (e) {
                                            debugPrint(
                                                'Error jumping to page: $e');
                                          }
                                        } else {
                                          debugPrint(
                                              'PageController no longer has clients');
                                        }
                                      }

                                      _animationController.reverse();
                                    }
                                  });
                                }
                              },
                            );
                          },
                        ),
                        // زر المشاركة
                        IconButton(
                          icon: Icon(
                            Icons.share,
                            size: isSmallScreen ? 24.0 : 28.0,
                          ),
                          padding: EdgeInsets.all(isSmallScreen ? 4.0 : 8.0),
                          constraints: BoxConstraints(
                            minWidth: isSmallScreen ? 32.0 : 48.0,
                            minHeight: isSmallScreen ? 32.0 : 48.0,
                          ),
                          onPressed: () {
                            Share.share(
                                '${widget.sentence.arabicText}\n\n${widget.sentence.englishText}');
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
