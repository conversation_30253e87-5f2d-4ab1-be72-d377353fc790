import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';

/// محول Timestamp لـ Hive
class TimestampAdapter extends TypeAdapter<Timestamp> {
  @override
  final int typeId = 10; // استخدم رقم فريد لم يتم استخدامه بعد

  @override
  Timestamp read(BinaryReader reader) {
    // قراءة الثواني والنانوثواني من Hive
    final seconds = reader.readInt();
    final nanoseconds = reader.readInt();
    return Timestamp(seconds, nanoseconds);
  }

  @override
  void write(BinaryWriter writer, Timestamp obj) {
    // كتابة الثواني والنانوثواني إلى Hive
    writer.writeInt(obj.seconds);
    writer.writeInt(obj.nanoseconds);
  }
}

/// محول DateTime لـ Hive
class DateTimeAdapter extends TypeAdapter<DateTime> {
  @override
  final int typeId = 11; // استخدم رقم فريد لم يتم استخدامه بعد

  @override
  DateTime read(BinaryReader reader) {
    // قراءة الميلي ثانية منذ عصر يونكس
    final millisecondsSinceEpoch = reader.readInt();
    return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  }

  @override
  void write(BinaryWriter writer, DateTime obj) {
    // كتابة الميلي ثانية منذ عصر يونكس
    writer.writeInt(obj.millisecondsSinceEpoch);
  }
}
