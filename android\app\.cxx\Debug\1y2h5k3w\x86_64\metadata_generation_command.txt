                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=F:\ANDROIDSDK01\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\ANDROIDSDK01\ndk\27.0.12077973
-<PERSON><PERSON>KE_TOOLCHAIN_FILE=F:\ANDROIDSDK01\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\ANDROIDSDK01\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\app\test05\test05\build\app\intermediates\cxx\Debug\1y2h5k3w\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\app\test05\test05\build\app\intermediates\cxx\Debug\1y2h5k3w\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BF:\app\test05\test05\android\app\.cxx\Debug\1y2h5k3w\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2