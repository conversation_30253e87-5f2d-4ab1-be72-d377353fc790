import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';

enum ProcessingIndicatorType {
  circular,
  wave,
  dots,
  pulse,
}

class SpeechProcessingIndicator extends StatefulWidget {
  final ProcessingIndicatorType type;
  final String message;
  final Color color;
  final double size;

  const SpeechProcessingIndicator({
    Key? key,
    this.type = ProcessingIndicatorType.circular,
    this.message = 'جاري تحليل الصوت...',
    this.color = AppTheme.primaryColor,
    this.size = 60.0,
  }) : super(key: key);

  @override
  State<SpeechProcessingIndicator> createState() => _SpeechProcessingIndicatorState();
}

class _SpeechProcessingIndicatorState extends State<SpeechProcessingIndicator> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.type) {
      case ProcessingIndicatorType.circular:
        return _buildCircularIndicator();
      case ProcessingIndicatorType.wave:
        return _buildWaveIndicator();
      case ProcessingIndicatorType.dots:
        return _buildDotsIndicator();
      case ProcessingIndicatorType.pulse:
        return _buildPulseIndicator();
      default:
        return _buildCircularIndicator();
    }
  }

  Widget _buildCircularIndicator() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Círculo exterior animado
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _controller.value * 2 * math.pi,
                    child: child,
                  );
                },
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        widget.color.withOpacity(0.0),
                        widget.color,
                      ],
                      stops: const [0.0, 1.0],
                      startAngle: 0.0,
                      endAngle: math.pi * 2,
                    ),
                  ),
                ),
              ),
              // Círculo interior
              Container(
                width: widget.size * 0.85,
                height: widget.size * 0.85,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 5,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.mic,
                  color: widget.color,
                  size: widget.size * 0.4,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          widget.message,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: widget.color,
          ),
        ),
      ],
    );
  }

  Widget _buildWaveIndicator() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size * 1.5,
          height: widget.size,
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return CustomPaint(
                painter: WavePainter(
                  animation: _controller,
                  color: widget.color,
                ),
                child: Center(
                  child: Container(
                    width: widget.size * 0.5,
                    height: widget.size * 0.5,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 5,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.mic,
                      color: widget.color,
                      size: widget.size * 0.25,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        Text(
          widget.message,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: widget.color,
          ),
        ),
      ],
    );
  }

  Widget _buildDotsIndicator() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size * 1.5,
          height: widget.size * 0.7,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  final double offset = index / 5;
                  final double t = (((_controller.value + offset) % 1.0) * 2);
                  final double scale = 0.5 + (t < 1.0 ? t : 2.0 - t) * 0.5;
                  
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: widget.size * 0.15,
                    height: widget.size * 0.15 * scale,
                    decoration: BoxDecoration(
                      color: widget.color.withOpacity(0.7 + 0.3 * scale),
                      borderRadius: BorderRadius.circular(widget.size * 0.075),
                    ),
                  );
                },
              );
            }),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.mic,
              color: widget.color,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              widget.message,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPulseIndicator() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Pulsos animados
              ...List.generate(3, (index) {
                final double delay = index * 0.3;
                return AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    final double progress = (_controller.value + delay) % 1.0;
                    final double scale = progress;
                    final double opacity = (1.0 - progress);
                    
                    return Opacity(
                      opacity: opacity,
                      child: Transform.scale(
                        scale: 0.5 + scale * 0.5,
                        child: Container(
                          width: widget.size,
                          height: widget.size,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: widget.color,
                              width: 2.0,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }),
              // Círculo central
              Container(
                width: widget.size * 0.5,
                height: widget.size * 0.5,
                decoration: BoxDecoration(
                  color: widget.color,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.mic,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          widget.message,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: widget.color,
          ),
        ),
      ],
    );
  }
}

class WavePainter extends CustomPainter {
  final Animation<double> animation;
  final Color color;

  WavePainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final centerY = size.height / 2;
    final width = size.width;
    
    for (int i = 0; i < 3; i++) {
      final amplitude = (i + 1) * 10.0;
      final frequency = (i + 1) * 2.0;
      final phase = animation.value * 2 * math.pi * frequency;
      
      final path = Path();
      path.moveTo(0, centerY);
      
      for (double x = 0; x < width; x += 1) {
        final y = centerY + math.sin(x / width * 2 * math.pi * frequency + phase) * amplitude;
        path.lineTo(x, y);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) => true;
}
