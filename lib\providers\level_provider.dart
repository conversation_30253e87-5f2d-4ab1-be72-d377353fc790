import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/level.dart';
import '../models/lesson_group.dart';
import '../models/cycle.dart';

class LevelProvider with ChangeNotifier {
  List<Level> _levels = [];
  bool _isLoading = false;
  String? _error;

  // الحصول على قائمة المستويات
  List<Level> get levels => [..._levels];

  // الحصول على المستوى الحالي
  Level? get currentLevel => _levels.firstWhere((level) => level.isCurrent,
      orElse: () => _levels.first);

  // حالة التحميل
  bool get isLoading => _isLoading;

  // رسالة الخطأ
  String? get error => _error;

  // الحصول على مستوى معين بواسطة المعرف
  Level? getLevelById(int id) {
    try {
      return _levels.firstWhere((level) => level.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على مستوى معين بواسطة مستوى الصعوبة
  Level? getLevelByDifficulty(String difficulty) {
    try {
      return _levels.firstWhere((level) => level.difficulty == difficulty);
    } catch (e) {
      return null;
    }
  }

  // جلب المستويات من Firestore فقط (بدون تخزين محلي)
  Future<void> fetchLevels() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final levelsSnapshot = await FirebaseFirestore.instance.collection('levels').get();
        final List<Level> fetchedLevels = [];
        final userProgressDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('progress')
            .doc('levels')
            .get();
        Map<String, dynamic> userProgress = {};
        if (userProgressDoc.exists) {
          userProgress = userProgressDoc.data() as Map<String, dynamic>;
          debugPrint('Firestore userProgress: $userProgress');
        }
        for (var doc in levelsSnapshot.docs) {
          final levelData = doc.data();
          final levelId = int.parse(doc.id);
          List<Cycle> cycles = [];
          try {
            final cyclesSnapshot = await FirebaseFirestore.instance
                .collection('levels')
                .doc(doc.id)
                .collection('cycles')
                .get();
            for (var cycleDoc in cyclesSnapshot.docs) {
              final cycleData = cycleDoc.data();
              final cycleId = int.parse(cycleDoc.id);
              final String cycleKey = 'level_${levelId}_cycle_$cycleId';
              final bool isCompleted = userProgress['${cycleKey}_completed'] ?? false;
              final int completedSentences = userProgress['${cycleKey}_completedSentences'] ?? 0;
              final double accuracy = userProgress['${cycleKey}_accuracy'] ?? 0.0;
              final bool isLocked = userProgress['${cycleKey}_locked'] ?? (cycleId > 1);
              List<LessonGroup> lessonGroups = [];
              final lessonGroupsSnapshot = await FirebaseFirestore.instance
                  .collection('levels')
                  .doc(doc.id)
                  .collection('cycles')
                  .doc(cycleDoc.id)
                  .collection('lessonGroups')
                  .get();
              for (var groupDoc in lessonGroupsSnapshot.docs) {
                final groupData = groupDoc.data();
                final groupId = int.parse(groupDoc.id);
                final globalId = groupData['globalId'] ?? ((cycleId - 1) * 4 + groupId);
                final String groupKey = '${cycleKey}_group_$groupId';
                final bool groupIsCompleted = userProgress['${groupKey}_completed'] ?? false;
                final int groupCompletedSentences = userProgress['${groupKey}_completedSentences'] ?? 0;
                final double groupAccuracy = userProgress['${groupKey}_accuracy'] ?? 0.0;
                final bool groupIsLocked = userProgress['${groupKey}_locked'] ?? (groupId > 1);
                debugPrint('Firestore groupKey: $groupKey, isLocked: $groupIsLocked, firestoreValue: ${userProgress['${groupKey}_locked']}');
                lessonGroups.add(LessonGroup(
                  id: groupId,
                  cycleId: cycleId,
                  globalId: globalId,
                  title: groupData['title'] ?? '',
                  type: LessonTypeExtension.fromJson(groupData['type'] ?? 'sentenceBatch'),
                  totalSentences: groupData['totalSentences'] ?? 0,
                  completedSentences: groupCompletedSentences,
                  accuracy: groupAccuracy,
                  isCompleted: groupIsCompleted,
                  isLocked: groupIsLocked,
                  routePath: groupData['routePath'],
                ));
              }
              cycles.add(Cycle(
                id: cycleId,
                title: cycleData['title'] ?? 'الدورة $cycleId',
                isLocked: isLocked,
                isCompleted: isCompleted,
                lessonGroups: lessonGroups,
                totalSentences: cycleData['totalSentences'] ?? 0,
                completedSentences: completedSentences,
                accuracy: accuracy,
              ));
            }
          } catch (e) {
            debugPrint('خطأ في جلب دورات المستوى $levelId: $e');
          }
          final bool isLocked = userProgress['level_${levelId}_unlocked'] != true;
          final bool isCurrent = userProgress['currentLevel'] == levelId;
          final int earnedPoints = userProgress['level_${levelId}_points'] ?? 0;
          String difficulty;
          switch (levelId) {
            case 1:
              difficulty = 'easy';
              break;
            case 2:
              difficulty = 'medium';
              break;
            case 3:
              difficulty = 'hard';
              break;
            default:
              difficulty = 'medium';
          }
          fetchedLevels.add(Level(
            id: levelId,
            title: levelData['title'] ?? '',
            isLocked: isLocked,
            isCurrent: isCurrent,
            requiredSentences: levelData['requiredSentences'] ?? 0,
            cycles: cycles,
            totalEducationalPoints: levelData['totalEducationalPoints'] ?? 0,
            earnedEducationalPoints: earnedPoints,
            difficulty: difficulty,
          ));
        }
        fetchedLevels.sort((a, b) => a.id.compareTo(b.id));
        _levels = fetchedLevels;
        notifyListeners();
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء جلب المستويات: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تحديث تقدم المستخدم في مستوى معين
  Future<void> updateLevelProgress(int levelId, int completedSentences, int earnedPoints) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${levelId}_completedSentences': completedSentences,
        'level_${levelId}_points': earnedPoints,
      }, SetOptions(merge: true));
      debugPrint('Firestore updateLevelProgress: levelId=$levelId, completedSentences=$completedSentences, earnedPoints=$earnedPoints');
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex != -1) {
        final updatedLevel = _levels[levelIndex].copyWith(earnedEducationalPoints: earnedPoints);
        _levels[levelIndex] = updatedLevel;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في تحديث تقدم المستوى: $e');
    }
  }

  // تحديث تقدم المستخدم في دورة معينة
  Future<void> updateCycleProgress(int levelId, int cycleId, int completedSentences, double accuracy, bool isCompleted) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${levelId}_cycle_${cycleId}_completedSentences': completedSentences,
        'level_${levelId}_cycle_${cycleId}_accuracy': accuracy,
        'level_${levelId}_cycle_${cycleId}_completed': isCompleted,
      }, SetOptions(merge: true));
      debugPrint('Firestore updateCycleProgress: levelId=$levelId, cycleId=$cycleId, completedSentences=$completedSentences, accuracy=$accuracy, isCompleted=$isCompleted');
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex != -1) {
        final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == cycleId);
        if (cycleIndex != -1) {
          final updatedCycles = List<Cycle>.from(_levels[levelIndex].cycles);
          updatedCycles[cycleIndex] = updatedCycles[cycleIndex].copyWith(
            completedSentences: completedSentences,
            accuracy: accuracy,
            isCompleted: isCompleted,
          );
          _levels[levelIndex] = _levels[levelIndex].copyWith(cycles: updatedCycles);
          notifyListeners();
          if (isCompleted) {
            await _unlockNextCycle(levelId, cycleId);
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث تقدم الدورة: $e');
    }
  }

  // تحديث تقدم المستخدم في مجموعة دروس معينة
  Future<void> updateLessonGroupProgress(int levelId, int cycleId, int groupId, int completedSentences, double accuracy, bool isCompleted) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${levelId}_cycle_${cycleId}_group_${groupId}_completedSentences': completedSentences,
        'level_${levelId}_cycle_${cycleId}_group_${groupId}_accuracy': accuracy,
        'level_${levelId}_cycle_${cycleId}_group_${groupId}_completed': isCompleted,
      }, SetOptions(merge: true));
      debugPrint('Firestore updateLessonGroupProgress: levelId=$levelId, cycleId=$cycleId, groupId=$groupId, completedSentences=$completedSentences, accuracy=$accuracy, isCompleted=$isCompleted');
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex != -1) {
        final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == cycleId);
        if (cycleIndex != -1) {
          final groupIndex = _levels[levelIndex].cycles[cycleIndex].lessonGroups.indexWhere((group) => group.id == groupId);
          if (groupIndex != -1) {
            final updatedGroups = List<LessonGroup>.from(_levels[levelIndex].cycles[cycleIndex].lessonGroups);
            updatedGroups[groupIndex] = updatedGroups[groupIndex].copyWith(
              completedSentences: completedSentences,
              accuracy: accuracy,
              isCompleted: isCompleted,
            );
            final updatedCycles = List<Cycle>.from(_levels[levelIndex].cycles);
            updatedCycles[cycleIndex] = updatedCycles[cycleIndex].copyWith(lessonGroups: updatedGroups);
            _levels[levelIndex] = _levels[levelIndex].copyWith(cycles: updatedCycles);
            notifyListeners();
            if (isCompleted) {
              await _unlockNextGroup(levelId, cycleId, groupId);
            }
            final cycle = _levels[levelIndex].cycles[cycleIndex];
            final totalCompletedSentences = cycle.lessonGroups.fold(0, (total, group) => total + group.completedSentences);
            final cycleIsCompleted = cycle.lessonGroups.every((group) => group.isCompleted);
            double totalAccuracy = 0;
            int groupsWithAccuracy = 0;
            for (var group in cycle.lessonGroups) {
              if (group.completedSentences > 0) {
                totalAccuracy += group.accuracy;
                groupsWithAccuracy++;
              }
            }
            final cycleAccuracy = groupsWithAccuracy > 0 ? totalAccuracy / groupsWithAccuracy : 0.0;
            if (cycleIsCompleted || totalCompletedSentences > 0) {
              await updateCycleProgress(levelId, cycleId, totalCompletedSentences, cycleAccuracy, cycleIsCompleted);
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث تقدم مجموعة الدروس: $e');
    }
  }

  // تحديث حالة قفل مجموعة دروس معينة
  Future<void> updateLessonGroupLockStatus(int levelId, int cycleId, int groupId, bool isLocked) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${levelId}_cycle_${cycleId}_group_${groupId}_locked': isLocked,
      }, SetOptions(merge: true));
      debugPrint('Firestore updateLessonGroupLockStatus: levelId=$levelId, cycleId=$cycleId, groupId=$groupId, isLocked=$isLocked');
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex != -1) {
        final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == cycleId);
        if (cycleIndex != -1) {
          final groupIndex = _levels[levelIndex].cycles[cycleIndex].lessonGroups.indexWhere((group) => group.id == groupId);
          if (groupIndex != -1) {
            final updatedGroups = List<LessonGroup>.from(_levels[levelIndex].cycles[cycleIndex].lessonGroups);
            updatedGroups[groupIndex] = updatedGroups[groupIndex].copyWith(isLocked: isLocked);
            final updatedCycles = List<Cycle>.from(_levels[levelIndex].cycles);
            updatedCycles[cycleIndex] = updatedCycles[cycleIndex].copyWith(lessonGroups: updatedGroups);
            _levels[levelIndex] = _levels[levelIndex].copyWith(cycles: updatedCycles);
            notifyListeners();
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة قفل مجموعة الدروس: $e');
    }
  }

  // تحديث حالة قفل دورة معينة
  Future<void> updateCycleLockStatus(int levelId, int cycleId, bool isLocked) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${levelId}_cycle_${cycleId}_locked': isLocked,
      }, SetOptions(merge: true));
      debugPrint('Firestore updateCycleLockStatus: levelId=$levelId, cycleId=$cycleId, isLocked=$isLocked');
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex != -1) {
        final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == cycleId);
        if (cycleIndex != -1) {
          final updatedCycles = List<Cycle>.from(_levels[levelIndex].cycles);
          updatedCycles[cycleIndex] = updatedCycles[cycleIndex].copyWith(isLocked: isLocked);
          _levels[levelIndex] = _levels[levelIndex].copyWith(cycles: updatedCycles);
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة قفل الدورة: $e');
    }
  }

  // فتح المستوى التالي
  Future<void> unlockNextLevel(int currentLevelId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;
      final nextLevelId = currentLevelId + 1;
      final nextLevelExists = _levels.any((level) => level.id == nextLevelId);
      if (!nextLevelExists) return;
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'level_${nextLevelId}_unlocked': true,
        'currentLevel': nextLevelId,
      }, SetOptions(merge: true));
      debugPrint('Firestore unlockNextLevel: currentLevelId=$currentLevelId, nextLevelId=$nextLevelId');
      for (int i = 0; i < _levels.length; i++) {
        if (_levels[i].id == currentLevelId) {
          _levels[i] = _levels[i].copyWith(isCurrent: false);
        } else if (_levels[i].id == nextLevelId) {
          _levels[i] = _levels[i].copyWith(isLocked: false, isCurrent: true);
        }
      }
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في فتح المستوى التالي: $e');
    }
  }

  // فتح المجموعة التالية بعد إكمال المجموعة الحالية
  Future<void> _unlockNextGroup(int levelId, int cycleId, int currentGroupId) async {
    try {
      // التحقق من وجود مستوى ودورة
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex == -1) return;

      final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == cycleId);
      if (cycleIndex == -1) return;

      // التحقق من وجود مجموعة تالية
      final nextGroupId = currentGroupId + 1;
      final nextGroupExists = _levels[levelIndex]
          .cycles[cycleIndex]
          .lessonGroups
          .any((g) => g.id == nextGroupId);

      if (!nextGroupExists) return;

      // فتح المجموعة التالية دومًا في Firestore
      await updateLessonGroupLockStatus(levelId, cycleId, nextGroupId, false);
    } catch (e) {
      debugPrint('خطأ في فتح المجموعة التالية: $e');
    }
  }

  // فتح الدورة التالية بعد إكمال الدورة الحالية
  Future<void> _unlockNextCycle(int levelId, int currentCycleId) async {
    try {
      // التحقق من وجود مستوى
      final levelIndex = _levels.indexWhere((level) => level.id == levelId);
      if (levelIndex == -1) return;

      // التحقق من وجود دورة تالية
      final nextCycleId = currentCycleId + 1;
      final nextCycleExists = _levels[levelIndex].cycles.any((c) => c.id == nextCycleId);

      if (!nextCycleExists) return;

      // الحصول على الدورة التالية
      final cycleIndex = _levels[levelIndex].cycles.indexWhere((cycle) => cycle.id == nextCycleId);

      if (cycleIndex != -1) {
        final nextCycle = _levels[levelIndex].cycles[cycleIndex];

        // إذا كانت الدورة التالية مغلقة، نقوم بفتحها
        if (nextCycle.isLocked) {
          await updateCycleLockStatus(levelId, nextCycleId, false);
        }
      }
    } catch (e) {
      debugPrint('خطأ في فتح الدورة التالية: $e');
    }
  }
}
