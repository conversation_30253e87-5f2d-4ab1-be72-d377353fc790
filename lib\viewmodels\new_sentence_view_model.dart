import 'package:flutter/foundation.dart';
import '../models/hive/hive_sentence_model.dart';
import '../services/hive_sentence_service.dart';
import '../services/sync_manager.dart';
import '../services/sync_service.dart';
import '../services/new_daily_sentence_service.dart';
import '../services/displayed_sentences_service.dart';

/// نموذج عرض جديد لإدارة الجمل باستخدام Hive
class NewSentenceViewModel extends ChangeNotifier {
  final HiveSentenceService _hiveSentenceService;
  final SyncManager _syncManager;
  final SyncService _syncService;
  final NewDailySentenceService _newDailySentenceService;
  final DisplayedSentencesService _displayedSentencesService;

  List<HiveSentenceModel> _dailySentences = [];
  List<HiveSentenceModel> _allSentences = [];
  List<HiveSentenceModel> _readSentences = [];
  List<HiveSentenceModel> _favoriteSentences = [];

  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  // إحصائيات
  int _todayShownCount = 0;
  int _todayReadCount = 0;

  // الحصول على البيانات
  List<HiveSentenceModel> get dailySentences => _dailySentences;
  List<HiveSentenceModel> get allSentences => _allSentences;
  List<HiveSentenceModel> get readSentences => _readSentences;
  List<HiveSentenceModel> get favoriteSentences => _favoriteSentences;

  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;

  int get todayShownCount => _todayShownCount;
  int get todayReadCount => _todayReadCount;

  NewSentenceViewModel({
    required HiveSentenceService hiveSentenceService,
    required SyncManager syncManager,
    required SyncService syncService,
    required NewDailySentenceService newDailySentenceService,
    required DisplayedSentencesService displayedSentencesService,
  })  : _hiveSentenceService = hiveSentenceService,
        _syncManager = syncManager,
        _syncService = syncService,
        _newDailySentenceService = newDailySentenceService,
        _displayedSentencesService = displayedSentencesService {
    // تحميل البيانات عند إنشاء النموذج
    _loadInitialData();

    // إضافة مستمع لتغييرات المزامنة
    _syncManager.addListener(_onSyncStateChanged);
  }

  /// استجابة لتغيير حالة المزامنة
  void _onSyncStateChanged() {
    // تحديث الإحصائيات عند تغيير حالة المزامنة
    loadStatistics();
    notifyListeners();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    await loadStatistics();
  }

  /// التحقق مما إذا كان صندوق الجمل فارغًا
  bool isSentencesBoxEmpty() {
    try {
      return _hiveSentenceService.getSentencesBox().isEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق مما إذا كان صندوق الجمل فارغًا: $e');
      return true; // نفترض أنه فارغ في حالة حدوث خطأ
    }
  }

  /// تحميل جميع الجمل من Firebase عند بدء التطبيق لأول مرة
  Future<void> loadAllSentencesFirstTime(String userId) async {
    try {
      _setLoading(true);

      // التحقق مما إذا كان صندوق الجمل فارغًا (أول مرة)
      final isFirstLoad = isSentencesBoxEmpty();

      if (isFirstLoad) {
        debugPrint('تحميل جميع الجمل من Firebase للمرة الأولى');
        // استدعاء طريقة جلب جميع الجمل من SyncService
        await _syncService.fetchAllSentencesFirstTime(userId);
        debugPrint('تم تحميل جميع الجمل من Firebase بنجاح');
      } else {
        debugPrint(
            'الجمل موجودة بالفعل في التخزين المحلي، تم تخطي التحميل الأولي');
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل جميع الجمل للمرة الأولى: $e');
    }
  }

  /// تحميل الجمل اليومية
  Future<void> loadDailySentences({
    String? userId,
    bool forceRefresh = false,
  }) async {
    try {
      _setLoading(true);

      // التحقق مما إذا كان هذا هو التحميل الأول
      final isFirstLoad = isSentencesBoxEmpty();

      if (isFirstLoad && userId != null) {
        // إذا كان هذا هو التحميل الأول، قم بتحميل جميع الجمل من Firebase
        await loadAllSentencesFirstTime(userId);
      }

      // الحصول على الجمل اليومية
      if (userId != null) {
        _dailySentences = await _newDailySentenceService.getDailySentences(
          userId,
          forceRefresh: forceRefresh,
        );
      } else {
        // استخدام الجمل المخزنة في Hive
        _dailySentences = _hiveSentenceService.getDailySentences();
      }

      // طباعة معلومات تشخيصية
      final unreadCount =
          _dailySentences.where((s) => !s.isReadByCurrentUser).length;
      debugPrint(
          'تم تحميل ${_dailySentences.length} جملة يومية، منها $unreadCount غير مقروءة');

      // التأكد من أن الجمل غير المقروءة تظهر أولاً
      if (_dailySentences.isNotEmpty) {
        _dailySentences.sort((a, b) {
          if (a.isReadByCurrentUser && !b.isReadByCurrentUser) return 1;
          if (!a.isReadByCurrentUser && b.isReadByCurrentUser) return -1;
          return 0;
        });
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الجمل اليومية: $e');
    }
  }

  /// تحميل جميع الجمل
  Future<void> loadAllSentences() async {
    try {
      _setLoading(true);

      _allSentences = _hiveSentenceService.getAllSentences();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل جميع الجمل: $e');
    }
  }

  /// تحميل الجمل المقروءة
  Future<void> loadReadSentences() async {
    try {
      _setLoading(true);

      _readSentences = _hiveSentenceService.getReadSentences();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الجمل المقروءة: $e');
    }
  }

  /// تحميل الجمل المفضلة
  Future<void> loadFavoriteSentences() async {
    try {
      _setLoading(true);

      _favoriteSentences = _hiveSentenceService.getFavoriteSentences();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الجمل المفضلة: $e');
    }
  }

  /// تعيين جملة كمقروءة
  Future<void> markSentenceAsRead(String sentenceId, String userId) async {
    try {
      await _newDailySentenceService.markSentenceAsRead(sentenceId, userId);

      // تحديث القوائم
      await loadDailySentences(userId: userId);
      await loadStatistics();

      notifyListeners();
    } catch (e) {
      _setError('خطأ في تعيين الجملة كمقروءة: $e');
    }
  }

  /// تبديل حالة المفضلة للجملة
  Future<void> toggleFavorite(String sentenceId) async {
    try {
      await _hiveSentenceService.toggleFavorite(sentenceId);

      // تحديث القوائم
      await loadDailySentences();
      await loadFavoriteSentences();

      notifyListeners();
    } catch (e) {
      _setError('خطأ في تبديل حالة المفضلة للجملة: $e');
    }
  }

  /// التحقق مما إذا كانت الجملة مفضلة
  bool isSentenceFavorite(String sentenceId) {
    return _hiveSentenceService.isSentenceFavorite(sentenceId);
  }

  /// الحصول على المزيد من الجمل (زر "10 مرة أخرى")
  Future<void> getMoreSentences(String userId) async {
    try {
      _setLoading(true);

      // التحقق مما إذا كان هذا هو التحميل الأول
      final isFirstLoad = isSentencesBoxEmpty();

      if (isFirstLoad) {
        // إذا كان هذا هو التحميل الأول، قم بتحميل جميع الجمل من Firebase
        await loadAllSentencesFirstTime(userId);
      }

      // التحقق مما إذا كانت جميع الجمل الحالية قد تمت قراءتها
      final allRead = _hiveSentenceService.areAllDailySentencesRead();

      if (!allRead) {
        // إذا لم تتم قراءة جميع الجمل، إظهار رسالة خطأ
        _setError('يجب قراءة جميع الجمل الحالية قبل الحصول على جمل جديدة');
        return;
      }

      _dailySentences = await _newDailySentenceService.getMoreSentences(userId);

      // تحديث الإحصائيات بعد الحصول على جمل جديدة
      await loadStatistics();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في الحصول على المزيد من الجمل: $e');
    }
  }

  /// مزامنة البيانات مع Firebase (يدويًا فقط)
  Future<bool> syncWithFirebase(String userId) async {
    try {
      _setLoading(true);

      // طباعة عدد العناصر المنتظرة للمزامنة قبل البدء
      final pendingCount = _syncManager.getPendingSyncItemsCount();
      debugPrint('بدء المزامنة اليدوية. العناصر المنتظرة: $pendingCount');

      final result = await _syncManager.syncWithFirebase(userId);

      // تحديث البيانات بعد المزامنة
      await loadDailySentences(userId: userId);
      await loadStatistics();

      // طباعة عدد العناصر المنتظرة للمزامنة بعد الانتهاء
      final remainingCount = _syncManager.getPendingSyncItemsCount();
      debugPrint('انتهت المزامنة اليدوية. العناصر المتبقية: $remainingCount');

      _setLoading(false);
      notifyListeners();

      return result;
    } catch (e) {
      _setError('خطأ في المزامنة مع Firebase: $e');
      return false;
    }
  }

  /// استعادة البيانات من Firebase بعد إعادة التثبيت
  Future<bool> restoreDataFromFirebase(String userId) async {
    try {
      _setLoading(true);

      // استخدام SyncManager لاستعادة البيانات
      final result = await _syncManager.restoreDataFromFirebase(userId);

      if (!result) {
        _setError('فشلت استعادة البيانات من Firebase');
        return false;
      }

      // تحديث البيانات بعد الاستعادة
      await loadDailySentences(userId: userId);
      await loadStatistics();
      await loadFavoriteSentences();
      await loadReadSentences();
      await loadAllSentences();

      _setLoading(false);
      notifyListeners();

      return true;
    } catch (e) {
      _setError('خطأ في استعادة البيانات من Firebase: $e');
      return false;
    }
  }

  /// تحميل الإحصائيات
  Future<void> loadStatistics() async {
    try {
      // تحميل الإحصائيات من التخزين المحلي
      _todayShownCount = _hiveSentenceService.getTodayShownCount();
      _todayReadCount = _hiveSentenceService.getTodayReadCount();

      // تحميل عدد الجمل المقروءة والمفضلة
      final readSentences = _hiveSentenceService.getReadSentences();
      final favoriteSentences = _hiveSentenceService.getFavoriteSentences();

      debugPrint('تم تحميل الإحصائيات من التخزين المحلي:');
      debugPrint('قراءة اليوم=$_todayReadCount, عرض اليوم=$_todayShownCount');
      debugPrint(
          'إجمالي المقروءة=${readSentences.length}, إجمالي المفضلة=${favoriteSentences.length}');

      // تحميل إحصائيات الأسبوع والشهر
      await loadPeriodStatistics();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }
  }

  // إحصائيات الفترات الزمنية
  Map<String, int> _weeklyStats = {
    'totalRead': 0,
    'totalShown': 0,
    'completionPercentage': 0
  };
  Map<String, int> _monthlyStats = {
    'totalRead': 0,
    'totalShown': 0,
    'completionPercentage': 0
  };

  // الحصول على إحصائيات الفترات الزمنية
  Map<String, int> get weeklyStats => _weeklyStats;
  Map<String, int> get monthlyStats => _monthlyStats;

  /// تحميل إحصائيات الفترات الزمنية (الأسبوع والشهر)
  Future<void> loadPeriodStatistics() async {
    try {
      final now = DateTime.now();

      // حساب بداية الأسبوع (قبل 7 أيام)
      final weekStart = DateTime(now.year, now.month, now.day - 7);

      // حساب بداية الشهر (قبل 30 يومًا)
      final monthStart = DateTime(now.year, now.month, now.day - 30);

      // الحصول على إحصائيات الأسبوع
      _weeklyStats =
          _hiveSentenceService.getTotalStatisticsForDateRange(weekStart, now);

      // الحصول على إحصائيات الشهر
      _monthlyStats =
          _hiveSentenceService.getTotalStatisticsForDateRange(monthStart, now);

      debugPrint(
          'تم تحميل إحصائيات الفترات الزمنية: أسبوع=${_weeklyStats['totalRead']}/${_weeklyStats['totalShown']}, شهر=${_monthlyStats['totalRead']}/${_monthlyStats['totalShown']}');
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات الفترات الزمنية: $e');
    }
  }

  /// الحصول على عدد الجمل المعروضة
  int getDisplayedSentencesCount() {
    return _displayedSentencesService.getDisplayedSentencesCount();
  }

  /// مسح قائمة الجمل المعروضة
  Future<void> clearDisplayedSentences() async {
    await _displayedSentencesService.clearDisplayedSentences();
    notifyListeners();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _hasError = false;
      _errorMessage = '';
    }
    notifyListeners();
  }

  /// تعيين حالة الخطأ
  void _setError(String message) {
    _isLoading = false;
    _hasError = true;
    _errorMessage = message;
    debugPrint(message);
    notifyListeners();
  }

  @override
  void dispose() {
    // إزالة المستمع عند التخلص من النموذج
    _syncManager.removeListener(_onSyncStateChanged);
    super.dispose();
  }
}
