import 'package:flutter/foundation.dart';
import '../models/sentence_model.dart';
import '../models/hive/hive_sentence_model.dart';
import '../services/hive_sentence_service.dart';
import '../services/sync_manager.dart';
import '../services/daily_sentence_service.dart';

/// محول بين SentenceViewModel و HiveSentenceService
/// يسمح لـ SentenceViewModel باستخدام HiveSentenceService للتخزين المحلي
class SentenceViewModelAdapter {
  final HiveSentenceService _hiveSentenceService;
  final SyncManager _syncManager;
  final DailySentenceService _dailySentenceService;

  SentenceViewModelAdapter(
      this._hiveSentenceService, this._syncManager, this._dailySentenceService);

  /// تحويل HiveSentenceModel إلى SentenceModel
  SentenceModel toSentenceModel(HiveSentenceModel hiveModel) {
    return SentenceModel(
      id: hiveModel.id,
      arabicText: hiveModel.arabicText,
      englishText: hiveModel.englishText,
      category: hiveModel.category,
      createdAt: hiveModel.createdAt,
      readBy: {}, // لا نستخدم readBy في النموذج الجديد
      isFavorite: false, // لا نستخدم isFavorite في النموذج الجديد
      difficulty: hiveModel.difficulty,
      audioUrl: hiveModel.audioUrl,
      isReadByCurrentUser: hiveModel.isReadByCurrentUser,
      isFavoriteByCurrentUser: hiveModel.isFavoriteByCurrentUser,
    );
  }

  /// تحويل SentenceModel إلى HiveSentenceModel
  HiveSentenceModel toHiveModel(SentenceModel model) {
    return HiveSentenceModel(
      id: model.id,
      arabicText: model.arabicText,
      englishText: model.englishText,
      category: model.category,
      createdAt: model.createdAt,
      audioUrl: model.audioUrl,
      difficulty: model.difficulty,
      isReadByCurrentUser: model.isReadByCurrentUser,
      isFavoriteByCurrentUser: model.isFavoriteByCurrentUser,
      lastModified: DateTime.now(),
    );
  }

  /// تعيين جملة كمقروءة
  Future<void> markSentenceAsRead(SentenceModel model) async {
    try {
      await _hiveSentenceService.markSentenceAsRead(model.id);
      debugPrint(
          'تم تعيين الجملة ${model.id} كمقروءة باستخدام HiveSentenceService');
    } catch (e) {
      debugPrint('خطأ في تعيين الجملة كمقروءة: $e');
      rethrow;
    }
  }

  /// تبديل حالة المفضلة للجملة
  Future<void> toggleFavorite(SentenceModel model) async {
    try {
      await _hiveSentenceService.toggleFavorite(model.id);
      debugPrint(
          'تم تبديل حالة المفضلة للجملة ${model.id} باستخدام HiveSentenceService');
    } catch (e) {
      debugPrint('خطأ في تبديل حالة المفضلة للجملة: $e');
      rethrow;
    }
  }

  /// الحصول على الجمل اليومية (بما في ذلك المقروءة)
  List<SentenceModel> getDailySentences() {
    try {
      final hiveSentences =
          _hiveSentenceService.getDailySentences(includeRead: true);
      final sentences = hiveSentences.map(toSentenceModel).toList();

      // طباعة معلومات تصحيح
      debugPrint('تم الحصول على ${sentences.length} جملة يومية من المحول');
      for (var sentence in sentences) {
        debugPrint(
            'الجملة ${sentence.id}: مقروءة=${sentence.isReadByCurrentUser}');
      }

      return sentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية: $e');
      return [];
    }
  }

  /// الحصول على الجمل اليومية غير المقروءة
  List<SentenceModel> getUnreadDailySentences() {
    try {
      final hiveSentences = _hiveSentenceService.getUnreadDailySentences();
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية غير المقروءة: $e');
      return [];
    }
  }

  /// الحصول على الجمل المقروءة
  List<SentenceModel> getReadSentences() {
    try {
      final hiveSentences = _hiveSentenceService.getReadSentences();
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل المقروءة: $e');
      return [];
    }
  }

  /// الحصول على الجمل المفضلة
  List<SentenceModel> getFavoriteSentences() {
    try {
      final hiveSentences = _hiveSentenceService.getFavoriteSentences();
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل المفضلة: $e');
      return [];
    }
  }

  /// الحصول على عدد الجمل المعروضة اليوم
  int getTodayShownCount() {
    return _hiveSentenceService.getTodayShownCount();
  }

  /// الحصول على عدد الجمل المقروءة اليوم
  int getTodayReadCount() {
    return _hiveSentenceService.getTodayReadCount();
  }

  /// الحصول على عدد العناصر المنتظرة للمزامنة
  int getPendingSyncItemsCount() {
    return _syncManager.getPendingSyncItemsCount();
  }

  /// مزامنة البيانات مع Firebase
  Future<bool> syncWithFirebase(String userId) async {
    try {
      return await _syncManager.syncWithFirebase(userId);
    } catch (e) {
      debugPrint('خطأ في مزامنة البيانات مع Firebase: $e');
      return false;
    }
  }

  /// التحقق مما إذا كان يوم جديد قد بدأ
  bool isNewDay() {
    return _hiveSentenceService.isNewDay();
  }

  /// التحقق مما إذا كانت جميع الجمل اليومية مقروءة
  bool areAllDailySentencesRead() {
    return _hiveSentenceService.areAllDailySentencesRead();
  }

  /// تحديث حالة القراءة من سجلات المزامنة
  Future<void> updateReadStatusFromSyncLogs() async {
    try {
      await _hiveSentenceService.updateReadStatusFromSyncLogs();
      debugPrint('تم تحديث حالة القراءة من سجلات المزامنة باستخدام المحول');
    } catch (e) {
      debugPrint('خطأ في تحديث حالة القراءة من سجلات المزامنة: $e');
    }
  }

  /// الحصول على المزيد من الجمل (زر "10 مرة أخرى")
  Future<List<SentenceModel>> getMoreSentences(String userId) async {
    try {
      // التحقق مما إذا كانت جميع الجمل الحالية قد تمت قراءتها
      final allRead = _hiveSentenceService.areAllDailySentencesRead();

      if (!allRead) {
        debugPrint('يجب قراءة جميع الجمل الحالية قبل الحصول على جمل جديدة');
        return [];
      }

      final hiveSentences =
          await _dailySentenceService.getMoreSentences(userId);
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المزيد من الجمل: $e');
      return [];
    }
  }

  // Estos métodos ya están implementados arriba
}
