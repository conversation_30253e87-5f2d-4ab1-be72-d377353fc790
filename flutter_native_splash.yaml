flutter_native_splash:
  # لون الخلفية للوضع العادي (النهاري)
  color: "#d4e4ed"
  image: "assets/images/10again_splash_screen.png"

  # لون الخلفية للوضع المظلم
  color_dark: "#1E1E2E"
  image_dark: "assets/images/10again_splash_screen_dark.png"

  # إعدادات Android 12
  android_12:
    # صورة الوضع العادي - يجب أن تكون 1152×1152 بكسل
    image: "assets/images/splash screen_12_logo.png"
    icon_background_color: "#d4e4ed"

    # صورة الوضع المظلم
    image_dark: "assets/images/splash screen_12_logo.png"
    icon_background_color_dark: "#1E1E2E"

  # تمكين وضع ملء الشاشة
  fullscreen: true

  # تحديد موضع الصورة
  android_gravity: center
  ios_content_mode: scaleAspectFit

  # تحسين الأداء
  android: true
  ios: true
  web: false

  # تقليل وقت الانتظار
  android_screen_orientation: portrait
  ios_screen_orientation: portrait

  # تحسين الأداء عن طريق تقليل حجم الصورة
  branding: false
  branding_mode: bottom

  # تحسين الأداء عن طريق تقليل وقت الانتظار
  android_12_splash_delay: 0
  ios_splash_delay: 0

  # تقليل وقت الانتظار للشاشة الرئيسية
  android_splash_delay: 0
  ios_splash_delay: 0

  # تحسين الأداء عن طريق تقليل وقت الانتظار
  android_screen_orientation: portrait
  ios_screen_orientation: portrait
