import 'lesson_group.dart';

/// نموذج الدورة في مسار التعلم
/// كل دورة تتكون من مجموعة من الدروس (دفعة 1، محادثة 1، دفعة 2، مراجعة)
class Cycle {
  final int id; // معرف الدورة
  final String title; // عنوان الدورة (مثل "الدورة الأولى")
  final bool isLocked; // هل الدورة مغلقة
  final bool isCompleted; // هل تم إكمال الدورة
  final List<LessonGroup> lessonGroups; // مجموعات الدروس في الدورة
  final int totalSentences; // إجمالي عدد الجمل في الدورة
  final int completedSentences; // عدد الجمل المكتملة في الدورة
  final double accuracy; // متوسط دقة النطق في الدورة

  Cycle({
    required this.id,
    required this.title,
    required this.isLocked,
    required this.isCompleted,
    required this.lessonGroups,
    required this.totalSentences,
    required this.completedSentences,
    required this.accuracy,
  });

  /// إنشاء نسخة من الدورة من بيانات JSON
  factory Cycle.fromJson(Map<String, dynamic> json) {
    return Cycle(
      id: json['id'] as int,
      title: json['title'] as String,
      isLocked: json['isLocked'] as bool,
      isCompleted: json['isCompleted'] as bool,
      lessonGroups: (json['lessonGroups'] as List<dynamic>)
          .map((e) => LessonGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalSentences: json['totalSentences'] as int,
      completedSentences: json['completedSentences'] as int,
      accuracy: (json['accuracy'] as num).toDouble(),
    );
  }

  /// تحويل الدورة إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isLocked': isLocked,
      'isCompleted': isCompleted,
      'lessonGroups': lessonGroups.map((e) => e.toJson()).toList(),
      'totalSentences': totalSentences,
      'completedSentences': completedSentences,
      'accuracy': accuracy,
    };
  }

  /// إنشاء نسخة جديدة من الدورة مع تحديث بعض الخصائص
  Cycle copyWith({
    int? id,
    String? title,
    bool? isLocked,
    bool? isCompleted,
    List<LessonGroup>? lessonGroups,
    int? totalSentences,
    int? completedSentences,
    double? accuracy,
  }) {
    return Cycle(
      id: id ?? this.id,
      title: title ?? this.title,
      isLocked: isLocked ?? this.isLocked,
      isCompleted: isCompleted ?? this.isCompleted,
      lessonGroups: lessonGroups ?? this.lessonGroups,
      totalSentences: totalSentences ?? this.totalSentences,
      completedSentences: completedSentences ?? this.completedSentences,
      accuracy: accuracy ?? this.accuracy,
    );
  }

  /// حساب ما إذا كانت الدورة مكتملة بناءً على مجموعات الدروس
  bool calculateIsCompleted() {
    return lessonGroups.every((group) => group.isCompleted);
  }

  /// حساب إجمالي عدد الجمل في الدورة
  int calculateTotalSentences() {
    return lessonGroups.fold(0, (sum, group) => sum + group.totalSentences);
  }

  /// حساب عدد الجمل المكتملة في الدورة
  int calculateCompletedSentences() {
    return lessonGroups.fold(0, (sum, group) => sum + group.completedSentences);
  }

  /// حساب متوسط دقة النطق في الدورة
  double calculateAccuracy() {
    if (lessonGroups.isEmpty) return 0.0;
    
    double totalAccuracy = 0.0;
    int groupsWithAccuracy = 0;
    
    for (var group in lessonGroups) {
      if (group.completedSentences > 0) {
        totalAccuracy += group.accuracy;
        groupsWithAccuracy++;
      }
    }
    
    return groupsWithAccuracy > 0 ? totalAccuracy / groupsWithAccuracy : 0.0;
  }

  @override
  String toString() {
    return 'Cycle{id: $id, title: $title, isLocked: $isLocked, isCompleted: $isCompleted, lessonGroups: $lessonGroups, totalSentences: $totalSentences, completedSentences: $completedSentences, accuracy: $accuracy}';
  }
}
