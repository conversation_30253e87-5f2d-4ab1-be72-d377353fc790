import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart' show debugPrint;

/// إعداد بيانات المستويات والنقاط في Firebase
class FirebaseSetup {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// إعداد بيانات المستويات الافتراضية
  static Future<void> setupLevelsData() async {
    try {
      // التحقق من وجود المستويات
      final levelsSnapshot = await _firestore.collection('levels').get();
      if (levelsSnapshot.docs.isNotEmpty) {
        debugPrint('المستويات موجودة بالفعل');
        return;
      }

      // إنشاء المستوى 1 (سهل)
      final level1Ref = _firestore.collection('levels').doc('1');
      await level1Ref.set({
        'title': 'المبتدئ',
        'requiredSentences': 420, // 60 جملة في اليوم × 7 أيام
        'totalEducationalPoints': 1260, // 420 جملة × 3 نقاط في المتوسط
        'difficulty': 'easy', // مستوى الصعوبة: سهل
      });

      // إنشاء دورات المستوى 1 (7 دورات)
      final level1CyclesRef = level1Ref.collection('cycles');

      // إنشاء الدورة الأولى
      await level1CyclesRef.doc('1').set({
        'title': 'الدورة الأولى',
        'isLocked': false,
        'isCompleted': false,
        'totalSentences': 40, // مجموع جمل المجموعات
        'completedSentences': 0,
        'accuracy': 0.0,
      });

      // إنشاء مجموعات الدورة الأولى
      final cycle1GroupsRef =
          level1CyclesRef.doc('1').collection('lessonGroups');

      // دفعة 1 في الدورة 1
      await cycle1GroupsRef.doc('1').set({
        'id': 1,
        'cycleId': 1,
        'globalId': 1, // معرف فريد عام
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': false,
        'routePath': '/daily-sentences',
      });

      // محادثة 1 في الدورة 1
      await cycle1GroupsRef.doc('2').set({
        'id': 2,
        'cycleId': 1,
        'globalId': 2,
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true, // مغلقة حتى يتم إكمال الدفعة 1
        'routePath': '/conversation',
      });

      // دفعة 2 في الدورة 1
      await cycle1GroupsRef.doc('3').set({
        'id': 3,
        'cycleId': 1,
        'globalId': 3,
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true, // مغلقة حتى يتم إكمال المحادثة 1
        'routePath': '/daily-sentences',
      });

      // مراجعة في الدورة 1
      await cycle1GroupsRef.doc('4').set({
        'id': 4,
        'cycleId': 1,
        'globalId': 4,
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true, // مغلقة حتى يتم إكمال الدفعة 2
        'routePath': '/review',
      });

      // إنشاء الدورة الثانية
      await level1CyclesRef.doc('2').set({
        'title': 'الدورة الثانية',
        'isLocked': true, // مغلقة حتى يتم إكمال الدورة الأولى
        'isCompleted': false,
        'totalSentences': 40,
        'completedSentences': 0,
        'accuracy': 0.0,
      });

      // إنشاء مجموعات الدورة الثانية
      final cycle2GroupsRef =
          level1CyclesRef.doc('2').collection('lessonGroups');

      // دفعة 1 في الدورة 2
      await cycle2GroupsRef.doc('1').set({
        'id': 1,
        'cycleId': 2,
        'globalId': 5,
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': false,
        'routePath': '/daily-sentences',
      });

      // محادثة 1 في الدورة 2
      await cycle2GroupsRef.doc('2').set({
        'id': 2,
        'cycleId': 2,
        'globalId': 6,
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/conversation',
      });

      // دفعة 2 في الدورة 2
      await cycle2GroupsRef.doc('3').set({
        'id': 3,
        'cycleId': 2,
        'globalId': 7,
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/daily-sentences',
      });

      // مراجعة في الدورة 2
      await cycle2GroupsRef.doc('4').set({
        'id': 4,
        'cycleId': 2,
        'globalId': 8,
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/review',
      });

      // إنشاء باقي الدورات (3-7) بنفس الهيكل
      for (int cycleId = 3; cycleId <= 7; cycleId++) {
        await level1CyclesRef.doc(cycleId.toString()).set({
          'title': 'الدورة $cycleId',
          'isLocked': true,
          'isCompleted': false,
          'totalSentences': 40,
          'completedSentences': 0,
          'accuracy': 0.0,
        });

        final cycleGroupsRef =
            level1CyclesRef.doc(cycleId.toString()).collection('lessonGroups');

        // دفعة 1
        await cycleGroupsRef.doc('1').set({
          'id': 1,
          'cycleId': cycleId,
          'globalId': (cycleId - 1) * 4 + 1,
          'title': 'دفعة 1',
          'type': 'sentenceBatch',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': false,
          'routePath': '/daily-sentences',
        });

        // محادثة 1
        await cycleGroupsRef.doc('2').set({
          'id': 2,
          'cycleId': cycleId,
          'globalId': (cycleId - 1) * 4 + 2,
          'title': 'محادثة 1',
          'type': 'conversation',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/conversation',
        });

        // دفعة 2
        await cycleGroupsRef.doc('3').set({
          'id': 3,
          'cycleId': cycleId,
          'globalId': (cycleId - 1) * 4 + 3,
          'title': 'دفعة 2',
          'type': 'sentenceBatch',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/daily-sentences',
        });

        // مراجعة
        await cycleGroupsRef.doc('4').set({
          'id': 4,
          'cycleId': cycleId,
          'globalId': (cycleId - 1) * 4 + 4,
          'title': 'مراجعة',
          'type': 'review',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/review',
        });
      }

      // إنشاء المستوى 2 (متوسط)
      final level2Ref = _firestore.collection('levels').doc('2');
      await level2Ref.set({
        'title': 'المتوسط',
        'requiredSentences': 350,
        'totalEducationalPoints': 1050,
        'difficulty': 'medium', // مستوى الصعوبة: متوسط
      });

      // إنشاء دورات المستوى 2 (5 دورات)
      final level2CyclesRef = level2Ref.collection('cycles');

      // إنشاء 5 دورات للمستوى 2
      for (int cycleId = 1; cycleId <= 5; cycleId++) {
        await level2CyclesRef.doc('$cycleId').set({
          'title': 'الدورة $cycleId',
          'isLocked': cycleId > 1, // الدورة الأولى فقط مفتوحة
          'isCompleted': false,
          'totalSentences': 50,
          'completedSentences': 0,
          'accuracy': 0.0,
        });
      }

      // إنشاء مجموعات الدورة الأولى للمستوى 2
      final cycle1Level2GroupsRef =
          level2CyclesRef.doc('1').collection('lessonGroups');

      // دفعة 1 في الدورة 1 للمستوى 2
      await cycle1Level2GroupsRef.doc('1').set({
        'id': 1,
        'cycleId': 1,
        'globalId': 201,
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': false,
        'routePath': '/daily-sentences',
      });

      // محادثة 1 في الدورة 1 للمستوى 2
      await cycle1Level2GroupsRef.doc('2').set({
        'id': 2,
        'cycleId': 1,
        'globalId': 202,
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/conversation',
      });

      // محادثة 2 في الدورة 1 للمستوى 2
      await cycle1Level2GroupsRef.doc('3').set({
        'id': 3,
        'cycleId': 1,
        'globalId': 203,
        'title': 'محادثة 2',
        'type': 'conversation',
        'totalSentences': 10,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/conversation',
      });

      // دفعة 2 في الدورة 1 للمستوى 2
      await cycle1Level2GroupsRef.doc('4').set({
        'id': 4,
        'cycleId': 1,
        'globalId': 204,
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/daily-sentences',
      });

      // مراجعة في الدورة 1 للمستوى 2
      await cycle1Level2GroupsRef.doc('5').set({
        'id': 5,
        'cycleId': 1,
        'globalId': 205,
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 50,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/review',
      });

      // إنشاء مجموعات الدروس للدورات 2-5 للمستوى 2
      for (int cycleId = 2; cycleId <= 5; cycleId++) {
        final cycleGroupsRef =
            level2CyclesRef.doc('$cycleId').collection('lessonGroups');

        // دفعة 1
        await cycleGroupsRef.doc('1').set({
          'id': 1,
          'cycleId': cycleId,
          'globalId': 200 + cycleId * 10 + 1,
          'title': 'دفعة 1',
          'type': 'sentenceBatch',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': false,
          'routePath': '/daily-sentences',
        });

        // محادثة 1
        await cycleGroupsRef.doc('2').set({
          'id': 2,
          'cycleId': cycleId,
          'globalId': 200 + cycleId * 10 + 2,
          'title': 'محادثة 1',
          'type': 'conversation',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/conversation',
        });

        // محادثة 2
        await cycleGroupsRef.doc('3').set({
          'id': 3,
          'cycleId': cycleId,
          'globalId': 200 + cycleId * 10 + 3,
          'title': 'محادثة 2',
          'type': 'conversation',
          'totalSentences': 10,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/conversation',
        });

        // دفعة 2
        await cycleGroupsRef.doc('4').set({
          'id': 4,
          'cycleId': cycleId,
          'globalId': 200 + cycleId * 10 + 4,
          'title': 'دفعة 2',
          'type': 'sentenceBatch',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/daily-sentences',
        });

        // مراجعة
        await cycleGroupsRef.doc('5').set({
          'id': 5,
          'cycleId': cycleId,
          'globalId': 200 + cycleId * 10 + 5,
          'title': 'مراجعة',
          'type': 'review',
          'totalSentences': 50,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/review',
        });
      }

      // إنشاء المستوى 3 (صعب)
      final level3Ref = _firestore.collection('levels').doc('3');
      await level3Ref.set({
        'title': 'المتقدم',
        'requiredSentences': 300,
        'totalEducationalPoints': 1200,
        'difficulty': 'hard', // مستوى الصعوبة: صعب
      });

      // إنشاء دورات المستوى 3 (3 دورات)
      final level3CyclesRef = level3Ref.collection('cycles');

      // إنشاء 3 دورات للمستوى 3
      for (int cycleId = 1; cycleId <= 3; cycleId++) {
        await level3CyclesRef.doc('$cycleId').set({
          'title': 'الدورة $cycleId',
          'isLocked': cycleId > 1, // الدورة الأولى فقط مفتوحة
          'isCompleted': false,
          'totalSentences': 60,
          'completedSentences': 0,
          'accuracy': 0.0,
        });
      }

      // إنشاء مجموعات الدورة الأولى للمستوى 3
      final cycle1Level3GroupsRef =
          level3CyclesRef.doc('1').collection('lessonGroups');

      // دفعة 1 في الدورة 1 للمستوى 3
      await cycle1Level3GroupsRef.doc('1').set({
        'id': 1,
        'cycleId': 1,
        'globalId': 301, // معرف فريد عام للمستوى 3
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': false,
        'routePath': '/daily-sentences',
      });

      // محادثة 1 في الدورة 1 للمستوى 3
      await cycle1Level3GroupsRef.doc('2').set({
        'id': 2,
        'cycleId': 1,
        'globalId': 302,
        'title': 'محادثة متقدمة 1',
        'type': 'conversation',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/conversation',
      });

      // دفعة 2 في الدورة 1 للمستوى 3
      await cycle1Level3GroupsRef.doc('3').set({
        'id': 3,
        'cycleId': 1,
        'globalId': 303,
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/daily-sentences',
      });

      // مراجعة في الدورة 1 للمستوى 3
      await cycle1Level3GroupsRef.doc('4').set({
        'id': 4,
        'cycleId': 1,
        'globalId': 304,
        'title': 'مراجعة متقدمة',
        'type': 'review',
        'totalSentences': 15,
        'completedSentences': 0,
        'accuracy': 0.0,
        'isCompleted': false,
        'isLocked': true,
        'routePath': '/review',
      });

      // إنشاء مجموعات الدروس للدورات 2-3 للمستوى 3
      for (int cycleId = 2; cycleId <= 3; cycleId++) {
        final cycleGroupsRef =
            level3CyclesRef.doc('$cycleId').collection('lessonGroups');

        // دفعة 1
        await cycleGroupsRef.doc('1').set({
          'id': 1,
          'cycleId': cycleId,
          'globalId': 300 + cycleId * 10 + 1,
          'title': 'دفعة 1',
          'type': 'sentenceBatch',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': false,
          'routePath': '/daily-sentences',
        });

        // محادثة متقدمة 1
        await cycleGroupsRef.doc('2').set({
          'id': 2,
          'cycleId': cycleId,
          'globalId': 300 + cycleId * 10 + 2,
          'title': 'محادثة متقدمة 1',
          'type': 'conversation',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/conversation',
        });

        // دفعة 2
        await cycleGroupsRef.doc('3').set({
          'id': 3,
          'cycleId': cycleId,
          'globalId': 300 + cycleId * 10 + 3,
          'title': 'دفعة 2',
          'type': 'sentenceBatch',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/daily-sentences',
        });

        // مراجعة متقدمة
        await cycleGroupsRef.doc('4').set({
          'id': 4,
          'cycleId': cycleId,
          'globalId': 300 + cycleId * 10 + 4,
          'title': 'مراجعة متقدمة',
          'type': 'review',
          'totalSentences': 15,
          'completedSentences': 0,
          'accuracy': 0.0,
          'isCompleted': false,
          'isLocked': true,
          'routePath': '/review',
        });
      }

      debugPrint('تم إعداد بيانات المستويات بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعداد بيانات المستويات: $e');
    }
  }

  /// إعداد إعدادات النقاط الافتراضية
  static Future<void> setupPointsSettings() async {
    try {
      // التحقق من وجود إعدادات النقاط
      final settingsDoc =
          await _firestore.collection('settings').doc('points').get();
      if (settingsDoc.exists) {
        debugPrint('إعدادات النقاط موجودة بالفعل');
        return;
      }

      // إنشاء إعدادات النقاط
      await _firestore.collection('settings').doc('points').set({
        // نقاط الأنشطة التعليمية
        'read_sentence': 10, // قراءة جملة جديدة
        'memory_test': 5, // اختبار الحفظ
        'complete_batch': 50, // إكمال دفعة
        'complete_conversation': 50, // إكمال محادثة
        'review_sentence': 3, // مراجعة جملة

        // نقاط أنشطة المكافأة
        'daily_login': 20, // تسجيل الدخول اليومي
        'streak_3_days': 50, // تسلسل 3 أيام
        'streak_7_days': 100, // تسلسل 7 أيام
        'streak_14_days': 200, // تسلسل 14 يوم
        'watch_ad': 50, // مشاهدة إعلان
        'share_link': 100, // مشاركة رابط
        'successful_referral': 500, // إحالة ناجحة
      });

      debugPrint('تم إعداد إعدادات النقاط بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعداد إعدادات النقاط: $e');
    }
  }

  /// إعداد تقدم المستخدم الافتراضي
  static Future<void> setupUserProgress() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('لم يتم تسجيل الدخول');
        return;
      }

      // التحقق من وجود تقدم للمستخدم
      final progressDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .get();

      if (progressDoc.exists) {
        debugPrint('تقدم المستخدم موجود بالفعل');
        // حتى لو كان التقدم موجودًا، نتأكد من وجود مجموعة المستخدم في Firestore
        await _setupUserGroup(user.uid);
        return;
      }

      // إنشاء تقدم المستخدم الافتراضي
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .set({
        'currentLevel': 1,
        'level_1_unlocked': true,
        'level_2_unlocked': false,
        'level_3_unlocked': false,
        'level_1_completedSentences': 0,
        'level_1_points': 0,
        'level_2_points': 0,
        'level_3_points': 0,
      });

      // إنشاء نقاط المستخدم الافتراضية
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('points')
          .doc('summary')
          .set({
        'educational': 0,
        'reward': 0,
        'total': 0,
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // إنشاء سجل نقاط المستخدم
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('points')
          .doc('history')
          .set({
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // إنشاء مجموعة المستخدم
      await _setupUserGroup(user.uid);

      debugPrint('تم إعداد تقدم المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعداد تقدم المستخدم: $e');
    }
  }

  /// إنشاء مجموعة المستخدم في Firestore
  static Future<void> _setupUserGroup(String userId) async {
    try {
      // التحقق من وجود مجموعة للمستخدم
      final userGroupDoc =
          await _firestore.collection('userGroups').doc(userId).get();

      if (userGroupDoc.exists) {
        debugPrint('مجموعة المستخدم موجودة بالفعل');
        return;
      }

      // إنشاء مجموعة المستخدم
      await _firestore.collection('userGroups').doc(userId).set({
        'userId': userId,
        'levelId': 1, // المستوى الحالي
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // إنشاء مجموعة المستوى للمستخدم
      await _firestore
          .collection('levelGroups')
          .doc('1') // المستوى 1
          .collection('users')
          .doc(userId)
          .set({
        'userId': userId,
        'addedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('تم إنشاء مجموعة المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء مجموعة المستخدم: $e');
    }
  }

  /// إعداد جميع البيانات الافتراضية
  static Future<void> setupAllData() async {
    try {
      debugPrint('بدء إعداد جميع البيانات الافتراضية...');

      // إعداد المستويات أولاً
      await setupLevelsData();

      // إعداد إعدادات النقاط
      await setupPointsSettings();

      // إعداد تقدم المستخدم (إذا كان مسجل الدخول)
      await setupUserProgress();

      debugPrint('تم إعداد جميع البيانات الافتراضية بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعداد البيانات الافتراضية: $e');
    }
  }

  /// إعادة تعيين تقدم المستخدم (للاختبار فقط)
  static Future<void> resetUserProgress() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('لم يتم تسجيل الدخول');
        return;
      }

      // حذف تقدم المستخدم
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('progress')
          .doc('levels')
          .delete();

      // حذف نقاط المستخدم
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('points')
          .doc('summary')
          .delete();

      // حذف سجل نقاط المستخدم
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('points')
          .doc('history')
          .delete();

      // حذف مجموعة المستخدم
      await _firestore.collection('userGroups').doc(user.uid).delete();

      // حذف المستخدم من مجموعة المستوى
      await _firestore
          .collection('levelGroups')
          .doc('1')
          .collection('users')
          .doc(user.uid)
          .delete();

      // إعادة إنشاء تقدم المستخدم
      await setupUserProgress();

      debugPrint('تم إعادة تعيين تقدم المستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين تقدم المستخدم: $e');
    }
  }
}
