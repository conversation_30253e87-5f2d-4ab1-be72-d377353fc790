{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\test05\\test05\\android\\app\\.cxx\\Debug\\4q4w1l6m\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\test05\\test05\\android\\app\\.cxx\\Debug\\4q4w1l6m\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "F:\\ANDROIDSDK01\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "F:\\ANDROIDSDK01\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}