import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'native_notification_service.dart';

/// A simplified notification service that stores notification settings
/// but doesn't actually schedule notifications due to compatibility issues
class SimpleNotificationService {
  // Singleton pattern
  static final SimpleNotificationService _instance =
      SimpleNotificationService._internal();
  factory SimpleNotificationService() => _instance;
  SimpleNotificationService._internal();

  // Firestore reference
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Firestore collection name
  static const String _notificationSettingsCollection = 'notificationSettings';

  // Shared preferences keys
  static const String unfinishedSentencesEnabledKey =
      'unfinished_sentences_notification_enabled';
  static const String newSentencesAddedEnabledKey =
      'new_sentences_added_notification_enabled';
  static const String morningReminderEnabledKey =
      'morning_reminder_notification_enabled';
  static const String newDayMotivationEnabledKey =
      'new_day_motivation_notification_enabled';

  static const String unfinishedSentencesTimeKey =
      'unfinished_sentences_notification_time';
  static const String morningReminderTimeKey =
      'morning_reminder_notification_time';
  static const String newDayMotivationTimeKey =
      'new_day_motivation_notification_time';

  // Initialize the notification service
  Future<void> init() async {
    // Set default notification preferences if not set
    await _initializeNotificationPreferences();
  }

  // Initialize notification preferences
  Future<void> _initializeNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Set default values if not already set
    if (!prefs.containsKey(unfinishedSentencesEnabledKey)) {
      await prefs.setBool(unfinishedSentencesEnabledKey, true);
    }

    if (!prefs.containsKey(newSentencesAddedEnabledKey)) {
      await prefs.setBool(newSentencesAddedEnabledKey, true);
    }

    if (!prefs.containsKey(morningReminderEnabledKey)) {
      await prefs.setBool(morningReminderEnabledKey, true);
    }

    if (!prefs.containsKey(newDayMotivationEnabledKey)) {
      await prefs.setBool(newDayMotivationEnabledKey, true);
    }

    // Set default times if not already set
    if (!prefs.containsKey(unfinishedSentencesTimeKey)) {
      // Default: 6:00 PM (18:00)
      await prefs.setString(unfinishedSentencesTimeKey, '18:00');
    }

    if (!prefs.containsKey(morningReminderTimeKey)) {
      // Default: 9:00 AM (09:00)
      await prefs.setString(morningReminderTimeKey, '09:00');
    }

    if (!prefs.containsKey(newDayMotivationTimeKey)) {
      // Default: 8:00 AM (08:00)
      await prefs.setString(newDayMotivationTimeKey, '08:00');
    }
  }

  // Get notification settings
  Future<Map<String, dynamic>> getNotificationSettings() async {
    try {
      // Check if user is authenticated
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Try to get settings from Firestore
      final docRef =
          _firestore.collection(_notificationSettingsCollection).doc(user.uid);
      final docSnapshot = await docRef.get();

      // If settings exist in Firestore, return them
      if (docSnapshot.exists) {
        final data = docSnapshot.data() as Map<String, dynamic>;
        debugPrint('Retrieved notification settings from Firestore: $data');
        return data;
      }

      // If settings don't exist in Firestore, get from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final settings = {
        'unfinishedSentencesEnabled':
            prefs.getBool(unfinishedSentencesEnabledKey) ?? true,
        'newSentencesAddedEnabled':
            prefs.getBool(newSentencesAddedEnabledKey) ?? true,
        'morningReminderEnabled':
            prefs.getBool(morningReminderEnabledKey) ?? true,
        'newDayMotivationEnabled':
            prefs.getBool(newDayMotivationEnabledKey) ?? true,
        'unfinishedSentencesTime':
            prefs.getString(unfinishedSentencesTimeKey) ?? '18:00',
        'morningReminderTime':
            prefs.getString(morningReminderTimeKey) ?? '09:00',
        'newDayMotivationTime':
            prefs.getString(newDayMotivationTimeKey) ?? '08:00',
      };

      // Save settings to Firestore for future use
      await docRef.set(settings);
      debugPrint('Saved notification settings to Firestore: $settings');

      return settings;
    } catch (e) {
      debugPrint('Error getting notification settings: $e');

      // Fallback to SharedPreferences if Firestore fails
      final prefs = await SharedPreferences.getInstance();
      return {
        'unfinishedSentencesEnabled':
            prefs.getBool(unfinishedSentencesEnabledKey) ?? true,
        'newSentencesAddedEnabled':
            prefs.getBool(newSentencesAddedEnabledKey) ?? true,
        'morningReminderEnabled':
            prefs.getBool(morningReminderEnabledKey) ?? true,
        'newDayMotivationEnabled':
            prefs.getBool(newDayMotivationEnabledKey) ?? true,
        'unfinishedSentencesTime':
            prefs.getString(unfinishedSentencesTimeKey) ?? '18:00',
        'morningReminderTime':
            prefs.getString(morningReminderTimeKey) ?? '09:00',
        'newDayMotivationTime':
            prefs.getString(newDayMotivationTimeKey) ?? '08:00',
      };
    }
  }

  // Update notification settings
  Future<void> updateNotificationSettings({
    bool? unfinishedSentencesEnabled,
    bool? newSentencesAddedEnabled,
    bool? morningReminderEnabled,
    bool? newDayMotivationEnabled,
    String? unfinishedSentencesTime,
    String? morningReminderTime,
    String? newDayMotivationTime,
  }) async {
    try {
      // Check if user is authenticated
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get current settings
      final currentSettings = await getNotificationSettings();

      // Update settings with new values
      final updatedSettings = Map<String, dynamic>.from(currentSettings);

      if (unfinishedSentencesEnabled != null) {
        updatedSettings['unfinishedSentencesEnabled'] =
            unfinishedSentencesEnabled;
      }

      if (newSentencesAddedEnabled != null) {
        updatedSettings['newSentencesAddedEnabled'] = newSentencesAddedEnabled;
      }

      if (morningReminderEnabled != null) {
        updatedSettings['morningReminderEnabled'] = morningReminderEnabled;
      }

      if (newDayMotivationEnabled != null) {
        updatedSettings['newDayMotivationEnabled'] = newDayMotivationEnabled;
      }

      if (unfinishedSentencesTime != null) {
        updatedSettings['unfinishedSentencesTime'] = unfinishedSentencesTime;
      }

      if (morningReminderTime != null) {
        updatedSettings['morningReminderTime'] = morningReminderTime;
      }

      if (newDayMotivationTime != null) {
        updatedSettings['newDayMotivationTime'] = newDayMotivationTime;
      }

      // Save updated settings to Firestore
      await _firestore
          .collection(_notificationSettingsCollection)
          .doc(user.uid)
          .set(updatedSettings);

      debugPrint(
          'Updated notification settings in Firestore: $updatedSettings');

      // Also update SharedPreferences for local access
      final prefs = await SharedPreferences.getInstance();

      // Update enabled/disabled status
      if (unfinishedSentencesEnabled != null) {
        await prefs.setBool(
            unfinishedSentencesEnabledKey, unfinishedSentencesEnabled);
      }

      if (newSentencesAddedEnabled != null) {
        await prefs.setBool(
            newSentencesAddedEnabledKey, newSentencesAddedEnabled);
      }

      if (morningReminderEnabled != null) {
        await prefs.setBool(morningReminderEnabledKey, morningReminderEnabled);
      }

      if (newDayMotivationEnabled != null) {
        await prefs.setBool(
            newDayMotivationEnabledKey, newDayMotivationEnabled);
      }

      // Update times
      if (unfinishedSentencesTime != null) {
        await prefs.setString(
            unfinishedSentencesTimeKey, unfinishedSentencesTime);
      }

      if (morningReminderTime != null) {
        await prefs.setString(morningReminderTimeKey, morningReminderTime);
      }

      if (newDayMotivationTime != null) {
        await prefs.setString(newDayMotivationTimeKey, newDayMotivationTime);
      }
    } catch (e) {
      debugPrint('Error updating notification settings: $e');

      // Fallback to SharedPreferences if Firestore fails
      final prefs = await SharedPreferences.getInstance();

      // Update enabled/disabled status
      if (unfinishedSentencesEnabled != null) {
        await prefs.setBool(
            unfinishedSentencesEnabledKey, unfinishedSentencesEnabled);
      }

      if (newSentencesAddedEnabled != null) {
        await prefs.setBool(
            newSentencesAddedEnabledKey, newSentencesAddedEnabled);
      }

      if (morningReminderEnabled != null) {
        await prefs.setBool(morningReminderEnabledKey, morningReminderEnabled);
      }

      if (newDayMotivationEnabled != null) {
        await prefs.setBool(
            newDayMotivationEnabledKey, newDayMotivationEnabled);
      }

      // Update times
      if (unfinishedSentencesTime != null) {
        await prefs.setString(
            unfinishedSentencesTimeKey, unfinishedSentencesTime);
      }

      if (morningReminderTime != null) {
        await prefs.setString(morningReminderTimeKey, morningReminderTime);
      }

      if (newDayMotivationTime != null) {
        await prefs.setString(newDayMotivationTimeKey, newDayMotivationTime);
      }

      // Re-throw the exception to be handled by the caller
      rethrow;
    }
  }

  // Parse time string to TimeOfDay
  TimeOfDay parseTimeString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  // Format TimeOfDay to string
  String formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  // Methods for scheduling notifications using NativeNotificationService

  Future<void> scheduleAllNotifications() async {
    try {
      final nativeService = NativeNotificationService();
      await nativeService.scheduleAllNotifications();
      debugPrint('All notifications scheduled successfully');
    } catch (e) {
      debugPrint('Error scheduling all notifications: $e');
    }
  }

  Future<void> scheduleUnfinishedSentencesNotification() async {
    try {
      final nativeService = NativeNotificationService();
      await nativeService.scheduleUnfinishedSentencesNotification();
      debugPrint('Unfinished sentences notification scheduled');
    } catch (e) {
      debugPrint('Error scheduling unfinished sentences notification: $e');
    }
  }

  Future<void> showNewSentencesAddedNotification() async {
    try {
      final nativeService = NativeNotificationService();
      await nativeService.showNewSentencesAddedNotification();
      debugPrint('New sentences added notification shown');
    } catch (e) {
      debugPrint('Error showing new sentences added notification: $e');
    }
  }

  Future<void> scheduleMorningReminderNotification() async {
    try {
      final nativeService = NativeNotificationService();
      await nativeService.scheduleMorningReminderNotification();
      debugPrint('Morning reminder notification scheduled');
    } catch (e) {
      debugPrint('Error scheduling morning reminder notification: $e');
    }
  }

  Future<void> scheduleNewDayMotivationNotification() async {
    try {
      final nativeService = NativeNotificationService();
      await nativeService.scheduleNewDayMotivationNotification();
      debugPrint('New day motivation notification scheduled');
    } catch (e) {
      debugPrint('Error scheduling new day motivation notification: $e');
    }
  }
}
