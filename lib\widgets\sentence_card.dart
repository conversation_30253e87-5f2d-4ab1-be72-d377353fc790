import 'package:flutter/material.dart';
import '../models/sentence_model.dart';
import 'package:share_plus/share_plus.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';

class SentenceCard extends StatelessWidget {
  final SentenceModel sentence;
  final VoidCallback onFavoritePressed;
  final bool showCategory;
  final bool showShare;

  const SentenceCard({
    super.key,
    required this.sentence,
    required this.onFavoritePressed,
    this.showCategory = false,
    this.showShare = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      child: InkWell(
        onTap: () {
          // Mark as read when tapped
          Provider.of<SentenceViewModel>(context, listen: false)
              .markAsRead(sentence);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showCategory) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    sentence.category,
                    style: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
              ],
              Text(
                sentence.arabicText,
                style: const TextStyle(
                  fontSize: 18,
                  height: 1.5,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
              const SizedBox(height: 8),
              Text(
                sentence.englishText,
                style: TextStyle(
                  fontSize: 16,
                  height: 1.5,
                  color: theme.textTheme.bodyMedium?.color
                      ?.withAlpha(204), // 0.8 * 255 = 204
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(
                          sentence.isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: sentence.isFavorite ? Colors.red : null,
                        ),
                        onPressed: onFavoritePressed,
                      ),
                      if (showShare)
                        IconButton(
                          icon: const Icon(Icons.share),
                          onPressed: () {
                            Share.share(
                              '${sentence.arabicText}\n\n${sentence.englishText}',
                            );
                          },
                        ),
                    ],
                  ),
                  if (sentence.isRead)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green
                            .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.green[700],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'تمت القراءة',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
