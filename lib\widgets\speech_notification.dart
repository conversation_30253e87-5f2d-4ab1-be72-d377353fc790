import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

enum NotificationType {
  success,
  error,
  warning,
  info,
  processing,
}

class SpeechNotification extends StatelessWidget {
  final String message;
  final NotificationType type;
  final Duration duration;
  final VoidCallback? onDismiss;

  const SpeechNotification({
    Key? key,
    required this.message,
    this.type = NotificationType.info,
    this.duration = const Duration(seconds: 2),
    this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: const EdgeInsets.only(top: 20.0),
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            decoration: BoxDecoration(
              color: _getBackgroundColor(),
              borderRadius: BorderRadius.circular(8.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(50),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _getIcon(),
                const SizedBox(width: 12),
                Flexible(
                  child: Text(
                    message,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (type == NotificationType.processing) ...[
                  const SizedBox(width: 12),
                  _buildProcessingIndicator(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _getIcon() {
    switch (type) {
      case NotificationType.success:
        return const Icon(
          Icons.check_circle,
          color: Colors.white,
        );
      case NotificationType.error:
        return const Icon(
          Icons.error,
          color: Colors.white,
        );
      case NotificationType.warning:
        return const Icon(
          Icons.warning,
          color: Colors.white,
        );
      case NotificationType.info:
        return const Icon(
          Icons.info,
          color: Colors.white,
        );
      case NotificationType.processing:
        return const Icon(
          Icons.mic,
          color: Colors.white,
        );
    }
  }

  Color _getBackgroundColor() {
    switch (type) {
      case NotificationType.success:
        return Colors.green;
      case NotificationType.error:
        return Colors.red;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.processing:
        return AppTheme.primaryColor;
    }
  }

  Widget _buildProcessingIndicator() {
    return const SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
      ),
    );
  }

  // Método estático para mostrar la notificación
  static void show({
    required BuildContext context,
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 2),
    VoidCallback? onDismiss,
  }) {
    // Guardar el contexto actual
    final BuildContext currentContext = context;

    showDialog(
      context: currentContext,
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        // Programar cierre automático después de la duración especificada
        Future.delayed(duration, () {
          if (Navigator.of(dialogContext).canPop()) {
            Navigator.of(dialogContext).pop();
            if (onDismiss != null) {
              onDismiss();
            }
          }
        });

        return SpeechNotification(
          message: message,
          type: type,
          duration: duration,
          onDismiss: onDismiss,
        );
      },
    );
  }
}
