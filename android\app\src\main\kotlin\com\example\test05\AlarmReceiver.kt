package com.example.test05

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast

class AlarmReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("AlarmReceiver", "Received alarm broadcast")

        // Extract notification data from intent
        val title = intent.getStringExtra("title") ?: "Notification"
        val body = intent.getStringExtra("body") ?: "This is a notification"
        val channelId = intent.getStringExtra("channelId") ?: "default_channel"
        val channelName = intent.getStringExtra("channelName") ?: "Default Channel"
        val channelDescription = intent.getStringExtra("channelDescription") ?: "Default notification channel"

        // Show the notification
        val notificationService = NotificationService(context)
        notificationService.showNotification(title, body, channelId, channelName, channelDescription)

        // Also show a toast message for immediate visibility
        try {
            val handler = Handler(Looper.getMainLooper())
            handler.post {
                Toast.makeText(context, "$title: $body", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        // Make sure the app is launched - but only if it's not already in foreground
        // This can cause issues if we try to force launch the app every time
        // So we'll comment this out for now
        /*
        try {
            val launchIntent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            launchIntent?.let {
                it.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                context.startActivity(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        */
    }
}
