import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/message_model.dart';
import '../services/conversation_service.dart';
import '../services/category_service.dart';

class CreateConversationScreen extends StatefulWidget {
  const CreateConversationScreen({Key? key}) : super(key: key);

  @override
  State<CreateConversationScreen> createState() =>
      _CreateConversationScreenState();
}

class _CreateConversationScreenState extends State<CreateConversationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  String _selectedCategory = 'greetings';
  String _selectedDifficulty = 'medium';
  String _selectedLevel = '1'; // المستوى الافتراضي هو 1
  final List<MessageModel> _messages = [];
  bool _isLoading = false;
  bool _isLoadingCategories = true;
  List<Map<String, dynamic>> _categories = [];

  final _arabicTextController = TextEditingController();
  final _englishTextController = TextEditingController();
  bool _isPersonA = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoadingCategories = true;
    });

    try {
      final categoryService =
          Provider.of<CategoryService>(context, listen: false);
      final categories = await categoryService.getCategories();

      setState(() {
        _categories = categories;
        if (categories.isNotEmpty) {
          _selectedCategory = categories[0]['id'];
        }
        _isLoadingCategories = false;
      });
    } catch (e) {
      // Si hay un error, usar categorías predeterminadas
      setState(() {
        _categories = [
          {'id': 'greetings', 'name': 'التحيات', 'nameEn': 'Greetings'},
          {'id': 'travel', 'name': 'السفر', 'nameEn': 'Travel'},
          {'id': 'food', 'name': 'الطعام', 'nameEn': 'Food'},
          {'id': 'shopping', 'name': 'التسوق', 'nameEn': 'Shopping'},
          {'id': 'work', 'name': 'العمل', 'nameEn': 'Work'},
        ];
        _isLoadingCategories = false;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _arabicTextController.dispose();
    _englishTextController.dispose();
    super.dispose();
  }

  void _addMessage() {
    if (_arabicTextController.text.isEmpty ||
        _englishTextController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال النص العربي والإنجليزي')),
      );
      return;
    }

    setState(() {
      _messages.add(
        MessageModel(
          id: const Uuid().v4(),
          arabicText: _arabicTextController.text,
          englishText: _englishTextController.text,
          isPersonA: _isPersonA,
          createdAt: DateTime.now(),
          readBy: {},
          testResults: {},
        ),
      );

      // Alternar entre persona A y B para el siguiente mensaje
      _isPersonA = !_isPersonA;

      // Limpiar los campos
      _arabicTextController.clear();
      _englishTextController.clear();
    });
  }

  void _removeMessage(int index) {
    setState(() {
      _messages.removeAt(index);
    });
  }

  Future<void> _saveConversation() async {
    if (!_formKey.currentState!.validate()) return;

    if (_messages.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة رسالتين على الأقل')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);

      final conversationId = await conversationService.createConversation(
        _titleController.text,
        _selectedCategory,
        _selectedDifficulty,
        _messages,
        _selectedLevel,
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ المحادثة بنجاح')),
        );
        Navigator.pop(context, conversationId);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء محادثة جديدة'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(16),
                // Envolver en SingleChildScrollView para evitar overflow cuando aparece el teclado
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Información de la conversación
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان المحادثة',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال عنوان للمحادثة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // الفئة
                      _isLoadingCategories
                          ? const Center(child: CircularProgressIndicator())
                          : DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'الفئة',
                                border: OutlineInputBorder(),
                              ),
                              value: _selectedCategory,
                              items: _categories
                                  .map<DropdownMenuItem<String>>((category) {
                                return DropdownMenuItem<String>(
                                  value: category['id'] as String,
                                  child: Text(category['name'] as String),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategory = value!;
                                });
                              },
                            ),
                      const SizedBox(height: 16),

                      // مستوى الصعوبة والمستوى
                      Row(
                        children: [
                          // مستوى الصعوبة
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'مستوى الصعوبة',
                                border: OutlineInputBorder(),
                              ),
                              value: _selectedDifficulty,
                              items: const [
                                DropdownMenuItem(
                                    value: 'easy', child: Text('سهل')),
                                DropdownMenuItem(
                                    value: 'medium', child: Text('متوسط')),
                                DropdownMenuItem(
                                    value: 'hard', child: Text('صعب')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedDifficulty = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 16),

                          // المستوى
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'المستوى',
                                border: OutlineInputBorder(),
                              ),
                              value: _selectedLevel,
                              items: const [
                                DropdownMenuItem(
                                    value: '1', child: Text('المستوى 1')),
                                DropdownMenuItem(
                                    value: '2', child: Text('المستوى 2')),
                                DropdownMenuItem(
                                    value: '3', child: Text('المستوى 3')),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedLevel = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Lista de mensajes
                      const Text(
                        'الرسائل',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Lista de mensajes con altura fija
                      Container(
                        height: 200, // Altura fija para la lista
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: _messages.isEmpty
                            ? const Center(
                                child: Text('لا توجد رسائل. أضف رسائل أدناه.'))
                            : ListView.builder(
                                shrinkWrap: true,
                                itemCount: _messages.length,
                                itemBuilder: (context, index) {
                                  final message = _messages[index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    child: ListTile(
                                      leading: Icon(
                                        message.isPersonA
                                            ? Icons.person
                                            : Icons.person_outline,
                                        color: message.isPersonA
                                            ? Colors.blue
                                            : Colors.green,
                                      ),
                                      title: Text(
                                        message.arabicText,
                                        textDirection: TextDirection.rtl,
                                      ),
                                      subtitle: Text(
                                        message.englishText,
                                        textDirection: TextDirection.ltr,
                                      ),
                                      trailing: IconButton(
                                        icon: const Icon(Icons.delete,
                                            color: Colors.red),
                                        onPressed: () => _removeMessage(index),
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),

                      // Formulario para añadir mensajes
                      const Divider(),
                      Row(
                        children: [
                          // Selector de persona
                          DropdownButton<bool>(
                            value: _isPersonA,
                            items: const [
                              DropdownMenuItem(
                                  value: true, child: Text('شخص أ')),
                              DropdownMenuItem(
                                  value: false, child: Text('شخص ب')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _isPersonA = value!;
                              });
                            },
                          ),
                          const SizedBox(width: 16),
                          // Contador de mensajes
                          Text(
                            '${_messages.length}/10 رسائل',
                            style: TextStyle(
                              color: _messages.length >= 10
                                  ? Colors.red
                                  : Colors.black,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Campos de texto para el nuevo mensaje
                      TextFormField(
                        controller: _arabicTextController,
                        decoration: const InputDecoration(
                          labelText: 'النص العربي',
                          border: OutlineInputBorder(),
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _englishTextController,
                        decoration: const InputDecoration(
                          labelText: 'النص الإنجليزي',
                          border: OutlineInputBorder(),
                        ),
                        textDirection: TextDirection.ltr,
                      ),
                      const SizedBox(height: 8),

                      // Botón para añadir mensaje
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed:
                              _messages.length >= 10 ? null : _addMessage,
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة رسالة'),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Botón para guardar la conversación
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _saveConversation,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('حفظ المحادثة'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
