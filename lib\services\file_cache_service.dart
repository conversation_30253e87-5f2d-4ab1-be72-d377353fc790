import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

class FileCacheService {
  late Directory _cacheDir;

  FileCacheService() {
    _initCacheDir();
  }

  Future<void> _initCacheDir() async {
    final appDir = await getApplicationDocumentsDirectory();
    _cacheDir = Directory('${appDir.path}/cache');
    if (!await _cacheDir.exists()) {
      await _cacheDir.create(recursive: true);
    }
  }

  Future<void> cacheFile(String fileName, List<int> fileBytes) async {
    try {
      final file = File('${_cacheDir.path}/$fileName');
      await file.writeAsBytes(fileBytes);
    } catch (e) {
      debugPrint('Error caching file: $e');
      rethrow;
    }
  }

  Future<File?> getCachedFile(String fileName) async {
    final file = File('${_cacheDir.path}/$fileName');
    if (await file.exists()) {
      return file;
    }
    return null;
  }

  Future<void> deleteCachedFile(String fileName) async {
    try {
      final file = File('${_cacheDir.path}/$fileName');
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('Error deleting cached file: $e');
      rethrow;
    }
  }

  Future<int> getCacheSize() async {
    int totalSize = 0;
    try {
      await for (final file
          in _cacheDir.list(recursive: true, followLinks: false)) {
        if (file is File) {
          totalSize += await file.length();
        }
      }
    } catch (e) {
      debugPrint('Error calculating cache size: $e');
    }
    return totalSize;
  }

  Future<void> clearCache() async {
    try {
      if (await _cacheDir.exists()) {
        await _cacheDir.delete(recursive: true);
        await _cacheDir.create();
      }
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      rethrow;
    }
  }

  String formatFileSize(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB'];
    var i = 0;
    double size = bytes.toDouble();

    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }

    return '${size.toStringAsFixed(2)} ${suffixes[i]}';
  }

  Future<Directory> getCacheDirectory() async {
    if (!await _cacheDir.exists()) {
      await _initCacheDir();
    }
    return _cacheDir;
  }
}
