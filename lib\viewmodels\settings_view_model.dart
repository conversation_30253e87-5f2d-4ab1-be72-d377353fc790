import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsViewModel extends ChangeNotifier {
  final SharedPreferences _prefs;

  // Keys for SharedPreferences
  static const String _themeModeKey = 'theme_mode';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _autoSyncKey = 'auto_sync_enabled';
  static const String _selectedLanguageKey = 'selected_language';

  SettingsViewModel(this._prefs) {
    // Initialize settings from SharedPreferences or use defaults
    // استخدام الوضع العادي (Light) كوضع افتراضي بدلاً من وضع النظام (System)
    _themeMode =
        ThemeMode.values[_prefs.getInt(_themeModeKey) ?? ThemeMode.light.index];
    _enableNotifications = _prefs.getBool(_notificationsKey) ?? true;
    _enableAutoSync = _prefs.getBool(_autoSyncKey) ?? true;
    _selectedLanguage = _prefs.getString(_selectedLanguageKey) ?? 'ar';
  }

  ThemeMode _themeMode = ThemeMode.light;
  bool _enableNotifications = true;
  bool _enableAutoSync = true;
  String _selectedLanguage = 'ar';

  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get enableNotifications => _enableNotifications;
  bool get enableAutoSync => _enableAutoSync;
  String get selectedLanguage => _selectedLanguage;

  // Additional getters for compatibility
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get notificationsEnabled => _enableNotifications;

  // Alias for setEnableNotifications for compatibility
  Future<void> setNotifications(bool value) => setEnableNotifications(value);

  // Setters with persistence
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _prefs.setInt(_themeModeKey, mode.index);
      notifyListeners();
    }
  }

  Future<void> setEnableNotifications(bool value) async {
    if (_enableNotifications != value) {
      _enableNotifications = value;
      await _prefs.setBool(_notificationsKey, value);
      notifyListeners();
    }
  }

  Future<void> setEnableAutoSync(bool value) async {
    if (_enableAutoSync != value) {
      _enableAutoSync = value;
      await _prefs.setBool(_autoSyncKey, value);
      notifyListeners();
    }
  }

  Future<void> setSelectedLanguage(String language) async {
    if (_selectedLanguage != language) {
      _selectedLanguage = language;
      await _prefs.setString(_selectedLanguageKey, language);
      notifyListeners();
    }
  }

  // Reset all settings to defaults
  Future<void> resetToDefaults() async {
    await Future.wait([
      _prefs.remove(_themeModeKey),
      _prefs.remove(_notificationsKey),
      _prefs.remove(_autoSyncKey),
      _prefs.remove(_selectedLanguageKey),
    ]);

    _themeMode = ThemeMode.light;
    _enableNotifications = true;
    _enableAutoSync = true;
    _selectedLanguage = 'ar';

    notifyListeners();
  }
}
