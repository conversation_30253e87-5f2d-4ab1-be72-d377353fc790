import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';
import '../constants/hive_constants.dart';
import '../models/hive/hive_sentence_model.dart';
import '../models/hive/sync_log_model.dart';
import '../models/hive/statistics_model.dart';
import '../models/sentence_model.dart';

/// خدمة إدارة الجمل باستخدام Hive
class HiveSentenceService {
  // الحصول على صناديق Hive
  final Box<HiveSentenceModel> _sentencesBox =
      Hive.box<HiveSentenceModel>(HiveConstants.sentencesBox);
  final Box<String> _readSentencesBox =
      Hive.box<String>(HiveConstants.readSentencesBox);
  final Box<String> _favoritesBox =
      Hive.box<String>(HiveConstants.favoritesBox);
  final Box<SyncLogModel> _syncLogBox =
      Hive.box<SyncLogModel>(HiveConstants.syncLogBox);
  final Box<StatisticsModel> _statisticsBox =
      Hive.box<StatisticsModel>(HiveConstants.statisticsBox);
  final Box _userBox = Hive.box(HiveConstants.userBox);
  final Box<String> _displayedSentencesBox =
      Hive.box<String>(HiveConstants.displayedSentencesBox);

  // معرف لإنشاء معرفات فريدة
  final _uuid = const Uuid();

  /// تخزين الجمل في Hive
  Future<void> storeSentences(List<SentenceModel> sentences) async {
    try {
      for (final sentence in sentences) {
        final hiveSentence = HiveSentenceModel.fromSentenceModel(sentence);
        await _sentencesBox.put(sentence.id, hiveSentence);
      }
      debugPrint('تم تخزين ${sentences.length} جملة في Hive');
    } catch (e) {
      debugPrint('خطأ في تخزين الجمل في Hive: $e');
      rethrow;
    }
  }

  /// الحصول على جميع الجمل
  List<HiveSentenceModel> getAllSentences() {
    try {
      return _sentencesBox.values.toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل من Hive: $e');
      return [];
    }
  }

  /// الحصول على جميع الجمل غير المقروءة
  List<HiveSentenceModel> getAllUnreadSentences() {
    try {
      // الحصول على جميع الجمل
      final allSentences = getAllSentences();

      // الحصول على معرفات الجمل المعروضة من الصندوق مباشرة للأداء الأفضل
      final displayedSentenceIds = getAllDisplayedSentenceIdsFromBox();

      // إضافة الجمل المقروءة من صندوق الجمل المقروءة
      displayedSentenceIds.addAll(_readSentencesBox.values.cast<String>());

      // تصفية الجمل غير المعروضة
      final unreadSentences = allSentences
          .where((sentence) =>
              !displayedSentenceIds.contains(sentence.id) &&
              !sentence.isReadByCurrentUser)
          .toList();

      debugPrint(
          'تم العثور على ${unreadSentences.length} جملة غير معروضة من أصل ${allSentences.length} جملة');
      debugPrint(
          'عدد الجمل المعروضة المستبعدة: ${displayedSentenceIds.length}');

      return unreadSentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل غير المقروءة من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل حسب الفئة
  List<HiveSentenceModel> getSentencesByCategory(String category) {
    try {
      return _sentencesBox.values
          .where((sentence) => sentence.category == category)
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل حسب الفئة من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل حسب مستوى الصعوبة
  List<HiveSentenceModel> getSentencesByDifficulty(String difficulty) {
    try {
      return _sentencesBox.values
          .where((sentence) => sentence.difficulty == difficulty)
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل حسب مستوى الصعوبة من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل المقروءة
  List<HiveSentenceModel> getReadSentences() {
    try {
      final readIds = _readSentencesBox.values.toList();
      return _sentencesBox.values
          .where((sentence) => readIds.contains(sentence.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل المقروءة من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل المفضلة
  List<HiveSentenceModel> getFavoriteSentences() {
    try {
      final favoriteIds = _favoritesBox.values.toList();
      return _sentencesBox.values
          .where((sentence) => favoriteIds.contains(sentence.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل المفضلة من Hive: $e');
      return [];
    }
  }

  /// التحقق مما إذا كانت الجملة مفضلة
  bool isSentenceFavorite(String sentenceId) {
    try {
      return _favoritesBox.containsKey(sentenceId);
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة المفضلة للجملة في Hive: $e');
      return false;
    }
  }

  /// تعيين جملة كمقروءة
  Future<void> markSentenceAsRead(String sentenceId) async {
    try {
      // Update the sentence model
      final sentence = _sentencesBox.get(sentenceId);
      if (sentence != null) {
        // Mark the sentence as read only if it's not already read
        if (!sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = true;
          await sentence.save(); // Save changes
          await _sentencesBox.flush(); // Ensure changes are saved to disk
          debugPrint(
              'Updated read status for sentence $sentenceId in sentences box');
        } else {
          debugPrint(
              'Sentence $sentenceId is already marked as read in the sentence model');
        }
      } else {
        debugPrint('Sentence $sentenceId not found in sentences box');
      }

      // Add the sentence ID to the read sentences box if it's not already there
      if (!_readSentencesBox.containsKey(sentenceId)) {
        await _readSentencesBox.put(sentenceId, sentenceId);
        await _readSentencesBox.flush(); // Ensure changes are saved to disk
        debugPrint('Added sentence $sentenceId to read sentences box');
      } else {
        debugPrint('Sentence $sentenceId already exists in read sentences box');
      }

      // Add to temporary read sentences list to ensure it won't be selected again
      await addToTempReadSentences(sentenceId);

      // Add to displayed sentences list to ensure it won't be shown again
      await addToDisplayedSentences(sentenceId);

      // Add directly to displayed sentences box for better performance
      await addToDisplayedSentencesBox(sentenceId);

      // إضافة سجل مزامنة
      final syncLog = SyncLogModel(
        id: _uuid.v4(),
        sentenceId: sentenceId,
        operationType: SyncOperationType.read,
        timestamp: DateTime.now(),
        isSynced: false, // تأكد من أن السجل غير متزامن
      );
      await _syncLogBox.add(syncLog);
      await _syncLogBox.flush(); // ضمان حفظ التغييرات على القرص
      debugPrint('تم إضافة سجل مزامنة للجملة $sentenceId');

      // تحديث الإحصائيات
      await _updateStatistics();
      debugPrint('تم تحديث الإحصائيات');

      // التحقق من عدد السجلات غير المتزامنة
      final unsyncedLogs = getUnsyncedLogs();
      debugPrint(
          'عدد السجلات غير المتزامنة بعد تعيين الجملة كمقروءة: ${unsyncedLogs.length}');

      // التحقق من أن الجملة تم تعيينها كمقروءة بشكل صحيح
      final isRead = _readSentencesBox.containsKey(sentenceId);
      final sentenceAfterSave = _sentencesBox.get(sentenceId);
      debugPrint(
          'التحقق من حالة القراءة بعد الحفظ: في صندوق الجمل المقروءة=$isRead، في نموذج الجملة=${sentenceAfterSave?.isReadByCurrentUser}');

      debugPrint('تم تعيين الجملة $sentenceId كمقروءة في Hive بنجاح');
    } catch (e) {
      debugPrint('خطأ في تعيين الجملة كمقروءة في Hive: $e');
      rethrow;
    }
  }

  /// تبديل حالة المفضلة للجملة
  Future<void> toggleFavorite(String sentenceId) async {
    try {
      // تحديث نموذج الجملة
      final sentence = _sentencesBox.get(sentenceId);
      if (sentence != null) {
        // تبديل حالة المفضلة
        sentence.isFavoriteByCurrentUser = !sentence.isFavoriteByCurrentUser;
        await sentence.save(); // حفظ التغييرات
        debugPrint(
            'تم تحديث حالة المفضلة للجملة $sentenceId في صندوق الجمل: ${sentence.isFavoriteByCurrentUser}');
      } else {
        debugPrint('الجملة $sentenceId غير موجودة في صندوق الجمل');
      }

      // التحقق من وجود الجملة في المفضلة
      final isFavorite = _favoritesBox.containsKey(sentenceId);

      if (isFavorite) {
        // إزالة من المفضلة
        await _favoritesBox.delete(sentenceId);
        debugPrint('تم إزالة الجملة $sentenceId من صندوق المفضلة');

        // إضافة سجل مزامنة
        final syncLog = SyncLogModel(
          id: _uuid.v4(),
          sentenceId: sentenceId,
          operationType: SyncOperationType.unfavorite,
          timestamp: DateTime.now(),
          isSynced: false, // تأكد من أن السجل غير متزامن
        );
        await _syncLogBox.add(syncLog);
        debugPrint('تم إضافة سجل مزامنة للجملة $sentenceId (إزالة من المفضلة)');

        // التحقق من عدد السجلات غير المتزامنة
        final unsyncedLogs = getUnsyncedLogs();
        debugPrint(
            'عدد السجلات غير المتزامنة بعد إزالة الجملة من المفضلة: ${unsyncedLogs.length}');

        debugPrint('تم إزالة الجملة $sentenceId من المفضلة في Hive بنجاح');
      } else {
        // إضافة إلى المفضلة
        await _favoritesBox.put(sentenceId, sentenceId);
        debugPrint('تم إضافة الجملة $sentenceId إلى صندوق المفضلة');

        // إضافة سجل مزامنة
        final syncLog = SyncLogModel(
          id: _uuid.v4(),
          sentenceId: sentenceId,
          operationType: SyncOperationType.favorite,
          timestamp: DateTime.now(),
          isSynced: false, // تأكد من أن السجل غير متزامن
        );
        await _syncLogBox.add(syncLog);
        debugPrint(
            'تم إضافة سجل مزامنة للجملة $sentenceId (إضافة إلى المفضلة)');

        // التحقق من عدد السجلات غير المتزامنة
        final unsyncedLogs = getUnsyncedLogs();
        debugPrint(
            'عدد السجلات غير المتزامنة بعد إضافة الجملة إلى المفضلة: ${unsyncedLogs.length}');

        debugPrint('تم إضافة الجملة $sentenceId إلى المفضلة في Hive بنجاح');
      }
    } catch (e) {
      debugPrint('خطأ في تبديل حالة المفضلة للجملة في Hive: $e');
      rethrow;
    }
  }

  /// تحديث الإحصائيات
  Future<void> _updateStatistics() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final todayKey = today.toIso8601String();

      // البحث عن إحصائيات اليوم أو إنشاء جديدة
      StatisticsModel? todayStats = _statisticsBox.get(todayKey);
      if (todayStats == null) {
        todayStats = StatisticsModel(
          id: todayKey,
          date: today,
          readCount: 0,
          shownCount: 0,
          lastUpdated: now,
        );
        await _statisticsBox.put(todayKey, todayStats);
      }

      todayStats.incrementReadCount();

      debugPrint(
          'تم تحديث إحصائيات اليوم: قراءة=${todayStats.readCount}, عرض=${todayStats.shownCount}');
    } catch (e) {
      debugPrint('خطأ في تحديث الإحصائيات في Hive: $e');
    }
  }

  /// تحديث عدد الجمل المعروضة اليوم
  Future<void> incrementTodayShownCount(int count) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final todayKey = today.toIso8601String();

      // تحديث العداد في userBox
      final currentCount =
          _userBox.get(HiveConstants.todayShownCountKey, defaultValue: 0);
      await _userBox.put(
          HiveConstants.todayShownCountKey, currentCount + count);

      // تحديث الإحصائيات
      StatisticsModel? todayStats = _statisticsBox.get(todayKey);
      if (todayStats == null) {
        todayStats = StatisticsModel(
          id: todayKey,
          date: today,
          readCount: 0,
          shownCount: 0,
          lastUpdated: now,
        );
        await _statisticsBox.put(todayKey, todayStats);
      }

      todayStats.incrementShownCount(count);

      debugPrint('تم تحديث عدد الجمل المعروضة اليوم: ${currentCount + count}');
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الجمل المعروضة اليوم في Hive: $e');
    }
  }

  /// زيادة عدد الجمل المقروءة اليوم
  Future<void> incrementTodayReadCount() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final todayKey = today.toIso8601String();

      // تحديث الإحصائيات
      StatisticsModel? todayStats = _statisticsBox.get(todayKey);
      todayStats.incrementReadCount();
      debugPrint('تم زيادة عدد الجمل المقروءة اليوم: ${todayStats.readCount}');
    } catch (e) {
      debugPrint('خطأ في زيادة عدد الجمل المقروءة اليوم في Hive: $e');
    }
  }

  /// الحصول على عدد الجمل المعروضة اليوم
  int getTodayShownCount() {
    try {
      return _userBox.get(HiveConstants.todayShownCountKey, defaultValue: 0);
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الجمل المعروضة اليوم من Hive: $e');
      return 0;
    }
  }

  /// الحصول على عدد الجمل المقروءة اليوم
  int getTodayReadCount() {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final todayKey = today.toIso8601String();

      final todayStats = _statisticsBox.get(todayKey);
      return todayStats?.readCount ?? 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الجمل المقروءة اليوم من Hive: $e');
      return 0;
    }
  }

  /// الحصول على إحصائيات لفترة زمنية محددة
  List<StatisticsModel> getStatisticsForDateRange(
      DateTime startDate, DateTime endDate) {
    try {
      // تحويل التواريخ إلى منتصف الليل
      final start = DateTime(startDate.year, startDate.month, startDate.day);
      final end =
          DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

      return _statisticsBox.values
          .where((stats) =>
              stats.date.isAfter(start.subtract(const Duration(seconds: 1))) &&
              stats.date.isBefore(end.add(const Duration(seconds: 1))))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الإحصائيات لفترة زمنية محددة من Hive: $e');
      return [];
    }
  }

  /// الحصول على إجمالي الإحصائيات لفترة زمنية محددة
  Map<String, int> getTotalStatisticsForDateRange(
      DateTime startDate, DateTime endDate) {
    try {
      final statsList = getStatisticsForDateRange(startDate, endDate);

      int totalRead = 0;
      int totalShown = 0;

      for (final stats in statsList) {
        totalRead += stats.readCount;
        totalShown += stats.shownCount;
      }

      return {
        'totalRead': totalRead,
        'totalShown': totalShown,
        'completionPercentage':
            totalShown > 0 ? ((totalRead / totalShown) * 100).round() : 0,
      };
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على إجمالي الإحصائيات لفترة زمنية محددة من Hive: $e');
      return {
        'totalRead': 0,
        'totalShown': 0,
        'completionPercentage': 0,
      };
    }
  }

  /// الحصول على معرفات الجمل اليومية الحالية
  List<String> getCurrentDailySentenceIds() {
    try {
      final dailySentenceIds = _userBox
          .get(HiveConstants.dailySentencesKey, defaultValue: <String>[]);

      if (dailySentenceIds is List) {
        return List<String>.from(dailySentenceIds);
      }

      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على معرفات الجمل اليومية الحالية من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل اليومية
  List<HiveSentenceModel> getDailySentences({bool includeRead = true}) {
    try {
      final dailySentenceIds = getCurrentDailySentenceIds();

      if (dailySentenceIds.isNotEmpty) {
        var sentences = dailySentenceIds
            .map((id) => _sentencesBox.get(id))
            .whereType<HiveSentenceModel>()
            .toList();

        // طباعة معلومات تصحيح
        debugPrint('تم العثور على ${sentences.length} جملة يومية في Hive');

        // التحقق من حالة القراءة من صندوق الجمل المقروءة
        final readIds = _readSentencesBox.values.toList();

        // تحديث حالة القراءة للجمل المخزنة محليًا
        for (var sentence in sentences) {
          // إذا كانت الجملة في قائمة الجمل المقروءة، تأكد من تعيينها كمقروءة
          final isInReadBox = readIds.contains(sentence.id);

          if (isInReadBox && !sentence.isReadByCurrentUser) {
            // Si está en el cuadro de oraciones leídas pero no está marcada como leída en el modelo
            sentence.isReadByCurrentUser = true;
            sentence.save(); // حفظ التغييرات
            debugPrint(
                'تم تحديث حالة القراءة للجملة ${sentence.id} من صندوق الجمل المقروءة');
          } else if (!isInReadBox && sentence.isReadByCurrentUser) {
            // Si no está en el cuadro de oraciones leídas pero está marcada como leída en el modelo
            sentence.isReadByCurrentUser = false;
            sentence.save(); // حفظ التغييرات
            debugPrint(
                'تم تصحيح حالة القراءة للجملة ${sentence.id} لتكون غير مقروءة');
          }

          debugPrint(
              'الجملة ${sentence.id}: مقروءة=${sentence.isReadByCurrentUser}');
        }

        // ضمان حفظ التغييرات على القرص
        _sentencesBox.flush();

        // إذا كان المطلوب استبعاد الجمل المقروءة
        if (!includeRead) {
          sentences = sentences
              .where((sentence) => !sentence.isReadByCurrentUser)
              .toList();

          debugPrint('بعد التصفية: ${sentences.length} جملة غير مقروءة');
        }

        return sentences;
      }

      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية من Hive: $e');
      return [];
    }
  }

  /// الحصول على الجمل اليومية غير المقروءة
  List<HiveSentenceModel> getUnreadDailySentences() {
    return getDailySentences(includeRead: false);
  }

  /// تخزين الجمل اليومية
  Future<void> storeDailySentences(List<HiveSentenceModel> sentences) async {
    try {
      final sentenceIds = sentences.map((s) => s.id).toList();
      await _userBox.put(HiveConstants.dailySentencesKey, sentenceIds);
      await _userBox.flush(); // ضمان حفظ التغييرات على القرص

      // تخزين الجمل نفسها إذا لم تكن موجودة بالفعل
      for (final sentence in sentences) {
        if (!_sentencesBox.containsKey(sentence.id)) {
          await _sentencesBox.put(sentence.id, sentence);
        } else {
          // تحديث الجملة الموجودة
          final existingSentence = _sentencesBox.get(sentence.id);
          if (existingSentence != null) {
            // تحديث حالة القراءة فقط إذا كانت الجملة غير مقروءة
            if (!existingSentence.isReadByCurrentUser) {
              existingSentence.isReadByCurrentUser =
                  sentence.isReadByCurrentUser;
              await existingSentence.save();
            }
          }
        }
      }

      // ضمان حفظ التغييرات على القرص
      await _sentencesBox.flush();

      // التحقق من أن الجمل اليومية تم حفظها بشكل صحيح
      final savedIds = _userBox
          .get(HiveConstants.dailySentencesKey, defaultValue: <String>[]);
      if (savedIds is List) {
        debugPrint('تم التحقق من حفظ ${savedIds.length} معرف جملة يومية');
      }

      debugPrint('تم تخزين ${sentences.length} جملة يومية في Hive');
    } catch (e) {
      debugPrint('خطأ في تخزين الجمل اليومية في Hive: $e');
    }
  }

  /// استبدال الجمل اليومية
  Future<void> replaceDailySentences(List<HiveSentenceModel> sentences) async {
    try {
      // مسح الجمل اليومية الحالية أولاً
      await _userBox.put(HiveConstants.dailySentencesKey, <String>[]);
      await _userBox.flush(); // ضمان حفظ التغييرات على القرص

      // تخزين الجمل الجديدة
      final sentenceIds = sentences.map((s) => s.id).toList();
      await _userBox.put(HiveConstants.dailySentencesKey, sentenceIds);
      await _userBox.flush(); // ضمان حفظ التغييرات على القرص

      // تخزين الجمل نفسها إذا لم تكن موجودة بالفعل
      for (final sentence in sentences) {
        if (!_sentencesBox.containsKey(sentence.id)) {
          // إضافة جملة جديدة
          // Asegurarse de que la oración no esté marcada como leída
          sentence.isReadByCurrentUser = false;
          await _sentencesBox.put(sentence.id, sentence);
          debugPrint('تم إضافة جملة جديدة ${sentence.id} كغير مقروءة');
        } else {
          // تحديث الجملة الموجودة
          final existingSentence = _sentencesBox.get(sentence.id);
          if (existingSentence != null) {
            // Verificar si la oración está en el cuadro de oraciones leídas
            final isInReadBox = _readSentencesBox.containsKey(sentence.id);

            if (isInReadBox) {
              // Si está en el cuadro de oraciones leídas, mantenerla como leída
              existingSentence.isReadByCurrentUser = true;
              await existingSentence.save();
              debugPrint(
                  'الجملة ${sentence.id} موجودة في صندوق الجمل المقروءة، تم الحفاظ عليها كمقروءة');
            } else {
              // Si no está en el cuadro de oraciones leídas, marcarla como no leída
              existingSentence.isReadByCurrentUser = false;
              await existingSentence.save();
              debugPrint(
                  'الجملة ${sentence.id} غير موجودة في صندوق الجمل المقروءة، تم تعيينها كغير مقروءة');
            }
          }
        }
      }

      // ضمان حفظ التغييرات على القرص
      await _sentencesBox.flush();

      // التحقق من أن الجمل اليومية تم حفظها بشكل صحيح
      final savedIds = _userBox
          .get(HiveConstants.dailySentencesKey, defaultValue: <String>[]);
      if (savedIds is List) {
        debugPrint('تم التحقق من حفظ ${savedIds.length} معرف جملة يومية');
      }

      debugPrint(
          'تم استبدال الجمل اليومية بـ ${sentences.length} جملة جديدة في Hive');
    } catch (e) {
      debugPrint('خطأ في استبدال الجمل اليومية في Hive: $e');
    }
  }

  /// الحصول على سجلات المزامنة غير المتزامنة
  List<SyncLogModel> getUnsyncedLogs() {
    try {
      final unsyncedLogs = <SyncLogModel>[];

      // البحث في جميع السجلات
      for (int i = 0; i < _syncLogBox.length; i++) {
        final log = _syncLogBox.getAt(i);
        if (log != null && !log.isSynced) {
          unsyncedLogs.add(log);
        }
      }

      debugPrint('تم العثور على ${unsyncedLogs.length} سجل غير متزامن');
      return unsyncedLogs;
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات المزامنة غير المتزامنة من Hive: $e');
      return [];
    }
  }

  /// تعيين سجل مزامنة واحد كمتزامن
  Future<bool> markLogAsSynced(String logId) async {
    try {
      // البحث عن السجل في صندوق سجلات المزامنة
      SyncLogModel? log;
      int? logIndex;

      // البحث في جميع السجلات
      for (int i = 0; i < _syncLogBox.length; i++) {
        final currentLog = _syncLogBox.getAt(i);
        if (currentLog != null && currentLog.id == logId) {
          log = currentLog;
          logIndex = i;
          break;
        }
      }

      if (log != null && logIndex != null) {
        // تعيين السجل كمتزامن
        log.isSynced = true;

        // حفظ التغييرات باستخدام putAt بدلاً من save
        await _syncLogBox.putAt(logIndex, log);

        // التحقق من أن التغييرات تم حفظها
        final updatedLog = _syncLogBox.getAt(logIndex);
        if (updatedLog != null && updatedLog.isSynced) {
          debugPrint('تم تعيين سجل المزامنة $logId كمتزامن بنجاح');
          return true;
        } else {
          debugPrint(
              'فشل في تعيين سجل المزامنة $logId كمتزامن: لم يتم حفظ التغييرات');
          return false;
        }
      } else {
        debugPrint('سجل المزامنة بالمعرف $logId غير موجود');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في تعيين سجل المزامنة $logId كمتزامن: $e');
      return false;
    }
  }

  /// تعيين سجلات المزامنة كمتزامنة
  Future<void> markLogsAsSynced(List<String> logIds) async {
    try {
      int successCount = 0;
      for (final id in logIds) {
        try {
          final success = await markLogAsSynced(id);
          if (success) {
            successCount++;
          }
        } catch (e) {
          debugPrint('خطأ في تعيين سجل المزامنة $id كمتزامن: $e');
          // استمر في المعالجة للسجلات الأخرى
        }
      }

      // التحقق من عدد السجلات غير المتزامنة بعد التحديث
      final remainingUnsyncedLogs = getUnsyncedLogs();

      debugPrint(
          'تم تعيين $successCount من أصل ${logIds.length} سجل مزامنة كمتزامن في Hive');
      debugPrint(
          'عدد السجلات غير المتزامنة المتبقية: ${remainingUnsyncedLogs.length}');
    } catch (e) {
      debugPrint('خطأ في تعيين سجلات المزامنة كمتزامنة في Hive: $e');
      rethrow;
    }
  }

  /// تحديث آخر وقت مزامنة
  Future<void> updateLastSyncTime() async {
    try {
      final now = DateTime.now();
      await _userBox.put(HiveConstants.lastSyncKey, now.toIso8601String());
      debugPrint('تم تحديث آخر وقت مزامنة في Hive: ${now.toIso8601String()}');
    } catch (e) {
      debugPrint('خطأ في تحديث آخر وقت مزامنة في Hive: $e');
    }
  }

  /// إعادة تعيين حالة المزامنة لجميع السجلات
  Future<void> resetSyncLogsStatus() async {
    try {
      int resetCount = 0;

      // البحث في جميع السجلات
      for (int i = 0; i < _syncLogBox.length; i++) {
        final log = _syncLogBox.getAt(i);
        if (log != null) {
          // إعادة تعيين حالة المزامنة لجميع السجلات
          log.isSynced = false;
          await _syncLogBox.putAt(i, log);
          resetCount++;
        }
      }

      debugPrint('تم إعادة تعيين حالة المزامنة لـ $resetCount سجل');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين حالة المزامنة للسجلات: $e');
    }
  }

  /// الحصول على آخر وقت مزامنة
  DateTime? getLastSyncTime() {
    try {
      final lastSyncStr = _userBox.get(HiveConstants.lastSyncKey);
      if (lastSyncStr != null) {
        return DateTime.parse(lastSyncStr);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على آخر وقت مزامنة من Hive: $e');
      return null;
    }
  }

  /// التحقق مما إذا كان الوقت قد حان للمزامنة التلقائية
  bool isTimeForAutoSync(int syncIntervalDays) {
    final lastSync = getLastSyncTime();
    if (lastSync == null) {
      return true;
    }

    final now = DateTime.now();
    final difference = now.difference(lastSync).inDays;

    return difference >= syncIntervalDays;
  }

  /// الحصول على صندوق الجمل
  Box<HiveSentenceModel> getSentencesBox() {
    return _sentencesBox;
  }

  /// الحصول على صندوق الجمل المقروءة
  Box<String> getReadSentencesBox() {
    return _readSentencesBox;
  }

  /// الحصول على صندوق المفضلة
  Box<String> getFavoritesBox() {
    return _favoritesBox;
  }

  /// الحصول على صندوق سجلات المزامنة
  Box<SyncLogModel> getSyncLogBox() {
    return _syncLogBox;
  }

  /// الحصول على صندوق الإحصائيات
  Box<StatisticsModel> getStatisticsBox() {
    return _statisticsBox;
  }

  /// الحصول على صندوق المستخدم
  Box getUserBox() {
    return _userBox;
  }

  /// التحقق مما إذا كان يوم جديد قد بدأ
  bool isNewDay() {
    try {
      final lastDateStr = _userBox.get(HiveConstants.todayDateKey);
      if (lastDateStr == null) {
        // لم يتم تعيين تاريخ سابق، لذا نعتبره يومًا جديدًا
        _updateTodayDate();
        return true;
      }

      final lastDate = DateTime.parse(lastDateStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final lastDay = DateTime(lastDate.year, lastDate.month, lastDate.day);

      final isNew = today.isAfter(lastDay);
      if (isNew) {
        _updateTodayDate();
      }

      return isNew;
    } catch (e) {
      debugPrint('خطأ في التحقق من بداية يوم جديد: $e');
      // في حالة الخطأ، نفترض أنه يوم جديد لتجنب فقدان البيانات
      _updateTodayDate();
      return true;
    }
  }

  /// تحديث تاريخ اليوم الحالي
  Future<void> _updateTodayDate() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      await _userBox.put(HiveConstants.todayDateKey, today.toIso8601String());
      debugPrint('تم تحديث تاريخ اليوم الحالي: ${today.toIso8601String()}');
    } catch (e) {
      debugPrint('خطأ في تحديث تاريخ اليوم الحالي: $e');
    }
  }

  /// مسح الجمل اليومية وإعادة تعيين العدادات
  Future<bool> clearDailySentencesForNewDay() async {
    try {
      // حفظ الجمل اليومية الحالية قبل التحقق من اليوم الجديد
      final currentDailySentenceIds = _userBox
          .get(HiveConstants.dailySentencesKey, defaultValue: <String>[]);

      // التحقق مما إذا كان يوم جديد
      final isNew = isNewDay();

      if (isNew) {
        debugPrint('تم اكتشاف يوم جديد، سيتم مسح الجمل اليومية');

        // مسح الجمل اليومية
        await _userBox.put(HiveConstants.dailySentencesKey, <String>[]);

        // إعادة تعيين عداد الجمل المعروضة
        await _userBox.put(HiveConstants.todayShownCountKey, 0);

        debugPrint('تم مسح الجمل اليومية وإعادة تعيين العدادات ليوم جديد');
        return true;
      } else {
        // إذا لم يكن يوم جديد، تأكد من أن الجمل اليومية لا تزال موجودة
        if (currentDailySentenceIds is List &&
            currentDailySentenceIds.isEmpty) {
          debugPrint(
              'تم اكتشاف قائمة جمل يومية فارغة في يوم غير جديد، سيتم استعادة الجمل غير المقروءة');

          // استعادة الجمل غير المقروءة من جميع الجمل المتاحة
          final allSentences = getAllSentences();
          final readIds = _readSentencesBox.values.toList();

          // اختيار 10 جمل غير مقروءة
          final unreadSentences = allSentences
              .where((s) => !readIds.contains(s.id))
              .take(10)
              .toList();

          if (unreadSentences.isNotEmpty) {
            final sentenceIds = unreadSentences.map((s) => s.id).toList();
            await _userBox.put(HiveConstants.dailySentencesKey, sentenceIds);
            debugPrint(
                'تم استعادة ${unreadSentences.length} جملة غير مقروءة للعرض اليومي');
          }
        }
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في مسح الجمل اليومية ليوم جديد: $e');
      return false;
    }
  }

  /// التحقق مما إذا كانت جميع الجمل اليومية مقروءة
  bool areAllDailySentencesRead() {
    try {
      final dailySentences = getDailySentences();
      if (dailySentences.isEmpty) {
        return true;
      }

      return dailySentences.every((sentence) => sentence.isReadByCurrentUser);
    } catch (e) {
      debugPrint('خطأ في التحقق من قراءة جميع الجمل اليومية: $e');
      return false;
    }
  }

  /// التحقق مما إذا كانت الجملة موجودة في صندوق الجمل المقروءة
  bool isInReadSentencesBox(String sentenceId) {
    try {
      return _readSentencesBox.containsKey(sentenceId);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الجملة في صندوق الجمل المقروءة: $e');
      return false;
    }
  }

  /// الحصول على جميع معرفات الجمل التي تمت قراءتها من سجلات المزامنة
  Set<String> getAllReadSentenceIdsFromSyncLogs() {
    try {
      final readSentenceIds = <String>{};

      // إضافة الجمل من صندوق الجمل المقروءة
      readSentenceIds.addAll(_readSentencesBox.values.cast<String>());

      // إضافة الجمل من سجلات المزامنة
      for (int i = 0; i < _syncLogBox.length; i++) {
        final log = _syncLogBox.getAt(i);
        if (log != null && log.operationType == SyncOperationType.read) {
          readSentenceIds.add(log.sentenceId);
        }
      }

      // إضافة الجمل التي تم تعليمها كمقروءة في نماذج الجمل
      final allSentences = getAllSentences();
      for (final sentence in allSentences) {
        if (sentence.isReadByCurrentUser) {
          readSentenceIds.add(sentence.id);
        }
      }

      // إضافة الجمل من صندوق الجمل اليومية الحالية (لأنها ستتم قراءتها قريباً)
      final currentDailySentenceIds = getCurrentDailySentenceIds();
      readSentenceIds.addAll(currentDailySentenceIds);

      // إضافة الجمل من صندوق الجمل المقروءة المؤقت (إذا كان موجوداً)
      final tempReadIds =
          _userBox.get('tempReadSentences', defaultValue: <String>[]);
      if (tempReadIds is List) {
        readSentenceIds.addAll(List<String>.from(tempReadIds));
      }

      debugPrint(
          'تم العثور على ${readSentenceIds.length} جملة مقروءة أو حالية');
      return readSentenceIds;
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على معرفات الجمل المقروءة من سجلات المزامنة: $e');
      return {};
    }
  }

  /// إزالة جملة من صندوق الجمل المقروءة
  Future<void> removeFromReadSentencesBox(String sentenceId) async {
    try {
      if (_readSentencesBox.containsKey(sentenceId)) {
        await _readSentencesBox.delete(sentenceId);
        await _readSentencesBox.flush(); // ضمان حفظ التغييرات على القرص
        debugPrint('تم إزالة الجملة $sentenceId من صندوق الجمل المقروءة');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الجملة من صندوق الجمل المقروءة: $e');
    }
  }

  /// تحديث حالة القراءة من سجلات المزامنة
  Future<void> updateReadStatusFromSyncLogs() async {
    try {
      // Get all sentences
      final allSentences = getAllSentences();

      // Get all read sentence IDs from sync logs
      final allReadSentenceIds = getAllReadSentenceIdsFromSyncLogs();

      // Update read status for all sentences
      int updatedCount = 0;
      for (var sentence in allSentences) {
        final isInReadLogs = allReadSentenceIds.contains(sentence.id);
        final isMarkedAsRead = sentence.isReadByCurrentUser;

        // If the sentence is in read logs but not marked as read in the model
        if (isInReadLogs && !isMarkedAsRead) {
          sentence.isReadByCurrentUser = true;
          await sentence.save();
          updatedCount++;
        }

        // If the sentence is not in read logs but marked as read in the model
        if (!isInReadLogs && isMarkedAsRead) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();
          updatedCount++;
        }
      }

      debugPrint(
          'تم تحديث حالة القراءة لـ $updatedCount جملة من سجلات المزامنة');
    } catch (e) {
      debugPrint('خطأ في تحديث حالة القراءة من سجلات المزامنة: $e');
    }
  }

  /// مسح ذاكرة التخزين المؤقت للجمل اليومية
  Future<void> clearDailySentencesCache() async {
    try {
      // Clear the daily sentences list
      await _userBox.put(HiveConstants.dailySentencesKey, <String>[]);

      // Flush to ensure changes are saved to disk
      await _userBox.flush();

      debugPrint('تم مسح ذاكرة التخزين المؤقت للجمل اليومية');
    } catch (e) {
      debugPrint('خطأ في مسح ذاكرة التخزين المؤقت للجمل اليومية: $e');
    }
  }

  /// حفظ معرف جملة في قائمة الجمل المقروءة المؤقتة
  Future<void> addToTempReadSentences(String sentenceId) async {
    try {
      // الحصول على القائمة الحالية
      final currentList =
          _userBox.get('tempReadSentences', defaultValue: <String>[]);

      // التأكد من أن القائمة من نوع List
      List<String> tempReadIds;
      if (currentList is List) {
        tempReadIds = List<String>.from(currentList);
      } else {
        tempReadIds = <String>[];
      }

      // إضافة المعرف إذا لم يكن موجوداً بالفعل
      if (!tempReadIds.contains(sentenceId)) {
        tempReadIds.add(sentenceId);

        // حفظ القائمة المحدثة
        await _userBox.put('tempReadSentences', tempReadIds);
        await _userBox.flush();

        debugPrint(
            'تم إضافة الجملة $sentenceId إلى قائمة الجمل المقروءة المؤقتة');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الجملة إلى قائمة الجمل المقروءة المؤقتة: $e');
    }
  }

  /// مسح قائمة الجمل المقروءة المؤقتة
  Future<void> clearTempReadSentences() async {
    try {
      // مسح القائمة
      await _userBox.put('tempReadSentences', <String>[]);
      await _userBox.flush();

      debugPrint('تم مسح قائمة الجمل المقروءة المؤقتة');
    } catch (e) {
      debugPrint('خطأ في مسح قائمة الجمل المقروءة المؤقتة: $e');
    }
  }

  /// إعادة تعيين حالة القراءة لجميع الجمل في قاعدة البيانات
  Future<void> resetAllSentencesReadStatus() async {
    try {
      // الحصول على جميع الجمل
      final allSentences = getAllSentences();

      // إعادة تعيين حالة القراءة لجميع الجمل
      int updatedCount = 0;
      for (var sentence in allSentences) {
        if (sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();
          updatedCount++;
        }
      }

      debugPrint(
          'تم إعادة تعيين حالة القراءة لـ $updatedCount جملة في قاعدة البيانات');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين حالة القراءة لجميع الجمل: $e');
    }
  }

  /// الحصول على جملة بواسطة المعرف
  HiveSentenceModel? getSentenceById(String sentenceId) {
    try {
      return _sentencesBox.get(sentenceId);
    } catch (e) {
      debugPrint('خطأ في الحصول على الجملة بواسطة المعرف: $e');
      return null;
    }
  }

  /// الحصول على جميع معرفات الجمل التي تم عرضها للمستخدم
  Set<String> getAllDisplayedSentenceIds() {
    try {
      final displayedIds = <String>{};

      // إضافة الجمل من صندوق الجمل المقروءة
      displayedIds.addAll(_readSentencesBox.values.cast<String>());

      // إضافة الجمل من صندوق الجمل المعروضة
      displayedIds.addAll(getAllDisplayedSentenceIdsFromBox());

      // إضافة الجمل من سجلات المزامنة
      for (int i = 0; i < _syncLogBox.length; i++) {
        final log = _syncLogBox.getAt(i);
        if (log != null) {
          displayedIds.add(log.sentenceId);
        }
      }

      // إضافة الجمل من صندوق الجمل اليومية الحالية
      final currentDailySentenceIds = getCurrentDailySentenceIds();
      displayedIds.addAll(currentDailySentenceIds);

      // إضافة الجمل من القائمة المؤقتة (للتوافق مع الإصدارات السابقة)
      final tempIds =
          _userBox.get('displayedSentences', defaultValue: <String>[]);
      if (tempIds is List) {
        displayedIds.addAll(List<String>.from(tempIds));
      }

      debugPrint('تم العثور على ${displayedIds.length} جملة تم عرضها للمستخدم');
      return displayedIds;
    } catch (e) {
      debugPrint('خطأ في الحصول على معرفات الجمل المعروضة: $e');
      return {};
    }
  }

  /// إضافة جملة إلى قائمة الجمل المعروضة
  Future<void> addToDisplayedSentences(String sentenceId) async {
    try {
      // إضافة إلى صندوق الجمل المعروضة
      await addToDisplayedSentencesBox(sentenceId);

      // للتوافق مع الإصدارات السابقة، نحتفظ أيضًا بالقائمة في userBox
      final currentList =
          _userBox.get('displayedSentences', defaultValue: <String>[]);

      List<String> displayedIds;
      if (currentList is List) {
        displayedIds = List<String>.from(currentList);
      } else {
        displayedIds = <String>[];
      }

      if (!displayedIds.contains(sentenceId)) {
        displayedIds.add(sentenceId);
        await _userBox.put('displayedSentences', displayedIds);
        await _userBox.flush();
        debugPrint(
            'تم إضافة الجملة $sentenceId إلى قائمة الجمل المعروضة (القديمة)');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الجملة إلى قائمة الجمل المعروضة: $e');
    }
  }

  /// مسح قائمة الجمل المعروضة
  Future<void> clearDisplayedSentences() async {
    try {
      // مسح صندوق الجمل المعروضة
      await _displayedSentencesBox.clear();

      // مسح القائمة المؤقتة في userBox (للتوافق مع الإصدارات السابقة)
      await _userBox.put('displayedSentences', <String>[]);
      await _userBox.flush();

      debugPrint('تم مسح قائمة الجمل المعروضة من جميع المصادر');
    } catch (e) {
      debugPrint('خطأ في مسح قائمة الجمل المعروضة: $e');
    }
  }

  /// إضافة جملة إلى صندوق الجمل المعروضة
  Future<void> addToDisplayedSentencesBox(String sentenceId) async {
    try {
      if (!_displayedSentencesBox.containsKey(sentenceId)) {
        await _displayedSentencesBox.put(sentenceId, sentenceId);
        debugPrint('تم إضافة الجملة $sentenceId إلى صندوق الجمل المعروضة');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الجملة إلى صندوق الجمل المعروضة: $e');
    }
  }

  /// التحقق مما إذا كانت الجملة موجودة في صندوق الجمل المعروضة
  bool isInDisplayedSentencesBox(String sentenceId) {
    try {
      return _displayedSentencesBox.containsKey(sentenceId);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الجملة في صندوق الجمل المعروضة: $e');
      return false;
    }
  }

  /// الحصول على جميع معرفات الجمل المعروضة من الصندوق
  Set<String> getAllDisplayedSentenceIdsFromBox() {
    try {
      return _displayedSentencesBox.keys.cast<String>().toSet();
    } catch (e) {
      debugPrint('خطأ في الحصول على معرفات الجمل المعروضة من الصندوق: $e');
      return {};
    }
  }
}
