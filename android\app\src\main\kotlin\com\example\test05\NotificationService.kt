package com.example.test05

import android.app.AlarmManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.widget.Toast
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

class NotificationService(private val context: Context) {

    fun showNotification(
        title: String,
        body: String,
        channelId: String,
        channelName: String,
        channelDescription: String
    ) {
        // Create the notification channel (required for Android 8.0 and above)
        createNotificationChannel(channelId, channelName, channelDescription)

        // Create an intent that will open the app when the notification is tapped
        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // Build the notification
        val builder = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.notification_icon) // Make sure this icon exists
            .setContentTitle(title)
            .setContentText(body)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setCategory(NotificationCompat.CATEGORY_REMINDER)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setVibrate(longArrayOf(1000, 1000, 1000, 1000, 1000))
            // .setFullScreenIntent(pendingIntent, true) // This can cause issues on some devices
            .setDefaults(NotificationCompat.DEFAULT_ALL) // Sound, vibration, and lights

        // Show the notification
        with(NotificationManagerCompat.from(context)) {
            try {
                // Use a consistent notification ID based on the channel ID's hash code
                // This helps prevent duplicate notifications
                val notificationId = channelId.hashCode()
                notify(notificationId, builder.build())

                // Also show a toast message for immediate visibility
                try {
                    val handler = Handler(Looper.getMainLooper())
                    handler.post {
                        Toast.makeText(context, "$title: $body", Toast.LENGTH_LONG).show()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } catch (e: SecurityException) {
                // Handle the case where notification permission is not granted
                e.printStackTrace()
            }
        }
    }

    // Schedule a notification to be shown later
    fun scheduleNotification(
        title: String,
        body: String,
        channelId: String,
        channelName: String,
        channelDescription: String,
        delayInMillis: Any
    ) {
        // Convert to Long to handle both Integer and Long from Dart
        val delay = when (delayInMillis) {
            is Int -> delayInMillis.toLong()
            is Long -> delayInMillis
            is Double -> delayInMillis.toLong()
            else -> {
                // Default to 60 seconds if conversion fails
                60000L
            }
        }
        // Create an intent for the AlarmReceiver
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            putExtra("title", title)
            putExtra("body", body)
            putExtra("channelId", channelId)
            putExtra("channelName", channelName)
            putExtra("channelDescription", channelDescription)
        }

        // Create a unique request code based on the parameters
        val requestCode = "$title$body$channelId".hashCode()

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // Get the AlarmManager service
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        // Set the alarm to trigger after the specified delay
        val triggerTime = SystemClock.elapsedRealtime() + delay

        // Use setExactAndAllowWhileIdle for Android 6.0+ to ensure the alarm fires even when the device is in Doze mode
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.ELAPSED_REALTIME_WAKEUP,
                triggerTime,
                pendingIntent
            )
        } else {
            alarmManager.setExact(
                AlarmManager.ELAPSED_REALTIME_WAKEUP,
                triggerTime,
                pendingIntent
            )
        }
    }

    private fun createNotificationChannel(
        channelId: String,
        channelName: String,
        channelDescription: String
    ) {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(channelId, channelName, importance).apply {
                description = channelDescription
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lockscreenVisibility = NotificationManager.IMPORTANCE_HIGH

                // Set sound and vibration
                vibrationPattern = longArrayOf(1000, 1000, 1000, 1000, 1000)

                // Note: We can't reassign importance here as it's a val in the NotificationChannel class
            }

            // Register the channel with the system
            val notificationManager: NotificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    // Cancel a scheduled notification
    fun cancelScheduledNotification(
        title: String,
        body: String,
        channelId: String
    ) {
        try {
            val intent = Intent(context, AlarmReceiver::class.java)
            val requestCode = "$title$body$channelId".hashCode()

            // Try to get the existing pending intent
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_NO_CREATE
            )

            // If the pending intent exists, cancel it
            if (pendingIntent != null) {
                val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                alarmManager.cancel(pendingIntent)
                pendingIntent.cancel()
            }

            // Also cancel any notification that might be showing
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(channelId.hashCode())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
