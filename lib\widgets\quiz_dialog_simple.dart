import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:avatar_glow/avatar_glow.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:just_audio/just_audio.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_result.dart';
import '../models/sentence_model.dart';
import '../theme/app_theme.dart';
import '../services/speech_recognition_service.dart';
import 'speech_processing_indicator.dart';
import 'speech_notification.dart';

class QuizDialog extends StatefulWidget {
  final SentenceModel sentence;

  const QuizDialog({
    Key? key,
    required this.sentence,
  }) : super(key: key);

  @override
  State<QuizDialog> createState() => _QuizDialogState();
}

class _QuizDialogState extends State<QuizDialog> {
  int _currentPage = 1;
  final int _totalPages = 3;

  // Variables para UI
  bool _isSpeaking = false;
  double _playbackProgress = 0.0;
  bool _isRecording = false;
  bool _showResults = false;
  bool _isProcessing = false; // Indicador de procesamiento
  double _pronunciationScore = 0.85;
  double _memoryScore = 0.75;

  // Variables para TTS
  late FlutterTts _flutterTts;
  int _currentWordIndex = -1;
  List<String> _words = [];
  double _speechRate = 0.5; // Velocidad de lectura (0.1 - 1.0)

  // Clave para guardar la velocidad de lectura en SharedPreferences
  static const String _speechRateKey = 'speech_rate_preference';

  // Variables para reconocimiento de voz directo (nuevo enfoque)
  final stt.SpeechToText _speechToText = stt.SpeechToText();
  bool _speechEnabled = false;

  // Variables para reconocimiento de voz (enfoque anterior)
  late SpeechRecognitionService _speechRecognitionService;
  String _recognizedText = '';
  Map<String, dynamic>? _speechComparisonResult;

  // Variables para reproducción de grabación
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlayingRecording = false;

  @override
  void initState() {
    super.initState();
    // Inicializar la lista de palabras
    _words = widget.sentence.englishText.split(' ');
    // Cargar la velocidad de lectura guardada
    _loadSavedSpeechRate();
    _initTts();
    _initSpeechRecognition();
    _initDirectSpeechRecognition();
  }

  // Inicializar el reconocimiento de voz directo (nuevo enfoque)
  void _initDirectSpeechRecognition() async {
    _speechEnabled = await _speechToText.initialize(
      onError: (error) => debugPrint('Error de inicialización directa: $error'),
      onStatus: (status) => debugPrint('Estado directo: $status'),
      debugLogging: true,
    );
    if (mounted) {
      setState(() {});
    }
    debugPrint('Reconocimiento de voz directo inicializado: $_speechEnabled');
  }

  // Método para cargar la velocidad de lectura guardada
  Future<void> _loadSavedSpeechRate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedRate = prefs.getDouble(_speechRateKey);
      if (savedRate != null) {
        setState(() {
          _speechRate = savedRate;
        });
      }
    } catch (e) {
      debugPrint('Error al cargar la velocidad de lectura: $e');
    }
  }

  @override
  void dispose() {
    _flutterTts.stop();
    _speechRecognitionService.dispose();
    _audioPlayer.dispose();
    // Detener el reconocimiento de voz directo si está activo
    if (_speechToText.isListening) {
      _speechToText.stop();
    }
    super.dispose();
  }

  // Inicializar el servicio de reconocimiento de voz
  Future<void> _initSpeechRecognition() async {
    _speechRecognitionService = SpeechRecognitionService();
    try {
      await _speechRecognitionService.initialize();
    } catch (e) {
      // Mostrar un mensaje de error si no se puede inicializar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('No se pudo inicializar el reconocimiento de voz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Inicializar el motor TTS
  Future<void> _initTts() async {
    _flutterTts = FlutterTts();

    // Configurar el idioma a inglés
    await _flutterTts.setLanguage("en-US");

    // Configurar la velocidad de habla usando la preferencia guardada
    await _flutterTts.setSpeechRate(_speechRate);
    debugPrint('Usando velocidad de lectura: $_speechRate');

    // Configurar el volumen (1.0 = volumen máximo)
    await _flutterTts.setVolume(1.0);

    // Configurar el tono (1.0 = tono normal)
    await _flutterTts.setPitch(1.0);

    // Configurar manejadores de eventos
    _flutterTts.setStartHandler(() {
      setState(() {
        _isSpeaking = true;
        _playbackProgress = 0.0;
      });
    });

    _flutterTts.setCompletionHandler(() {
      // Forzar inmediatamente la última palabra resaltada
      if (mounted) {
        setState(() {
          if (_words.isNotEmpty) {
            _currentWordIndex = _words.length - 1;
            _playbackProgress = 1.0;
          }
        });
      }

      // Mantener la última palabra resaltada por un tiempo suficiente
      Future.delayed(const Duration(milliseconds: 3000), () {
        if (mounted) {
          setState(() {
            _isSpeaking = false;
            // No resetear el índice de palabra aquí para mantener la última palabra resaltada
          });

          // Mantener la última palabra resaltada por un momento más antes de resetear
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              setState(() {
                _currentWordIndex = -1;
              });
            }
          });
        }
      });
    });

    _flutterTts.setProgressHandler(
        (String text, int startOffset, int endOffset, String word) {
      if (mounted) {
        setState(() {
          // Limpiar la palabra para comparación
          String trimmedWord = word.trim();

          // Buscar la palabra exacta primero
          int wordIndex = _words.indexOf(trimmedWord);

          // Si no se encuentra, buscar palabras que contengan esta palabra
          if (wordIndex < 0) {
            for (int i = 0; i < _words.length; i++) {
              if (_words[i].contains(trimmedWord) ||
                  trimmedWord.contains(_words[i])) {
                wordIndex = i;
                break;
              }
            }
          }

          // Si se encontró la palabra, actualizar el índice y el progreso
          if (wordIndex >= 0) {
            _currentWordIndex = wordIndex;
            _playbackProgress = (wordIndex + 1) / _words.length;

            // Registrar para depuración
            debugPrint(
                'Palabra: "$trimmedWord", Índice: $wordIndex, Progreso: $_playbackProgress');

            // Si estamos cerca del final, preparar para la última palabra
            if (wordIndex >= _words.length - 2) {
              // Programar la última palabra para que se resalte pronto
              Future.delayed(const Duration(milliseconds: 50), () {
                if (mounted && _isSpeaking) {
                  setState(() {
                    _currentWordIndex = _words.length - 1;
                    _playbackProgress = 1.0;
                    debugPrint('Forzando última palabra: ${_words.last}');
                  });
                }
              });
            }
          } else {
            // Si no se encontró la palabra pero tenemos un índice de palabra actual,
            // intentar avanzar al siguiente índice de palabra de manera secuencial
            if (_currentWordIndex >= 0 &&
                _currentWordIndex < _words.length - 1) {
              _currentWordIndex++;
              _playbackProgress = (_currentWordIndex + 1) / _words.length;
              debugPrint(
                  'Avanzando secuencialmente a la palabra: ${_words[_currentWordIndex]}');
            } else {
              debugPrint('No se encontró la palabra: "$trimmedWord"');
            }
          }
        });
      }
    });

    _flutterTts.setErrorHandler((msg) {
      setState(() {
        _isSpeaking = false;
        debugPrint("Error TTS: $msg");
      });
    });
  }

  // Método para hablar o detener el habla
  Future<void> _speak() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      setState(() {
        _isSpeaking = false;
        _currentWordIndex = -1;
        _playbackProgress = 0.0;
      });
    } else {
      setState(() {
        _currentWordIndex = -1;
        _playbackProgress = 0.0;
      });

      // Preparar el texto para hablar
      final textToSpeak = widget.sentence.englishText;

      // Añadir un pequeño retraso para asegurar que la UI se actualice antes de hablar
      await Future.delayed(const Duration(milliseconds: 100));

      // Hablar el texto
      await _flutterTts.speak(textToSpeak);

      // Calcular la duración aproximada de la reproducción (basada en el número de palabras)
      // Asumimos un promedio de 300ms por palabra a velocidad normal, ajustado por la velocidad actual
      final int estimatedDurationMs =
          (_words.length * 300 ~/ _speechRate).toInt();

      // Añadir un manejador para asegurar que la última palabra se resalte
      Future.delayed(Duration(milliseconds: estimatedDurationMs - 200), () {
        if (mounted && _isSpeaking && _currentWordIndex < _words.length - 1) {
          debugPrint('Verificando si la última palabra está resaltada...');
          // Si cerca del final no se ha resaltado la última palabra, forzarla
          setState(() {
            _currentWordIndex = _words.length - 1;
            _playbackProgress = 1.0;
          });
        }
      });
    }
  }

  // Método para cambiar la velocidad de habla
  Future<void> _changeSpeechRate(double rate) async {
    // Asegurarse de que la velocidad esté en el rango válido (0.1 - 1.0)
    rate = rate.clamp(0.1, 1.0);

    setState(() {
      _speechRate = rate;
    });

    // Actualizar la velocidad en el motor TTS
    await _flutterTts.setSpeechRate(rate);

    // Guardar la preferencia del usuario
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_speechRateKey, rate);
      debugPrint('Velocidad de lectura guardada: $rate');
    } catch (e) {
      debugPrint('Error al guardar la velocidad de lectura: $e');
    }

    // Si está hablando, reiniciar para aplicar la nueva velocidad
    if (_isSpeaking) {
      await _flutterTts.stop();
      await _flutterTts.speak(widget.sentence.englishText);
    }
  }

  // Método para reproducir la grabación del usuario
  Future<void> _playRecording() async {
    try {
      if (_isPlayingRecording) {
        // Si ya está reproduciendo, detener
        await _audioPlayer.stop();
        setState(() {
          _isPlayingRecording = false;
        });
        debugPrint('Reproducción detenida');
      } else {
        // Obtener la ruta del archivo de audio
        final audioFilePath = _speechRecognitionService.getRecordingFilePath();
        debugPrint('Ruta del archivo de audio: $audioFilePath');

        if (audioFilePath != null && await File(audioFilePath).exists()) {
          // Preparar el reproductor
          setState(() {
            _isPlayingRecording = true;
          });

          // Mostrar mensaje de depuración
          debugPrint('Iniciando reproducción del archivo: $audioFilePath');

          try {
            // Detener cualquier reproducción anterior
            await _audioPlayer.stop();

            // Configurar el volumen al máximo
            await _audioPlayer.setVolume(1.0);

            // Reproducir el audio
            await _audioPlayer.setFilePath(audioFilePath);
            await _audioPlayer.play();

            debugPrint('Reproducción iniciada correctamente');

            // Configurar el manejador de finalización
            _audioPlayer.playerStateStream.listen((state) {
              debugPrint('Estado del reproductor: ${state.processingState}');
              if (state.processingState == ProcessingState.completed) {
                if (mounted) {
                  setState(() {
                    _isPlayingRecording = false;
                  });
                }
                debugPrint('Reproducción completada');
              }
            });

            // Añadir un temporizador de seguridad para detener la reproducción después de un tiempo
            Future.delayed(const Duration(seconds: 10), () {
              if (_isPlayingRecording && mounted) {
                _audioPlayer.stop();
                setState(() {
                  _isPlayingRecording = false;
                });
                debugPrint(
                    'Reproducción detenida por temporizador de seguridad');
              }
            });
          } catch (audioError) {
            debugPrint('Error específico de audio: $audioError');
            rethrow;
          }
        } else {
          // No hay grabación disponible
          debugPrint('No se encontró el archivo de audio o no existe');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('No hay grabación disponible para reproducir'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error al reproducir la grabación: $e');
      setState(() {
        _isPlayingRecording = false;
      });

      // Mostrar error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al reproducir la grabación: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Método para manejar el reconocimiento de voz directo
  void _onSpeechResult(SpeechRecognitionResult result) {
    setState(() {
      _recognizedText = result.recognizedWords;
      debugPrint('Palabras reconocidas directamente: $_recognizedText');
      debugPrint('¿Es resultado final? ${result.finalResult}');

      if (result.finalResult) {
        // Mostrar indicador de procesamiento
        setState(() {
          _isProcessing = true;
        });

        // Mostrar diálogo de procesamiento con el nuevo indicador
        if (mounted) {
          showDialog(
            context: context,
            barrierColor: Colors.transparent,
            barrierDismissible: false, // No permitir cerrar mientras procesa
            builder: (BuildContext dialogContext) {
              return Align(
                alignment: Alignment.center,
                child: Container(
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(70),
                        blurRadius: 15,
                        spreadRadius: 2,
                      )
                    ],
                  ),
                  // Usar el nuevo indicador de procesamiento
                  child: const SpeechProcessingIndicator(
                    type: ProcessingIndicatorType
                        .wave, // Usar el indicador de ondas
                    message: 'جاري تحليل الصوت...',
                    color: AppTheme.primaryColor,
                    size: 80.0,
                  ),
                ),
              );
            },
          );
        }

        // Comparar el texto reconocido con el texto original
        _speechComparisonResult = _speechRecognitionService.compareTexts(
          widget.sentence.englishText,
          _recognizedText,
        );

        // Actualizar la puntuación según la similitud
        if (_currentPage == 2) {
          _pronunciationScore = _speechComparisonResult!['overallSimilarity'];
        } else if (_currentPage == 3) {
          _memoryScore = _speechComparisonResult!['overallSimilarity'];
        }

        // Cerrar el diálogo de procesamiento
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }

        // Mostrar los resultados
        setState(() {
          _showResults = true;
          _isRecording = false;
          _isProcessing = false; // Finalizar procesamiento
        });

        // Mostrar mensaje de éxito si se reconoció texto
        if (_recognizedText.isNotEmpty && mounted) {
          // Mostrar mensaje de éxito usando el nuevo widget de notificación
          if (mounted) {
            SpeechNotification.show(
              context: context,
              message: 'تم التعرف على الكلام بنجاح',
              type: NotificationType.success,
              duration: const Duration(seconds: 2),
            );
          }
        }
      }
    });
  }

  // Método para iniciar el reconocimiento de voz directo
  Future<void> _startDirectListening() async {
    // Limpiar resultados anteriores
    setState(() {
      _recognizedText = '';
      _showResults = false;
    });

    // Mostrar mensaje en la parte superior de la pantalla emergente
    if (mounted) {
      // Usar el nuevo widget de notificación
      SpeechNotification.show(
        context: context,
        message: 'جاري الاستماع... تحدث الآن',
        type: NotificationType.processing,
        duration: const Duration(seconds: 2),
      );
    }

    // Iniciar el reconocimiento
    try {
      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor:
            const Duration(seconds: 15), // Tiempo más largo para escuchar
        pauseFor:
            const Duration(seconds: 5), // Pausa más larga antes de finalizar
        localeId: 'en-US',
      );

      setState(() {
        _isRecording = true;
      });

      // Programar detención automática después de un tiempo
      Future.delayed(const Duration(seconds: 15), () {
        if (mounted && _speechToText.isListening) {
          // Guardar el contexto actual antes de la operación asíncrona
          final BuildContext currentContext = context;

          // Mostrar indicador de procesamiento antes de detener
          showDialog(
            context: currentContext,
            barrierColor: Colors.transparent,
            barrierDismissible: false,
            builder: (BuildContext dialogContext) {
              return Align(
                alignment: Alignment.center,
                child: Container(
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(70),
                        blurRadius: 15,
                        spreadRadius: 2,
                      )
                    ],
                  ),
                  // Usar el nuevo indicador de procesamiento con animación de pulso
                  child: const SpeechProcessingIndicator(
                    type: ProcessingIndicatorType.pulse,
                    message: 'جاري تحليل الصوت...',
                    color: AppTheme.primaryColor,
                    size: 80.0,
                  ),
                ),
              );
            },
          );

          // Detener el reconocimiento
          _stopDirectListening();

          // Cerrar el diálogo de procesamiento después de un tiempo
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && Navigator.of(currentContext).canPop()) {
              Navigator.of(currentContext).pop();

              // Mostrar mensaje de finalización automática usando el nuevo widget
              if (mounted) {
                SpeechNotification.show(
                  context: context,
                  message: 'تم إيقاف التسجيل تلقائيًا',
                  type: NotificationType.warning,
                  duration: const Duration(seconds: 2),
                );
              }
            }
          });
        }
      });
    } catch (e) {
      debugPrint('Error al iniciar el reconocimiento directo: $e');
      if (mounted) {
        // Mostrar mensaje de error usando el nuevo widget
        SpeechNotification.show(
          context: context,
          message: 'خطأ في بدء الاستماع: $e',
          type: NotificationType.error,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  // Método para detener el reconocimiento de voz directo
  Future<void> _stopDirectListening() async {
    if (!_speechToText.isListening) return;

    try {
      // Mostrar indicador de procesamiento
      setState(() {
        _isRecording = false;
        _isProcessing = true;
      });

      // Mostrar mensaje de procesamiento
      if (mounted) {
        showDialog(
          context: context,
          barrierColor: Colors.transparent,
          barrierDismissible: false, // No permitir cerrar mientras procesa
          builder: (BuildContext dialogContext) {
            return Align(
              alignment: Alignment.center,
              child: Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(70),
                      blurRadius: 15,
                      spreadRadius: 2,
                    )
                  ],
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Indicador personalizado con animación
                    SizedBox(
                      width: 60,
                      height: 60,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Círculo exterior animado
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor),
                            strokeWidth: 3,
                          ),
                          // Icono de micrófono en el centro
                          Icon(
                            Icons.mic,
                            color: AppTheme.primaryColor,
                            size: 30,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20),
                    Text(
                      'جاري تحليل الصوت...',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }

      // Detener el reconocimiento
      await _speechToText.stop();

      // Pequeña pausa para permitir que el sistema procese
      await Future.delayed(const Duration(milliseconds: 500));

      // Cerrar el diálogo de procesamiento
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Si no hay texto reconocido, mostrar un mensaje
      if (_recognizedText.isEmpty) {
        setState(() {
          _showResults = true;
          _isProcessing = false;
          _speechComparisonResult = _speechRecognitionService.compareTexts(
            widget.sentence.englishText,
            '',
          );

          if (_currentPage == 2) {
            _pronunciationScore = 0.0;
          } else if (_currentPage == 3) {
            _memoryScore = 0.0;
          }
        });

        if (mounted) {
          // Mostrar mensaje usando el nuevo widget
          SpeechNotification.show(
            context: context,
            message: 'لم يتم التعرف على أي كلام',
            type: NotificationType.warning,
            duration: const Duration(seconds: 2),
          );
        }
      } else {
        // Si hay texto reconocido, actualizar el estado
        setState(() {
          _isProcessing = false;
        });
      }
    } catch (e) {
      debugPrint('Error al detener el reconocimiento directo: $e');
      // Asegurarse de que el indicador de procesamiento se oculte
      setState(() {
        _isProcessing = false;
      });

      // Cerrar el diálogo de procesamiento si está abierto
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    }
  }

  // Método principal para manejar la grabación (ahora usa el enfoque directo)
  Future<void> _handleRecording() async {
    if (_isRecording) {
      // Detener el reconocimiento
      await _stopDirectListening();
    } else {
      // Iniciar el reconocimiento
      await _startDirectListening();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determinar si la pantalla es pequeña
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 600;
    final isLandscape = screenSize.width > screenSize.height;

    // Ajustar tamaños basados en el tamaño de la pantalla
    final dialogWidth =
        isLandscape ? screenSize.width * 0.7 : screenSize.width * 0.9;
    final dialogHeight =
        isLandscape ? screenSize.height * 0.8 : screenSize.height * 0.7;
    final pageSelectorSize = isSmallScreen ? 30.0 : 40.0;
    final pageSelectorFontSize = isSmallScreen ? 14.0 : 16.0;
    final buttonFontSize = isSmallScreen ? 14.0 : 16.0;
    final contentPadding = isSmallScreen ? 12.0 : 20.0;
    final titleFontSize = isSmallScreen ? 18.0 : 22.0;

    return Dialog(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        padding: EdgeInsets.all(contentPadding),
        child: Column(
          children: [
            // Encabezado del cuestionario
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getPageTitle(_currentPage),
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: 'إغلاق',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Indicador de páginas estilo timeline (de derecha a izquierda)
            Directionality(
              textDirection: TextDirection
                  .rtl, // Para que el timeline vaya de derecha a izquierda
              child: SizedBox(
                height: pageSelectorSize + 10,
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    for (int i = 0; i < _totalPages; i++) ...[
                      // Círculo indicador
                      GestureDetector(
                        onTap: () {
                          // Solo permitir navegar a páginas anteriores o a la siguiente
                          if (i + 1 <= _currentPage ||
                              i + 1 == _currentPage + 1) {
                            setState(() {
                              _currentPage = i + 1;
                            });
                          }
                        },
                        child: Container(
                          width: pageSelectorSize,
                          height: pageSelectorSize,
                          decoration: BoxDecoration(
                            color: i + 1 == _currentPage
                                ? AppTheme.primaryColor
                                : i + 1 < _currentPage
                                    ? Colors.green
                                    : Colors.grey.shade300,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: i + 1 <= _currentPage
                                  ? Colors.white
                                  : Colors.transparent,
                              width: 2,
                            ),
                            boxShadow: i + 1 == _currentPage
                                ? [
                                    BoxShadow(
                                      color:
                                          AppTheme.primaryColor.withAlpha(100),
                                      blurRadius: 8,
                                      spreadRadius: 2,
                                    )
                                  ]
                                : null,
                          ),
                          child: Center(
                            child: Text(
                              '${i + 1}',
                              style: TextStyle(
                                color: i + 1 <= _currentPage
                                    ? Colors.white
                                    : Colors.grey.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: pageSelectorFontSize,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Línea conectora (excepto después del último círculo)
                      if (i < _totalPages - 1)
                        Expanded(
                          child: Container(
                            height: 3,
                            color: i + 1 < _currentPage
                                ? Colors.green
                                : Colors.grey.shade300,
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Contenido del cuestionario
            Expanded(
              child: _buildQuizContent(_currentPage),
            ),

            const SizedBox(height: 16),

            // Botones de navegación
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  // Botón para volver a la página anterior (solo visible si no estamos en la primera página)
                  if (_currentPage > 1)
                    Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _currentPage--;
                            });
                          },
                          icon: const Icon(Icons.arrow_back),
                          label: const Text('السابق'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.shade700,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ),

                  // Espacio entre botones
                  if (_currentPage > 1) const SizedBox(width: 16),

                  // Botón para avanzar a la siguiente página o finalizar
                  Expanded(
                    flex: _currentPage > 1 ? 2 : 1,
                    child: ElevatedButton(
                      onPressed: _currentPage < _totalPages
                          ? () {
                              // Limpiar resultados anteriores al cambiar a la página de memoria (página 3)
                              if (_currentPage == 2) {
                                setState(() {
                                  _recognizedText = '';
                                  _speechComparisonResult = null;
                                  _showResults = false;
                                });
                              }
                              setState(() => _currentPage++);
                            }
                          : () {
                              // Devolver el resultado al cerrar el diálogo
                              // Usar el mejor resultado entre pronunciación y memoria
                              final bestScore =
                                  _pronunciationScore > _memoryScore
                                      ? _pronunciationScore
                                      : _memoryScore;

                              // التحقق من أن المستخدم قد قام بالاختبار فعلاً
                              final hasTested = _recognizedText.isNotEmpty;

                              // التحقق من أن متوسط النتيجة أكبر من أو يساوي 80%
                              final averageScore =
                                  (_pronunciationScore + _memoryScore) / 2;
                              final passedTest = averageScore >= 0.8;

                              // تعيين حالة الاختبار في نموذج الجملة فقط إذا اجتاز الاختبار
                              widget.sentence.isPronunciationTested =
                                  hasTested && passedTest;

                              // إظهار رسالة إذا لم يجتز الاختبار
                              if (hasTested && !passedTest) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                        'لم تجتز الاختبار. يجب أن يكون متوسط النتيجة 80% على الأقل. حاول مرة أخرى.'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }

                              Navigator.of(context).pop({
                                'score': bestScore,
                                'pronunciationScore': _pronunciationScore,
                                'memoryScore': _memoryScore,
                                'recognizedText': _recognizedText,
                                'isPronunciationTested':
                                    hasTested && passedTest,
                                'hasTested': hasTested,
                                'passedTest': passedTest,
                              });
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        _currentPage < _totalPages
                            ? 'التالي'
                            : 'إنهاء الاختبار',
                        style: TextStyle(
                          fontSize: buttonFontSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizContent(int page) {
    switch (page) {
      case 1:
        return _buildListeningPage();
      case 2:
        return _buildReadingPage();
      case 3:
        return _buildMemoryPage();
      default:
        return const SizedBox.shrink();
    }
  }

  // Página 1: Escuchar y pronunciar
  Widget _buildListeningPage() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instrucciones
          Text(
            'استمع إلى الجملة وانتبه للنطق الصحيح',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 24),

          // Texto con resaltado
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Directionality(
              textDirection:
                  TextDirection.ltr, // Forzar dirección de izquierda a derecha
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.black,
                    height: 1.5,
                  ),
                  children: _buildHighlightedText(),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Barra de progreso
          LinearPercentIndicator(
            lineHeight: 8.0,
            percent: _playbackProgress,
            backgroundColor: Colors.grey.shade300,
            progressColor: AppTheme.primaryColor,
            barRadius: const Radius.circular(4),
            padding: const EdgeInsets.symmetric(horizontal: 0),
            animation: true,
            animateFromLastPercent: true,
          ),

          const SizedBox(height: 16),

          // Controles de audio
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: _speak,
                    icon: Icon(_isSpeaking ? Icons.pause : Icons.play_arrow),
                    label: Text(_isSpeaking ? 'إيقاف' : 'تشغيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Control de velocidad de lectura
              Directionality(
                textDirection:
                    TextDirection.rtl, // Para que el texto esté a la derecha
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('سرعة القراءة:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(
                      child: Slider(
                        value: _speechRate,
                        min: 0.1,
                        max: 1.0,
                        divisions: 9,
                        label: _getSpeechRateLabel(),
                        onChanged: (value) => _changeSpeechRate(value),
                        activeColor: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Instrucciones adicionales
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.withAlpha(50),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber, width: 1),
            ),
            child: const Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'استمع جيدًا للنطق وانتبه للكلمات المميزة باللون الأزرق أثناء النطق',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Página 2: Leer con texto visible
  Widget _buildReadingPage() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Espacio en lugar de instrucciones
          const SizedBox(height: 8),

          // Área de grabación o resultados
          Center(
            child: !_showResults || _isRecording
                ? Column(
                    children: [
                      AvatarGlow(
                        animate: _isRecording,
                        glowColor: Colors.red,
                        endRadius: 75.0,
                        duration: const Duration(milliseconds: 2000),
                        repeat: true,
                        child: GestureDetector(
                          onTap: _handleRecording,
                          child: Material(
                            elevation: 8.0,
                            shape: const CircleBorder(),
                            child: CircleAvatar(
                              backgroundColor: _isRecording
                                  ? Colors.red.shade50
                                  : Colors.grey[100],
                              radius: 35.0,
                              child: Icon(
                                _isRecording ? Icons.mic : Icons.mic_none,
                                color: _isRecording
                                    ? Colors.red
                                    : AppTheme.primaryColor,
                                size: 30,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          Text(
                            _isRecording
                                ? ''
                                : 'اقرأ الجملة بصوت عالٍ وسنقيم نطقك',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Texto para leer
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: AppTheme.primaryColor.withAlpha(100)),
                            ),
                            child: Directionality(
                              textDirection: TextDirection
                                  .ltr, // Forzar dirección de izquierda a derecha
                              child: Text(
                                widget.sentence.englishText,
                                style: const TextStyle(
                                  fontSize: 18,
                                  height: 1.5,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                : // Mostrar resultados en lugar del botón de grabación
                Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green, width: 1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتيجة النطق:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // Botón para volver a grabar
                            ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  _showResults = false;
                                });
                              },
                              icon: const Icon(Icons.refresh, size: 16),
                              label: const Text('إعادة'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade700,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                minimumSize: const Size(0, 32),
                                textStyle: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearPercentIndicator(
                          lineHeight: 20.0,
                          percent: _pronunciationScore,
                          center: Text(
                            '${(_pronunciationScore * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          backgroundColor: Colors.grey.shade300,
                          progressColor: Colors.green,
                          barRadius: const Radius.circular(10),
                          padding: const EdgeInsets.symmetric(horizontal: 0),
                          animation: true,
                        ),
                        const SizedBox(height: 16),

                        // Botón para reproducir la grabación
                        Center(
                          child: ElevatedButton.icon(
                            onPressed: _playRecording,
                            icon: Icon(_isPlayingRecording
                                ? Icons.stop
                                : Icons.play_arrow),
                            label: Text(_isPlayingRecording
                                ? 'إيقاف'
                                : 'استماع للتسجيل'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Mostrar el texto reconocido
                        const Text(
                          'النص الذي تم التعرف عليه:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(
                              _recognizedText.isEmpty
                                  ? 'لم يتم التعرف على أي نص'
                                  : _recognizedText,
                              style: TextStyle(
                                fontSize: 14,
                                color: _recognizedText.isEmpty
                                    ? Colors.grey
                                    : Colors.black,
                                fontStyle: _recognizedText.isEmpty
                                    ? FontStyle.italic
                                    : FontStyle.normal,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Palabras correctas/incorrectas
                        const Text(
                          'تحليل الكلمات:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Directionality(
                          textDirection: TextDirection.ltr,
                          child: RichText(
                            text: TextSpan(
                              style: const TextStyle(
                                  fontSize: 16, color: Colors.black),
                              children: _getSimulatedPronunciationResult(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  // Página 3: Prueba de memoria
  Widget _buildMemoryPage() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Espacio en lugar de instrucciones
          const SizedBox(height: 8),

          // Botón para mostrar la frase original
          Center(
            child: OutlinedButton.icon(
              onPressed: () {
                // Mostrar un diálogo con la frase original
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('الجملة الأصلية'),
                    content: Directionality(
                      textDirection: TextDirection
                          .ltr, // Forzar dirección de izquierda a derecha
                      child: Text(
                        widget.sentence.englishText,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('إغلاق'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.visibility),
              label: const Text('عرض الجملة الأصلية'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Área de grabación o resultados
          Center(
            child: !_showResults || _isRecording
                ? Column(
                    children: [
                      AvatarGlow(
                        animate: _isRecording,
                        glowColor: Colors.red,
                        endRadius: 75.0,
                        duration: const Duration(milliseconds: 2000),
                        repeat: true,
                        child: GestureDetector(
                          onTap: _handleRecording,
                          child: Material(
                            elevation: 8.0,
                            shape: const CircleBorder(),
                            child: CircleAvatar(
                              backgroundColor: _isRecording
                                  ? Colors.red.shade50
                                  : Colors.grey[100],
                              radius: 35.0,
                              child: Icon(
                                _isRecording ? Icons.mic : Icons.mic_none,
                                color: _isRecording
                                    ? Colors.red
                                    : AppTheme.primaryColor,
                                size: 30,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          Text(
                            _isRecording
                                ? ''
                                : 'اختبار الحفظ: حاول قراءة الجملة من الذاكرة',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Pistas para el texto oculto
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                              border:
                                  Border.all(color: Colors.blue.withAlpha(100)),
                            ),
                            child: Directionality(
                              textDirection: TextDirection.ltr,
                              child: Text(
                                _getHiddenText(),
                                style: const TextStyle(
                                  fontSize: 18,
                                  height: 1.5,
                                  letterSpacing: 1.5,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                : // Mostrar resultados en lugar del botón de grabación
                Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue, width: 1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'نتيجة الحفظ:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // Botón para volver a grabar
                            ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  _showResults = false;
                                });
                              },
                              icon: const Icon(Icons.refresh, size: 16),
                              label: const Text('إعادة'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade700,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                minimumSize: const Size(0, 32),
                                textStyle: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearPercentIndicator(
                          lineHeight: 20.0,
                          percent: _memoryScore,
                          center: Text(
                            '${(_memoryScore * 100).toInt()}%',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          backgroundColor: Colors.grey.shade300,
                          progressColor: Colors.blue,
                          barRadius: const Radius.circular(10),
                          padding: const EdgeInsets.symmetric(horizontal: 0),
                          animation: true,
                        ),
                        const SizedBox(height: 16),

                        // Botón para reproducir la grabación
                        Center(
                          child: ElevatedButton.icon(
                            onPressed: _playRecording,
                            icon: Icon(_isPlayingRecording
                                ? Icons.stop
                                : Icons.play_arrow),
                            label: Text(_isPlayingRecording
                                ? 'إيقاف'
                                : 'استماع للتسجيل'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Mostrar el texto reconocido
                        const Text(
                          'النص الذي تم التعرف عليه:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Directionality(
                            textDirection: TextDirection.ltr,
                            child: Text(
                              _recognizedText.isEmpty
                                  ? 'لم يتم التعرف على أي نص'
                                  : _recognizedText,
                              style: TextStyle(
                                fontSize: 14,
                                color: _recognizedText.isEmpty
                                    ? Colors.grey
                                    : Colors.black,
                                fontStyle: _recognizedText.isEmpty
                                    ? FontStyle.italic
                                    : FontStyle.normal,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Comparación con el texto original
                        const Text(
                          'مقارنة بالجملة الأصلية:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Simulación de palabras recordadas/olvidadas
                        Directionality(
                          textDirection: TextDirection.ltr,
                          child: RichText(
                            text: TextSpan(
                              style: const TextStyle(
                                  fontSize: 16, color: Colors.black),
                              children: _getSimulatedMemoryResult(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  // Métodos auxiliares

  String _getPageTitle(int page) {
    switch (page) {
      case 1:
        return 'الاستماع والنطق';
      case 2:
        return 'القراءة والتقييم';
      case 3:
        return 'اختبار الحفظ';
      default:
        return 'اختبار الفهم';
    }
  }

  String _getHiddenText() {
    // Reemplazar cada palabra con guiones bajos, manteniendo la puntuación
    final words = widget.sentence.englishText.split(' ');
    final hiddenWords = words.map((word) {
      // Extraer puntuación al final de la palabra
      final punctuation = RegExp(r'[^\w\s]$').stringMatch(word) ?? '';
      final cleanWord = word.replaceAll(RegExp(r'[^\w\s]'), '');

      // Mostrar la primera letra y reemplazar el resto con guiones
      if (cleanWord.length > 2) {
        return '${cleanWord[0]}${'_' * (cleanWord.length - 2)}${cleanWord[cleanWord.length - 1]}$punctuation';
      } else if (cleanWord.length == 2) {
        return '${cleanWord[0]}_$punctuation';
      } else {
        return '$cleanWord$punctuation';
      }
    }).toList();

    return hiddenWords.join(' ');
  }

  List<TextSpan> _getSimulatedPronunciationResult() {
    final result = <TextSpan>[];

    // Si tenemos resultados reales de reconocimiento de voz, usarlos
    if (_speechComparisonResult != null &&
        _speechComparisonResult!.containsKey('wordResults')) {
      final wordResults =
          _speechComparisonResult!['wordResults'] as List<Map<String, dynamic>>;

      for (final wordResult in wordResults) {
        final word = wordResult['word'] as String;
        final isCorrect = wordResult['isCorrect'] as bool;

        result.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isCorrect ? Colors.green : Colors.red,
              fontWeight: isCorrect ? FontWeight.normal : FontWeight.bold,
            ),
          ),
        );
      }

      // Ya no necesitamos agregar el texto reconocido aquí
      // porque lo mostramos en un widget separado
    } else {
      // Simulación si no hay resultados reales
      final words = widget.sentence.englishText.split(' ');

      for (int i = 0; i < words.length; i++) {
        final word = words[i];
        // Simular que algunas palabras son pronunciadas incorrectamente
        final isCorrect = i % 4 != 0; // Cada cuarta palabra es "incorrecta"

        result.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isCorrect ? Colors.green : Colors.red,
              fontWeight: isCorrect ? FontWeight.normal : FontWeight.bold,
            ),
          ),
        );
      }
    }

    return result;
  }

  List<TextSpan> _getSimulatedMemoryResult() {
    final result = <TextSpan>[];

    // Si tenemos resultados reales de reconocimiento de voz, usarlos
    if (_speechComparisonResult != null &&
        _speechComparisonResult!.containsKey('wordResults')) {
      final wordResults =
          _speechComparisonResult!['wordResults'] as List<Map<String, dynamic>>;

      for (final wordResult in wordResults) {
        final word = wordResult['word'] as String;
        final isCorrect = wordResult['isCorrect'] as bool;

        result.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isCorrect ? Colors.blue : Colors.red,
              fontWeight: isCorrect ? FontWeight.normal : FontWeight.bold,
            ),
          ),
        );
      }

      // Ya no necesitamos agregar el texto reconocido aquí
      // porque lo mostramos en un widget separado
    } else {
      // Simulación si no hay resultados reales
      final words = widget.sentence.englishText.split(' ');

      for (int i = 0; i < words.length; i++) {
        final word = words[i];
        // Simular que algunas palabras son olvidadas
        final isRemembered = i % 3 != 0; // Cada tercera palabra es "olvidada"

        result.add(
          TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isRemembered ? Colors.blue : Colors.red,
              fontWeight: isRemembered ? FontWeight.normal : FontWeight.bold,
            ),
          ),
        );
      }
    }

    return result;
  }

  // Método para obtener la etiqueta de velocidad de lectura
  String _getSpeechRateLabel() {
    if (_speechRate <= 0.3) {
      return 'بطيئة';
    } else if (_speechRate <= 0.6) {
      return 'متوسطة';
    } else {
      return 'سريعة';
    }
  }

  // Método para construir el texto resaltado durante la reproducción
  List<TextSpan> _buildHighlightedText() {
    final result = <TextSpan>[];

    // Usar un tamaño de fuente constante para evitar movimientos
    const double baseFontSize = 18.0;

    // Si no hay palabra actual resaltada, mostrar todo el texto en negro
    if (_currentWordIndex < 0 || _currentWordIndex >= _words.length) {
      // Unir todas las palabras con espacios
      final String fullText = _words.join(' ');
      result.add(
        TextSpan(
          text: fullText,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.normal,
            fontSize: baseFontSize,
          ),
        ),
      );
      return result;
    }

    // Añadir palabras antes de la palabra resaltada
    if (_currentWordIndex > 0) {
      final String beforeText =
          '${_words.sublist(0, _currentWordIndex).join(' ')} ';
      result.add(
        TextSpan(
          text: beforeText,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.normal,
            fontSize: baseFontSize,
          ),
        ),
      );
    }

    // Añadir la palabra resaltada
    final String highlightedWord = _words[_currentWordIndex];
    result.add(
      TextSpan(
        text: highlightedWord +
            (_currentWordIndex < _words.length - 1 ? ' ' : ''),
        style: const TextStyle(
          color: Colors.blue,
          fontWeight: FontWeight.bold,
          fontSize: baseFontSize,
          decoration: TextDecoration.underline,
          decorationColor: Colors.blue,
          decorationThickness: 2.0,
          shadows: [
            Shadow(
              color: Colors.blue,
              blurRadius: 2.0,
            )
          ],
        ),
      ),
    );

    // Añadir palabras después de la palabra resaltada
    if (_currentWordIndex < _words.length - 1) {
      final String afterText = _words.sublist(_currentWordIndex + 1).join(' ');
      result.add(
        TextSpan(
          text: afterText,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.normal,
            fontSize: baseFontSize,
          ),
        ),
      );
    }

    return result;
  }
}
