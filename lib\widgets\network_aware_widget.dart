import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/connectivity_service.dart';

class NetworkAwareWidget extends StatelessWidget {
  final Widget onlineChild;
  final Widget offlineChild;

  const NetworkAwareWidget({
    super.key,
    required this.onlineChild,
    required this.offlineChild,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityService>(
      builder: (context, connectivity, child) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: connectivity.isOnline ? onlineChild : offlineChild,
        );
      },
    );
  }
}
