import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_view_model.dart';

import 'login_screen.dart';
import 'main_page.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    // تقليل مدة الانتظار لأن الـ native splash screen سيظهر أولاً
    // إزالة التأخير تمامًا للانتقال الفوري
    // await Future.delayed(const Duration(milliseconds: 300));
    if (!mounted) return;

    try {
      final authVM = context.read<AuthViewModel>();

      // محاولة تحديث حالة المصادقة واستعادة جلسة المستخدم إذا كانت موجودة
      final bool isAuthenticated = await authVM.refreshUserData();

      if (!mounted) return;

      if (isAuthenticated) {
        debugPrint('المستخدم مسجل الدخول، الانتقال إلى الصفحة الرئيسية');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MainPage()),
        );
      } else {
        debugPrint('المستخدم غير مسجل الدخول، الانتقال إلى صفحة تسجيل الدخول');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، إعادة المحاولة بعد فترة قصيرة
      debugPrint('خطأ في تهيئة الشاشة الرئيسية: $e');
      if (mounted) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _initialize();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: isDarkMode
              ? Theme.of(context).scaffoldBackgroundColor
              : Colors.white,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // الصورة والمحتوى
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الصورة في الأعلى
                    Image.asset(
                      'assets/images/10again_splash_screen.png',
                      width: size.width * 0.8,
                      height: size.height * 0.4,
                      fit: BoxFit.contain,
                    ),

                    const SizedBox(height: 40),

                    // مؤشر التحميل
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.primary,
                      ),
                    ),

                    const SizedBox(height: 20),

                    // اسم التطبيق
                    Text(
                      '10 Again',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? Colors.white
                            : Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
