import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';
import '../services/connectivity_service.dart';
import 'main_page.dart';

class InitialDataLoadingScreen extends StatefulWidget {
  const InitialDataLoadingScreen({super.key});

  @override
  State<InitialDataLoadingScreen> createState() =>
      _InitialDataLoadingScreenState();
}

class _InitialDataLoadingScreenState extends State<InitialDataLoadingScreen> {
  bool _isLoading = true;
  String _status = 'جاري تحميل البيانات...';
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      final connectivityService = context.read<ConnectivityService>();
      if (!connectivityService.isOnline) {
        setState(() {
          _error =
              'لا يوجد اتصال بالإنترنت. يرجى التحقق من الاتصال والمحاولة مرة أخرى.';
          _isLoading = false;
        });
        return;
      }

      final sentenceVM = context.read<SentenceViewModel>();

      setState(() => _status = 'جاري تحميل الفئات...');
      await sentenceVM.loadCategories();

      setState(() => _status = 'جاري تحميل الجمل...');
      await sentenceVM.loadSentences();

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MainPage()),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isLoading) ...[
                const CircularProgressIndicator(),
                const SizedBox(height: 24),
                Text(
                  _status,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
              ] else if (_error != null) ...[
                const Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  _error!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                      _error = null;
                    });
                    _loadInitialData();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
