import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class ReadConversationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة جملة محادثة مقروءة لمسار تعلم المستخدم
  Future<void> addReadConversationSentence({
    required String userId,
    required int conversationGroupId,
    required String sentenceId,
    required int order,
  }) async {
    final docRef = _firestore
        .collection('users')
        .doc(userId)
        .collection('readConversations')
        .doc(conversationGroupId.toString());
    await docRef.set({
      'sentences': FieldValue.arrayUnion([
        {'sentenceId': sentenceId, 'order': order}
      ])
    }, SetOptions(merge: true));
  }

  /// جلب معرفات جمل المحادثة المقروءة لمسار تعلم المستخدم بنفس الترتيب
  Future<List<String>> getReadConversationSentences({
    required String userId,
    required int conversationGroupId,
  }) async {
    final doc = await _firestore
        .collection('users')
        .doc(userId)
        .collection('readConversations')
        .doc(conversationGroupId.toString())
        .get();
    if (!doc.exists) return [];
    final data = doc.data();
    if (data == null || data['sentences'] == null) return [];
    final List sentences = data['sentences'];
    sentences.sort((a, b) => (a['order'] as int).compareTo(b['order'] as int));
    return sentences.map<String>((e) => e['sentenceId'] as String).toList();
  }
}
