import 'package:flutter/material.dart';

/// زر مع تأثير وميض داخلي
class FlashingButton extends StatefulWidget {
  final IconData icon;
  final String label;
  final bool isSmallScreen;
  final VoidCallback onPressed;

  const FlashingButton({
    Key? key,
    required this.icon,
    required this.label,
    required this.isSmallScreen,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<FlashingButton> createState() => _FlashingButtonState();
}

class _FlashingButtonState extends State<FlashingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    // إنشاء متحكم الرسوم المتحركة بسرعة مناسبة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    )..repeat(reverse: true); // تكرار الرسوم المتحركة مع عكس الاتجاه
    
    // إنشاء رسوم متحركة للتدرج اللوني
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // الألوان الأساسية
    final primaryColor = Theme.of(context).primaryColor;
    final secondaryColor = Theme.of(context).colorScheme.secondary;
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return ElevatedButton.icon(
          icon: Icon(
            widget.icon,
            size: widget.isSmallScreen ? 16.0 : 24.0,
            color: Colors.white,
          ),
          label: Text(
            widget.label,
            style: TextStyle(
              fontSize: widget.isSmallScreen ? 12.0 : 14.0,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          style: ElevatedButton.styleFrom(
            padding: widget.isSmallScreen
                ? const EdgeInsets.symmetric(horizontal: 10, vertical: 6)
                : const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(widget.isSmallScreen ? 8.0 : 12.0),
            ),
            // استخدام تدرج لوني متحرك للخلفية
            backgroundColor: Color.lerp(primaryColor, secondaryColor, _animation.value),
            // إضافة تأثير ظل للزر
            elevation: 3,
            shadowColor: Color.lerp(primaryColor, secondaryColor, _animation.value),
          ),
          onPressed: widget.onPressed,
        );
      },
    );
  }
}
