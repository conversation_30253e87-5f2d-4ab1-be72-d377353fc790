import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:test05/viewmodels/sentence_view_model.dart';
import '../models/sentence_model.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../services/hive_sentence_service.dart';

class ReadSentencesScreen extends StatefulWidget {
  const ReadSentencesScreen({super.key});

  @override
  ReadSentencesScreenState createState() => ReadSentencesScreenState();
}

class ReadSentencesScreenState extends State<ReadSentencesScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  FilterOptions _filterOptions = FilterOptions();
  List<String> _categories = [];

  // Lista local para almacenar las frases cargadas
  final List<SentenceModel> _loadedSentences = [];
  bool _hasMoreSentences = true;
  bool _initialLoadDone = false;

  // عدادات الجمل
  int _totalReadCount = 0;
  int _filteredCount = 0;

  @override
  void initState() {
    super.initState();
    _loadCategories();

    // Configurar el listener para la paginación
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    // Verificar si estamos cerca del final de la lista
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreSentences) {
        _loadMore();
      }
    }
  }

  Future<void> _loadCategories() async {
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
    await viewModel.loadCategories();
    setState(() {
      _categories = viewModel.categories;
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _showFilterBottomSheet() {
    FilterBottomSheet.show(
      context: context,
      initialFilters: _filterOptions,
      categories: _categories,
      onApplyFilters: (filters) {
        setState(() {
          _filterOptions = filters;

          // إعادة تطبيق الفلترة وتحديث العدادات
          List<SentenceModel> allSentences = [];

          // استخدام المحول للحصول على جميع الجمل المقروءة
          final viewModel =
              Provider.of<SentenceViewModel>(context, listen: false);
          if (viewModel.adapter != null) {
            final hiveSentenceService =
                Provider.of<HiveSentenceService>(context, listen: false);
            final hiveSentences = hiveSentenceService.getReadSentences();
            allSentences = hiveSentences
                .map((s) => viewModel.adapter!.toSentenceModel(s))
                .toList();
          }

          // تطبيق الفلترة
          List<SentenceModel> filteredSentences = _applyFilters(allSentences);

          // تحديث العدادات
          _totalReadCount = allSentences.length;
          _filteredCount = filteredSentences.length;

          // تحديث القائمة المعروضة
          _loadedSentences.clear();
          _loadedSentences.addAll(filteredSentences);
        });
      },
    );
  }

  Future<void> _loadInitialSentences() async {
    if (_initialLoadDone) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final viewModel = Provider.of<SentenceViewModel>(context, listen: false);

      // الحصول على الجمل المقروءة من التخزين المحلي أولاً
      List<SentenceModel> localReadSentences = [];

      // استخدام المحول إذا كان متاحًا للحصول على الجمل المقروءة من التخزين المحلي
      if (viewModel.adapter != null) {
        debugPrint(
            'استخدام المحول للحصول على الجمل المقروءة من التخزين المحلي');
        // استخدام HiveSentenceService مباشرة للحصول على الجمل المقروءة
        final hiveSentenceService =
            Provider.of<HiveSentenceService>(context, listen: false);
        final hiveSentences = hiveSentenceService.getReadSentences();
        localReadSentences = hiveSentences
            .map((s) => viewModel.adapter!.toSentenceModel(s))
            .toList();
        debugPrint(
            'تم العثور على ${localReadSentences.length} جملة مقروءة في التخزين المحلي');
      }

      // Using only local storage for read sentences
      final List<SentenceModel> allSentences = [];

      // Add sentences from local storage
      for (final localSentence in localReadSentences) {
        if (!allSentences.any((s) => s.id == localSentence.id)) {
          allSentences.add(localSentence);
        }
      }

      // Log the count of read sentences from local storage
      debugPrint(
          "Found ${allSentences.length} read sentences in local storage");

      debugPrint('إجمالي الجمل المقروءة بعد الدمج: ${allSentences.length}');

      // إذا كانت هناك جمل معلقة للمزامنة، نحاول إضافتها إلى القائمة
      final pendingSentences =
          await viewModel.getSentenceService().getPendingSyncSentences();
      if (pendingSentences.isNotEmpty) {
        debugPrint(
            'Found ${pendingSentences.length} pending sentences to display');

        // الحصول على جميع الجمل
        final allAvailableSentences = await viewModel.getAllSentences();

        // إضافة الجمل المعلقة إلى القائمة
        for (final sentenceId in pendingSentences) {
          // التحقق مما إذا كانت الجملة موجودة في قائمة الجمل المقروءة بالفعل
          if (!allSentences.any((s) => s.id == sentenceId)) {
            // البحث عن الجملة في جميع الجمل المتاحة
            final sentence = allAvailableSentences.firstWhere(
              (s) => s.id == sentenceId,
              orElse: () => SentenceModel(
                id: sentenceId,
                englishText: 'جملة غير متاحة حاليًا',
                arabicText: 'سيتم مزامنتها عند عودة الاتصال',
                category: 'غير متاح',
                createdAt: DateTime.now(),
                readBy: {},
                isFavorite: false,
                difficulty: 'medium',
                isReadByCurrentUser: true,
              ),
            );

            // إضافة الجملة إلى القائمة
            allSentences.add(sentence.copyWith(isReadByCurrentUser: true));
          }
        }
      }

      // تطبيق الفلترة على الجمل المحملة
      List<SentenceModel> filteredSentences = _applyFilters(allSentences);

      setState(() {
        _totalReadCount = allSentences.length;
        _filteredCount = filteredSentences.length;
        _loadedSentences.clear();
        _loadedSentences.addAll(filteredSentences);
        _hasMoreSentences = false; // No more sentences from Firebase
        _isLoadingMore = false;
        _initialLoadDone = true;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          _initialLoadDone = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل الجمل: $e')),
        );
      }
    }
  }

  Future<void> _resetAndReload() async {
    setState(() {
      _loadedSentences.clear();
      _hasMoreSentences = true;
      _initialLoadDone = false;
      // إعادة تعيين العدادات (سيتم تحديثها في _loadInitialSentences)
      _totalReadCount = 0;
      _filteredCount = 0;
    });
    await _loadInitialSentences();
  }

  Future<void> _loadMore() async {
    if (!_isLoadingMore && _hasMoreSentences) {
      setState(() {
        _isLoadingMore = true;
      });

      try {
        final viewModel =
            Provider.of<SentenceViewModel>(context, listen: false);
        final lastReadAt = _loadedSentences.isNotEmpty
            ? await viewModel.getReadTimestamp(_loadedSentences.last.id)
            : null;

        final newSentences = await viewModel.getReadSentencesAsList(
          limit: 10,
          category: _filterOptions.category,
          startDate: _filterOptions.startDate,
          endDate: _filterOptions.endDate,
          difficulty: _filterOptions.difficulty, // إضافة فلتر الصعوبة
          descending: _filterOptions.sortOrder == SortOrder.newest,
          lastReadAt: lastReadAt,
          excludeIds: _loadedSentences.map((s) => s.id).toList(),
        );

        if (mounted) {
          setState(() {
            if (newSentences.isEmpty) {
              _hasMoreSentences = false;
            } else {
              _loadedSentences.addAll(newSentences);
            }
            _isLoadingMore = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('حدث خطأ أثناء تحميل المزيد من الجمل')),
          );
        }
      }
    }
  }

  // تطبيق الفلترة على قائمة الجمل
  List<SentenceModel> _applyFilters(List<SentenceModel> sentences) {
    return sentences.where((sentence) {
      // تطبيق فلتر الفئة
      if (_filterOptions.category != null &&
          _filterOptions.category!.isNotEmpty) {
        if (sentence.category != _filterOptions.category) {
          return false;
        }
      }

      // تطبيق فلتر الصعوبة
      if (_filterOptions.difficulty != null &&
          _filterOptions.difficulty!.isNotEmpty) {
        if (sentence.difficulty != _filterOptions.difficulty) {
          return false;
        }
      }

      // تطبيق فلتر التاريخ
      if (_filterOptions.startDate != null || _filterOptions.endDate != null) {
        // الحصول على تاريخ قراءة الجملة
        final readDate = sentence
            .createdAt; // يمكن استبداله بتاريخ القراءة الفعلي إذا كان متاحًا

        if (_filterOptions.startDate != null) {
          final startDate = DateTime(_filterOptions.startDate!.year,
              _filterOptions.startDate!.month, _filterOptions.startDate!.day);
          if (readDate.isBefore(startDate)) {
            return false;
          }
        }

        if (_filterOptions.endDate != null) {
          final endDate = DateTime(
              _filterOptions.endDate!.year,
              _filterOptions.endDate!.month,
              _filterOptions.endDate!.day,
              23,
              59,
              59);
          if (readDate.isAfter(endDate)) {
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  String _buildFilterDescription() {
    final List<String> parts = [];

    if (_filterOptions.category != null) {
      parts.add('الفئة: ${_filterOptions.category}');
    }

    if (_filterOptions.difficulty != null) {
      parts.add('الصعوبة: ${_getDifficultyText(_filterOptions.difficulty)}');
    }

    if (_filterOptions.sortOrder == SortOrder.newest) {
      parts.add('الترتيب: الأحدث');
    } else {
      parts.add('الترتيب: الأقدم');
    }

    if (_filterOptions.startDate != null) {
      parts.add(
          'من: ${_filterOptions.startDate!.day}/${_filterOptions.startDate!.month}/${_filterOptions.startDate!.year}');
    }

    if (_filterOptions.endDate != null) {
      parts.add(
          'إلى: ${_filterOptions.endDate!.day}/${_filterOptions.endDate!.month}/${_filterOptions.endDate!.year}');
    }

    return parts.join(' | ');
  }

  // دالة مساعدة لعرض نص مستوى الصعوبة
  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  // دالة مساعدة للحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // دالة مساعدة للحصول على أيقونة مستوى الصعوبة
  IconData _getDifficultyIcon(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Icons.sentiment_satisfied_alt;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final titleFontSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 18.0 : 20.0);
    final emptyIconSize =
        isVerySmallScreen ? 48.0 : (isSmallScreen ? 56.0 : 64.0);
    final emptyTextSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 17.0 : 18.0);
    final filterIconSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 17.0 : 18.0);
    final filterTextSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 13.0 : 14.0);
    final filterPadding = isVerySmallScreen
        ? const EdgeInsets.symmetric(horizontal: 12, vertical: 6)
        : (isSmallScreen
            ? const EdgeInsets.symmetric(horizontal: 14, vertical: 7)
            : const EdgeInsets.symmetric(horizontal: 16, vertical: 8));
    final cardMargin = isVerySmallScreen ? 6.0 : (isSmallScreen ? 7.0 : 8.0);
    final cardPadding =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0);
    final englishTextSize =
        isVerySmallScreen ? 14.0 : (isSmallScreen ? 15.0 : 16.0);
    final arabicTextSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 13.0 : 14.0);
    final categoryTextSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 13.0 : 14.0);
    final spacingHeight = isVerySmallScreen ? 6.0 : (isSmallScreen ? 7.0 : 8.0);
    final favoriteIconSize =
        isVerySmallScreen ? 20.0 : (isSmallScreen ? 22.0 : 24.0);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الجمل المقروءة',
          style: TextStyle(fontSize: titleFontSize),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
            tooltip: 'تصفية',
          ),
        ],
      ),
      body: FutureBuilder<void>(
        future: _loadInitialSentences(),
        builder: (context, snapshot) {
          if (!_initialLoadDone) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_loadedSentences.isEmpty) {
            return RefreshIndicator(
              onRefresh: () async {
                await _resetAndReload();
              },
              child: ListView(
                // إضافة physics للسماح بالسحب للأسفل حتى عندما تكون القائمة فارغة
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  // عنصر رئيسي بارتفاع كافٍ للسماح بالسحب
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.9,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.menu_book_outlined,
                            size: emptyIconSize,
                            color: Colors.grey,
                          ),
                          SizedBox(height: spacingHeight * 2),
                          Text(
                            'لا توجد جمل مقروءة بعد',
                            style: TextStyle(
                              fontSize: emptyTextSize,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: spacingHeight),
                          Text(
                            'اسحب للأسفل للتحديث',
                            style: TextStyle(
                              fontSize: filterTextSize,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // بطاقة عدادات الجمل
              Card(
                margin: EdgeInsets.all(cardMargin),
                child: Padding(
                  padding: EdgeInsets.all(cardPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'إجمالي الجمل المقروءة: $_totalReadCount',
                        style: TextStyle(
                          fontSize: filterTextSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'الجمل المعروضة: $_filteredCount',
                        style: TextStyle(
                          fontSize: filterTextSize,
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Active filters indicator
              if (_filterOptions.category != null ||
                  _filterOptions.difficulty != null ||
                  _filterOptions.startDate != null ||
                  _filterOptions.endDate != null ||
                  _filterOptions.sortOrder != SortOrder.newest)
                Container(
                  padding: filterPadding,
                  color: Theme.of(context).primaryColor.withAlpha(26),
                  child: Row(
                    children: [
                      Icon(Icons.filter_list, size: filterIconSize),
                      SizedBox(width: spacingHeight),
                      Expanded(
                        child: Text(
                          _buildFilterDescription(),
                          style: TextStyle(fontSize: filterTextSize),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.clear, size: filterIconSize),
                        padding: EdgeInsets.all(isVerySmallScreen ? 4.0 : 8.0),
                        constraints: BoxConstraints(
                          minWidth: isVerySmallScreen ? 32.0 : 40.0,
                          minHeight: isVerySmallScreen ? 32.0 : 40.0,
                        ),
                        onPressed: () {
                          setState(() {
                            _filterOptions = FilterOptions();
                            _resetAndReload();
                          });
                        },
                        tooltip: 'مسح التصفية',
                      ),
                    ],
                  ),
                ),

              // Sentences list with pull-to-refresh
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await _resetAndReload();
                  },
                  child: ListView.builder(
                    // إضافة physics للسماح بالسحب للأسفل حتى عندما تكون القائمة قصيرة
                    physics: const AlwaysScrollableScrollPhysics(),
                    controller: _scrollController,
                    // إضافة عنصر إضافي في نهاية القائمة لضمان إمكانية السحب
                    itemCount: _loadedSentences.length + 2,
                    itemBuilder: (context, index) {
                      if (index == _loadedSentences.length) {
                        if (_isLoadingMore) {
                          return Padding(
                            padding: EdgeInsets.all(cardPadding),
                            child: const Center(
                                child: CircularProgressIndicator()),
                          );
                        } else if (!_hasMoreSentences) {
                          return Padding(
                            padding: EdgeInsets.all(cardPadding),
                            child: Center(
                              child: Text(
                                'لا توجد المزيد من الجمل',
                                style: TextStyle(fontSize: filterTextSize),
                              ),
                            ),
                          );
                        } else {
                          // مساحة فارغة عندما يكون هناك المزيد من العناصر ولكن لم يتم تحميلها بعد
                          return SizedBox(
                              height: isVerySmallScreen ? 60.0 : 80.0);
                        }
                      } else if (index == _loadedSentences.length + 1) {
                        // عنصر إضافي في نهاية القائمة لضمان إمكانية السحب
                        return SizedBox(
                            height: MediaQuery.of(context).size.height * 0.3);
                      }

                      final sentence = _loadedSentences[index];
                      return Card(
                        margin: EdgeInsets.all(cardMargin),
                        elevation:
                            Theme.of(context).brightness == Brightness.dark
                                ? 2
                                : 1,
                        child: Padding(
                          padding: EdgeInsets.all(cardPadding),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                sentence.englishText,
                                style: TextStyle(
                                  fontSize: englishTextSize,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: spacingHeight),
                              Text(
                                sentence.arabicText,
                                style: TextStyle(
                                  fontSize: arabicTextSize,
                                  color: Colors.grey,
                                ),
                              ),
                              SizedBox(height: spacingHeight),
                              Row(
                                children: [
                                  // عرض الفئة
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            isVerySmallScreen ? 6.0 : 8.0,
                                        vertical:
                                            isVerySmallScreen ? 3.0 : 4.0),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withAlpha(40)
                                          : Theme.of(context)
                                              .primaryColor
                                              .withAlpha(26),
                                      borderRadius: BorderRadius.circular(
                                          isVerySmallScreen ? 6.0 : 8.0),
                                      border: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Border.all(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .primary
                                                  .withAlpha(100),
                                              width: 1.0,
                                            )
                                          : null,
                                    ),
                                    child: Text(
                                      'الفئة: ${sentence.category}',
                                      style: TextStyle(
                                        fontSize: categoryTextSize,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Theme.of(context)
                                                .colorScheme
                                                .primary
                                            : Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  // عرض مستوى الصعوبة
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal:
                                            isVerySmallScreen ? 6.0 : 8.0,
                                        vertical:
                                            isVerySmallScreen ? 3.0 : 4.0),
                                    decoration: BoxDecoration(
                                      color: _getDifficultyColor(
                                          sentence.difficulty),
                                      borderRadius: BorderRadius.circular(
                                          isVerySmallScreen ? 6.0 : 8.0),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          _getDifficultyIcon(
                                              sentence.difficulty),
                                          size: 14,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _getDifficultyText(
                                              sentence.difficulty),
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  StatefulBuilder(
                                    builder: (context, setIconState) {
                                      return Consumer<SentenceViewModel>(
                                        builder: (context, viewModel, _) {
                                          return IconButton(
                                            icon: Icon(
                                              sentence.isFavoriteByCurrentUser
                                                  ? Icons.favorite
                                                  : Icons.favorite_border,
                                              color: sentence
                                                      .isFavoriteByCurrentUser
                                                  ? Colors.red
                                                  : Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? Colors.white
                                                      : Colors.grey.shade600,
                                              size: favoriteIconSize,
                                            ),
                                            padding: EdgeInsets.all(
                                                isVerySmallScreen ? 4.0 : 8.0),
                                            constraints: BoxConstraints(
                                              minWidth: isVerySmallScreen
                                                  ? 32.0
                                                  : 40.0,
                                              minHeight: isVerySmallScreen
                                                  ? 32.0
                                                  : 40.0,
                                            ),
                                            onPressed: () {
                                              // استخدام toggleFavorite فقط - سيقوم بتحديث الحالة محليًا وفي جميع القوائم
                                              viewModel
                                                  .toggleFavorite(sentence);

                                              // تحديث حالة الأيقونة مباشرة (للتأكد من التحديث الفوري في الواجهة)
                                              setIconState(() {});
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
