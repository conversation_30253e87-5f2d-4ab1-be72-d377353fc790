import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/sentence_model.dart';
import 'file_cache_service.dart';
import 'local_sentence_storage_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'connectivity_service.dart';

class SentenceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  late final FileCacheService _cacheService;
  late final LocalSentenceStorageService _localStorageService;
  final Connectivity _connectivity = Connectivity();

  SentenceService(
      {FileCacheService? cacheService,
      LocalSentenceStorageService? localStorageService}) {
    _cacheService = cacheService ?? FileCacheService();
    _localStorageService = localStorageService ?? LocalSentenceStorageService();
  }
  final String _sentencesCollection = 'sentences';

  // فحص حالة الاتصال بالإنترنت
  Future<bool> isOnline() async {
    try {
      // استخدام القيمة المخزنة مسبقًا إذا كانت متاحة
      // هذا يقلل من عدد عمليات التحقق من الاتصال
      final connectivityService = ConnectivityService();
      return await connectivityService.checkConnectivity();
    } catch (e) {
      // في حالة الخطأ، استخدام الطريقة التقليدية
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    }
  }

  Stream<List<SentenceModel>> getFavoriteSentences(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('favorites')
        .snapshots()
        .asyncMap((snapshot) async {
      if (snapshot.docs.isEmpty) return [];

      final List<SentenceModel> sentences = [];

      // الحصول على جميع الجمل المفضلة
      for (var doc in snapshot.docs) {
        try {
          // الحصول على الجملة من المجموعة الرئيسية باستخدام المعرف
          final sentenceDoc = await _firestore
              .collection(_sentencesCollection)
              .doc(doc.id)
              .get();

          if (sentenceDoc.exists) {
            // إنشاء نموذج الجملة وتحديده كمفضل
            final sentence = SentenceModel.fromMap(sentenceDoc.data()!, doc.id,
                isFavoriteByCurrentUser: true);
            sentences.add(sentence);
          } else {
            debugPrint(
                'Favorite sentence ${doc.id} does not exist in the main collection');
          }
        } catch (e) {
          debugPrint('Error getting favorite sentence ${doc.id}: $e');
        }
      }

      return sentences;
    });
  }

  Stream<List<SentenceModel>> getRandomSentences(String? userId) async* {
    if (userId == null) {
      // إذا لم يكن هناك مستخدم، فقط إرجاع الجمل بدون تحديدها كمقروءة أو مفضلة
      final query = _firestore.collection(_sentencesCollection).limit(10);
      await for (final snapshot in query.snapshots()) {
        yield snapshot.docs
            .map((doc) => SentenceModel.fromMap(doc.data(), doc.id))
            .toList();
      }
    } else {
      // إذا كان هناك مستخدم، الحصول على الجمل المقروءة والمفضلة لتحديدها بشكل صحيح
      final readSentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .get();
      final readSentenceIds =
          readSentencesSnapshot.docs.map((doc) => doc.id).toSet();

      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      final favoriteSentenceIds =
          favoritesSnapshot.docs.map((doc) => doc.id).toSet();

      // الحصول على جمل عشوائية
      final query = _firestore.collection(_sentencesCollection).limit(10);
      await for (final snapshot in query.snapshots()) {
        yield snapshot.docs.map((doc) {
          final isRead = readSentenceIds.contains(doc.id);
          final isFavorite = favoriteSentenceIds.contains(doc.id);
          return SentenceModel.fromMap(
            doc.data(),
            doc.id,
            isReadByCurrentUser: isRead,
            isFavoriteByCurrentUser: isFavorite,
          );
        }).toList();
      }
    }
  }

  Future<List<SentenceModel>> getUnreadDailySentences(
    String userId,
    int limit, {
    bool forceRefresh = false,
  }) async {
    debugPrint('الحصول على الجمل اليومية غير المقروءة للمستخدم $userId');
    debugPrint('تحديث إجباري: $forceRefresh، الحد: $limit');

    try {
      // أولاً، نحاول الحصول على الجمل من التخزين المحلي
      final localSentences =
          await _localStorageService.getLocalDailySentences();

      // طباعة معلومات تشخيصية
      final unreadLocalCount =
          localSentences.where((s) => !s.isReadByCurrentUser).length;
      debugPrint(
          'تم العثور على ${localSentences.length} جملة محلية، منها $unreadLocalCount غير مقروءة');

      // إذا كان هناك تحديث إجباري وكانت هناك جمل محلية، نعيد استخدامها مع إعادة تعيين حالة القراءة
      if (forceRefresh && localSentences.isNotEmpty) {
        debugPrint('تم طلب تحديث إجباري مع وجود جمل محلية');

        // إعادة تعيين حالة القراءة لجميع الجمل المحلية
        for (var sentence in localSentences) {
          sentence.isReadByCurrentUser = false;
        }

        // تحديث التخزين المحلي بالجمل المعاد تعيينها
        await _localStorageService.storeLocalDailySentences(localSentences);

        debugPrint(
            'تم إعادة تعيين حالة القراءة لـ ${localSentences.length} جملة محلية');

        // المزامنة مع Firestore في الخلفية فقط إذا كان هناك اتصال بالإنترنت
        // استخدام Future.delayed لتأخير المزامنة وتجنب التأثير على أداء واجهة المستخدم
        Future.delayed(const Duration(seconds: 3), () async {
          try {
            final isConnected = await isOnline();
            if (isConnected) {
              // مزامنة الجمل مع Firestore
              await _syncLocalSentencesToFirestore(userId, localSentences);
              debugPrint('تمت مزامنة الجمل المحلية مع Firestore في الخلفية');
            }
          } catch (e) {
            debugPrint('خطأ في مزامنة الجمل المحلية مع Firestore: $e');
          }
        });

        return localSentences;
      }

      // إذا لم يكن تحديثًا إجباريًا وهناك جمل محلية، نستخدمها
      if (!forceRefresh && localSentences.isNotEmpty) {
        debugPrint('استخدام الجمل اليومية المحلية: ${localSentences.length}');

        // تصفية الجمل غير المقروءة
        final unreadLocalSentences =
            localSentences.where((s) => !s.isReadByCurrentUser).toList();

        if (unreadLocalSentences.isNotEmpty) {
          debugPrint(
              'إرجاع ${unreadLocalSentences.length} جملة محلية غير مقروءة');
          return unreadLocalSentences;
        }

        // إذا كانت جميع الجمل المحلية مقروءة ولم يكن تحديثًا إجباريًا، نرجع قائمة فارغة
        // لأن المستخدم يجب أن يضغط على زر "10 again" للحصول على جمل جديدة
        if (!forceRefresh) {
          debugPrint(
              'تمت قراءة جميع الجمل المحلية، ولكن لا يوجد تحديث إجباري. إرجاع قائمة فارغة.');
          return [];
        }

        debugPrint('تمت قراءة جميع الجمل المحلية، جاري الحصول على جمل جديدة');
      }

      // إذا لم تكن هناك جمل محلية أو كان تحديثًا إجباريًا، نحاول الحصول عليها من Firestore
      // تحقق من الاتصال بالإنترنت بطريقة محسنة
      final isConnected = await isOnline();

      if (!isConnected) {
        debugPrint('لا يوجد اتصال بالإنترنت، لا يمكن جلب جمل جديدة');

        // إذا لم يكن هناك اتصال بالإنترنت ولكن هناك جمل محلية، نعيد استخدامها مع إعادة تعيين حالة القراءة
        if (localSentences.isNotEmpty) {
          // إعادة تعيين حالة القراءة لجميع الجمل المحلية
          for (var sentence in localSentences) {
            sentence.isReadByCurrentUser = false;
          }

          // تحديث التخزين المحلي بالجمل المعاد تعيينها
          await _localStorageService.storeLocalDailySentences(localSentences);

          debugPrint(
              'تم إعادة تعيين حالة القراءة لـ ${localSentences.length} جملة محلية في وضع عدم الاتصال');
          return localSentences;
        }

        // إذا لم تكن هناك جمل محلية، نرجع قائمة فارغة
        return [];
      }

      // الحصول على الجمل من Firestore
      final userDailySentencesRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('dailySentences');

      final existingDocs = await userDailySentencesRef.get();
      debugPrint(
          'Existing daily sentences in Firestore: ${existingDocs.docs.length}');

      // إذا لم يكن تحديثًا إجباريًا وهناك جمل موجودة في Firestore، إرجاع تلك الجمل
      if (!forceRefresh && existingDocs.docs.isNotEmpty) {
        debugPrint('Using existing daily sentences from Firestore');
        final sentences = <SentenceModel>[];

        for (var doc in existingDocs.docs) {
          // التحقق مما إذا كانت الجملة قد تمت قراءتها بالفعل
          final sentenceDoc = await _firestore
              .collection(_sentencesCollection)
              .doc(doc.id)
              .get();

          if (sentenceDoc.exists) {
            final sentenceMap = sentenceDoc.data()!;
            final readBy = sentenceMap['readBy'] as Map<String, dynamic>?;

            // إذا لم تتم قراءة الجملة، إضافتها إلى القائمة
            if (readBy == null || !readBy.containsKey(userId)) {
              sentences.add(SentenceModel.fromMap(sentenceMap, doc.id));
            }
          }
        }

        // إذا كانت هناك جمل غير مقروءة، نخزنها محليًا ثم نرجعها
        if (sentences.isNotEmpty) {
          debugPrint('Storing ${sentences.length} sentences locally');
          await _localStorageService.storeLocalDailySentences(sentences);
          debugPrint('Returning ${sentences.length} existing unread sentences');
          return sentences;
        }

        // إذا تمت قراءة جميع الجمل بالفعل، المتابعة مع التحديث الإجباري
        debugPrint('All existing sentences have been read, getting new ones');
      }

      // إذا كان تحديثًا إجباريًا أو لم تكن هناك جمل موجودة غير مقروءة، حذف الموجودة
      if (existingDocs.docs.isNotEmpty) {
        debugPrint('Deleting all existing daily sentences from Firestore');
        final batch = _firestore.batch();
        for (var doc in existingDocs.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        debugPrint('Daily sentences deleted from Firestore');
      }

      // الحصول على جميع الجمل المتاحة
      final allSentencesSnapshot =
          await _firestore.collection(_sentencesCollection).get();
      debugPrint(
          'Total available sentences: ${allSentencesSnapshot.docs.length}');

      // الحصول على الجمل المقروءة من قبل المستخدم
      final readSentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .get();

      // إنشاء مجموعة من معرفات الجمل المقروءة للبحث السريع
      final readSentenceIds =
          readSentencesSnapshot.docs.map((doc) => doc.id).toSet();

      // إضافة الجمل المقروءة محليًا
      final localReadSentenceIds =
          await _localStorageService.getReadSentenceIds();
      readSentenceIds.addAll(localReadSentenceIds);

      // الحصول على الجمل المفضلة للمستخدم
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      final favoriteSentenceIds =
          favoritesSnapshot.docs.map((doc) => doc.id).toSet();

      // تصفية الجمل التي ليست في مجموعة الجمل المقروءة
      final unreadSentences = allSentencesSnapshot.docs
          .where((doc) => !readSentenceIds.contains(doc.id))
          .map((doc) {
        final isFavorite = favoriteSentenceIds.contains(doc.id);
        return SentenceModel.fromMap(
          doc.data(),
          doc.id,
          isFavoriteByCurrentUser: isFavorite,
        );
      }).toList();

      debugPrint('Unread sentences: ${unreadSentences.length}');

      // إذا لم تكن هناك جمل غير مقروءة، إرجاع قائمة فارغة
      if (unreadSentences.isEmpty) {
        debugPrint('No unread sentences available');
        // مسح الجمل المحلية
        await _localStorageService.clearAllLocalData();
        return [];
      }

      // خلط الجمل بشكل عشوائي
      unreadSentences.shuffle();

      // تحديد بالكمية المطلوبة
      final selectedSentences = unreadSentences.take(limit).toList();
      debugPrint('Selected sentences: ${selectedSentences.length}');

      // حفظ الجمل محليًا فقط، بدون إنشاء مجموعة dailySentences في Firestore
      await _localStorageService.storeLocalDailySentences(selectedSentences);
      debugPrint(
          'Sentences stored locally only, not creating dailySentences collection in Firestore');

      // تحديث عداد الجمل المعروضة اليوم
      final today = DateTime.now();

      // الحصول على الإحصائيات الحالية
      final statsDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .get();

      int currentShownCount = 0;

      // التحقق مما إذا كانت الإحصائيات موجودة لليوم الحالي
      if (statsDoc.exists) {
        final statsData = statsDoc.data();
        final statsDate = statsData?['date'] as Timestamp?;
        final shownCount = statsData?['shownCount'] as num?;

        // التحقق من أن التاريخ هو اليوم
        if (statsDate != null) {
          final statsDateDay = statsDate.toDate();
          final isToday = statsDateDay.year == today.year &&
              statsDateDay.month == today.month &&
              statsDateDay.day == today.day;

          if (isToday && shownCount != null) {
            currentShownCount = shownCount.toInt();
          }
        }
      }

      // تحديث العداد بإضافة عدد الجمل الجديدة
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .set({
        'date':
            Timestamp.fromDate(DateTime(today.year, today.month, today.day)),
        'shownCount': currentShownCount + selectedSentences.length,
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // تحديث العداد المحلي (10 جمل فقط)
      await _localStorageService.incrementTodayShownCount(10);

      return selectedSentences;
    } catch (e) {
      debugPrint('Error getting daily sentences: $e');

      // في حالة الخطأ، نحاول استخدام الجمل المحلية
      final localSentences =
          await _localStorageService.getLocalDailySentences();
      final unreadLocalSentences =
          localSentences.where((s) => !s.isReadByCurrentUser).toList();

      if (unreadLocalSentences.isNotEmpty) {
        debugPrint(
            'Returning ${unreadLocalSentences.length} local unread sentences after error');
        return unreadLocalSentences;
      }

      return [];
    }
  }

  Future<void> toggleFavorite(String sentenceId, String userId) async {
    try {
      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(sentenceId);

      final doc = await docRef.get();

      // التحقق مما إذا كانت الجملة موجودة
      final sentenceDoc = await _firestore
          .collection(_sentencesCollection)
          .doc(sentenceId)
          .get();

      if (!sentenceDoc.exists) {
        debugPrint('Error: Sentence with ID $sentenceId does not exist');
        throw Exception('الجملة غير موجودة في قاعدة البيانات');
      }

      if (doc.exists) {
        // إزالة من المفضلة
        await docRef.delete();
        debugPrint('Sentence $sentenceId removed from favorites');
      } else {
        // إضافة إلى المفضلة (فقط مع الطابع الزمني، بدون بيانات الجملة)
        await docRef.set({
          'timestamp': FieldValue.serverTimestamp(),
        });
        debugPrint('Sentence $sentenceId added to favorites');
      }

      // لم نعد نقوم بتحديث حقل isFavorite في مجموعة sentences
    } catch (e) {
      // تسجيل الخطأ وإعادة رميه للتعامل معه في الطبقة العليا
      debugPrint('Error in toggleFavorite: $e');
      throw Exception(
          'فشل في تحديث حالة المفضلة. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.');
    }
  }

  Future<void> markSentenceAsRead(String userId, String sentenceId) async {
    try {
      // تحديث حالة القراءة محليًا أولاً
      await _localStorageService.markSentenceAsReadLocally(sentenceId);
      debugPrint('تم تعليم الجملة $sentenceId كمقروءة محليًا');

      // تأخير المزامنة مع Firestore لتجنب التأثير على أداء واجهة المستخدم
      // استخدام Future.delayed بدلاً من التنفيذ المباشر
      Future.delayed(const Duration(milliseconds: 500), () async {
        try {
          // التحقق من الاتصال بالإنترنت بطريقة محسنة
          final isConnected = await isOnline();

          if (!isConnected) {
            debugPrint(
                'لا يوجد اتصال بالإنترنت، تم تعليم الجملة كمقروءة محليًا فقط');
            return;
          }

          final timestamp = FieldValue.serverTimestamp();

          // التحقق مما إذا كانت الجملة موجودة في Firestore
          // استخدام طريقة أكثر كفاءة للتحقق من وجود الجملة
          try {
            // إضافة إلى مجموعة readSentences للمستخدم (فقط مع readAt، بدون بيانات الجملة)
            await _firestore
                .collection('users')
                .doc(userId)
                .collection('readSentences')
                .doc(sentenceId)
                .set({
              'readAt': timestamp,
            });

            debugPrint('تم تعليم الجملة $sentenceId كمقروءة في Firestore');

            // تحديث سجل القراءة في الخلفية
            _updateReadingLog(userId);
          } catch (e) {
            debugPrint(
                'تحذير: فشل في تعليم الجملة $sentenceId كمقروءة في Firestore: $e');
          }
        } catch (e) {
          // تسجيل الخطأ ولكن لا نعيد رميه لأننا قمنا بالفعل بتحديث الحالة محليًا
          debugPrint('خطأ في تعليم الجملة كمقروءة: $e');
        }
      });
    } catch (e) {
      // تسجيل الخطأ ولكن لا نعيد رميه لأننا قمنا بالفعل بتحديث الحالة محليًا
      debugPrint('خطأ في تعليم الجملة كمقروءة: $e');
    }
  }

  Future<void> _updateReadingLog(String userId) async {
    try {
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month}-${today.day}';

      await _firestore
          .collection('users')
          .doc(userId)
          .collection('readingLog')
          .doc(dateKey)
          .set({
        'date':
            Timestamp.fromDate(DateTime(today.year, today.month, today.day)),
        'count': FieldValue.increment(1),
      }, SetOptions(merge: true));

      // تحديث عداد الجمل المعروضة اليوم
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .set({
        'date':
            Timestamp.fromDate(DateTime(today.year, today.month, today.day)),
        'shownCount': FieldValue.increment(
            0), // لا نزيد العدد هنا، فقط نتأكد من وجود الوثيقة
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // تحديث إجمالي عدد الجمل المقروءة
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('total')
          .set({
        'readCount': FieldValue.increment(1),
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      debugPrint('Updated total read count for user $userId');
    } catch (e) {
      // تسجيل الخطأ ولكن لا نعيد رميه لأن هذه وظيفة ثانوية
      // وفشلها لا يجب أن يؤثر على تجربة المستخدم الأساسية
      debugPrint('Error updating reading log: $e');
    }
  }

  // إضافة دالة جديدة للحصول على Stream للإحصائيات
  Stream<Map<String, dynamic>> getUserReadingStatsStream(String userId) {
    // إنشاء Stream للإحصائيات
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('stats')
        .doc('total')
        .snapshots()
        .asyncMap((totalSnapshot) async {
      // الحصول على الإحصائيات الأساسية
      final stats = await getUserReadingStats(userId);

      // تحديث عدد الجمل المقروءة من Stream إذا كان متاحًا
      if (totalSnapshot.exists) {
        final totalData = totalSnapshot.data();
        if (totalData != null && totalData.containsKey('readCount')) {
          stats['readCount'] = (totalData['readCount'] as num).toInt();
        }
      }

      return stats;
    });
  }

  // دالة لمزامنة الجمل المحلية مع Firestore
  Future<void> _syncLocalSentencesToFirestore(
      String userId, List<SentenceModel> sentences) async {
    try {
      // الحصول على مرجع مجموعة dailySentences للمستخدم
      final userDailySentencesRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('dailySentences');

      // حذف جميع الجمل الموجودة في مجموعة dailySentences
      final existingDocs = await userDailySentencesRef.get();
      if (existingDocs.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (var doc in existingDocs.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        debugPrint('Deleted existing daily sentences from Firestore');
      }

      // إضافة الجمل المحلية إلى مجموعة dailySentences
      final batch = _firestore.batch();
      for (var sentence in sentences) {
        final docRef = userDailySentencesRef.doc(sentence.id);
        batch.set(docRef, {
          'addedAt': FieldValue.serverTimestamp(),
        });
      }
      await batch.commit();
      debugPrint(
          'Added ${sentences.length} sentences to Firestore dailySentences');

      // تحديث عداد الجمل المعروضة اليوم
      final today = DateTime.now();
      final statsDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .get();

      int currentShownCount = 0;
      if (statsDoc.exists) {
        final statsData = statsDoc.data();
        final statsDate = statsData?['date'] as Timestamp?;
        final shownCount = statsData?['shownCount'] as num?;

        if (statsDate != null) {
          final statsDateDay = statsDate.toDate();
          final isToday = statsDateDay.year == today.year &&
              statsDateDay.month == today.month &&
              statsDateDay.day == today.day;

          if (isToday && shownCount != null) {
            currentShownCount = shownCount.toInt();
          }
        }
      }

      // تحديث العداد بإضافة عدد الجمل
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .set({
        'date':
            Timestamp.fromDate(DateTime(today.year, today.month, today.day)),
        'shownCount': currentShownCount + sentences.length,
        'lastUpdated': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      debugPrint('Updated daily stats in Firestore');
    } catch (e) {
      debugPrint('Error in _syncLocalSentencesToFirestore: $e');
      // لا نرمي استثناء هنا لأن هذه وظيفة خلفية
    }
  }

  // دالة الإحصائيات الأصلية
  Future<Map<String, dynamic>> getUserReadingStats(String userId) async {
    try {
      // الحصول على جميع الجمل
      final allSentencesSnapshot =
          await _firestore.collection(_sentencesCollection).get();
      final totalSentences = allSentencesSnapshot.size;

      // الحصول على إجمالي عدد الجمل المقروءة من وثيقة 'total'
      int readCount = 0;

      // استخدام get() بدلاً من snapshots() لأننا نريد قيمة واحدة فقط
      final totalStatsDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('total')
          .get();

      if (totalStatsDoc.exists) {
        final data = totalStatsDoc.data();
        if (data != null && data.containsKey('readCount')) {
          readCount = (data['readCount'] as num).toInt();
        }
      }

      // إذا لم تكن هناك إحصائيات محفوظة، نستخدم العدد من مجموعة readSentences
      if (readCount == 0) {
        final readSentencesSnapshot = await _firestore
            .collection('users')
            .doc(userId)
            .collection('readSentences')
            .get();
        readCount = readSentencesSnapshot.size;

        // تحديث وثيقة 'total' بالعدد الصحيح
        await _firestore
            .collection('users')
            .doc(userId)
            .collection('stats')
            .doc('total')
            .set({
          'readCount': readCount,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }

      // الحصول على الجمل المفضلة من مجموعة favorites
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      final favoriteCount = favoritesSnapshot.size;

      // حساب نسبة التقدم
      final progressPercentage = totalSentences > 0
          ? ((readCount / totalSentences) * 100).toStringAsFixed(1)
          : '0';

      // الحصول على الجمل المقروءة اليوم
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      // الحصول على الجمل المقروءة من قبل المستخدم من مجموعة readSentences
      final readSentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .get();

      int todayReadCount = readSentencesSnapshot.docs.where((doc) {
        final readAt = doc.data()['readAt'] as Timestamp?;
        if (readAt == null) return false;
        final readDate = readAt.toDate();
        return readDate.isAfter(todayStart);
      }).length;

      // الحصول على عدد الجمل التي ظهرت اليوم
      // 1. الحصول على الجمل الحالية في dailySentences
      final dailySentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('dailySentences')
          .get();
      int currentDailySentencesCount = dailySentencesSnapshot.docs.length;

      // 2. الحصول على عدد الجمل المعروضة من مجموعة stats
      int todayShownCount = 0;
      final statsDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('daily')
          .get();

      if (statsDoc.exists) {
        final statsData = statsDoc.data();
        final statsDate = statsData?['date'] as Timestamp?;
        final shownCount = statsData?['shownCount'] as num?;

        // التحقق من أن التاريخ هو اليوم
        if (statsDate != null) {
          final statsDateDay = statsDate.toDate();
          final isToday = statsDateDay.year == today.year &&
              statsDateDay.month == today.month &&
              statsDateDay.day == today.day;

          if (isToday && shownCount != null) {
            todayShownCount = shownCount.toInt();
          }
        }
      }

      // 3. إذا لم يكن هناك عدد محفوظ، استخدم عدد الجمل المقروءة + الجمل الحالية
      if (todayShownCount == 0) {
        todayShownCount = todayReadCount;

        // إضافة الجمل الحالية غير المقروءة
        if (currentDailySentencesCount > 0) {
          todayShownCount += currentDailySentencesCount;
        }
      }

      // 4. الحصول على سجل القراءة اليومي
      final dateKey = '${today.year}-${today.month}-${today.day}';
      final readingLogDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readingLog')
          .doc(dateKey)
          .get();

      // 5. إذا كان هناك سجل قراءة لليوم، استخدم القيمة المخزنة
      if (readingLogDoc.exists) {
        final count = readingLogDoc.data()?['count'] as num?;
        if (count != null) {
          todayReadCount = count.toInt();
        }
      }

      return {
        'readCount': readCount,
        'favoriteCount': favoriteCount,
        'totalSentences': totalSentences,
        'progressPercentage': progressPercentage,
        'todayReadCount': todayReadCount,
        'todayShownCount': todayShownCount,
      };
    } catch (e) {
      debugPrint('Error getting reading statistics: $e');
      return {
        'readCount': 0,
        'favoriteCount': 0,
        'totalSentences': 0,
        'progressPercentage': '0',
        'todayReadCount': 0,
        'todayShownCount': 10, // قيمة افتراضية
      };
    }
  }

  Future<Map<String, int>> getWeeklyReadingLog(String userId) async {
    try {
      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final weekEnd = weekStart
          .add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));

      debugPrint(
          'Getting weekly reading log for user $userId from ${weekStart.toString()} to ${weekEnd.toString()}');

      final snapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readingLog')
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(weekStart))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(weekEnd))
          .get();

      debugPrint('Found ${snapshot.docs.length} reading log entries');

      final result = <String, int>{};

      // تهيئة القيم لجميع أيام الأسبوع
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        final dateKey = '${date.year}-${date.month}-${date.day}';
        result[dateKey] = 0;
      }

      // إضافة القيم الفعلية من قاعدة البيانات
      for (var doc in snapshot.docs) {
        final count = (doc.data()['count'] as num).toInt();
        final date = (doc.data()['date'] as Timestamp).toDate();
        final dateKey = '${date.year}-${date.month}-${date.day}';
        result[dateKey] = count;
        debugPrint('Reading log for $dateKey: $count');
      }

      return result;
    } catch (e) {
      debugPrint('Error getting weekly reading log: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> getLocalStats() async {
    final cacheSize = await _cacheService.getCacheSize();

    return {
      'totalSentences': 0,
      'cacheSize': _cacheService.formatFileSize(cacheSize),
    };
  }

  // مزامنة الجمل المعلقة مع قاعدة البيانات
  Future<void> syncPendingSentences(String userId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final isConnected = await isOnline();
      if (!isConnected) {
        debugPrint('No internet connection, cannot sync pending sentences');
        return;
      }

      // الحصول على قائمة الجمل المعلقة للمزامنة
      final pendingSentences =
          await _localStorageService.getPendingSyncSentences();

      if (pendingSentences.isEmpty) {
        debugPrint('No pending sentences to sync');
        return;
      }

      debugPrint('Syncing ${pendingSentences.length} pending sentences');

      // مزامنة كل جملة مع قاعدة البيانات
      for (final sentenceId in pendingSentences) {
        try {
          final timestamp = FieldValue.serverTimestamp();

          // التحقق مما إذا كانت الجملة موجودة
          final sentenceDoc = await _firestore
              .collection(_sentencesCollection)
              .doc(sentenceId)
              .get();

          if (sentenceDoc.exists) {
            // إضافة إلى مجموعة readSentences للمستخدم
            await _firestore
                .collection('users')
                .doc(userId)
                .collection('readSentences')
                .doc(sentenceId)
                .set({
              'readAt': timestamp,
            });

            debugPrint('Synced sentence $sentenceId to Firestore');

            // إزالة من dailySentences إذا كانت موجودة
            await _firestore
                .collection('users')
                .doc(userId)
                .collection('dailySentences')
                .doc(sentenceId)
                .delete();

            // تحديث سجل القراءة
            await _updateReadingLog(userId);

            // إزالة الجملة من قائمة الجمل المعلقة
            await _localStorageService
                .removeFromPendingSyncSentences(sentenceId);
          } else {
            debugPrint(
                'Warning: Sentence $sentenceId does not exist in Firestore');
            // إزالة الجملة من قائمة الجمل المعلقة لأنها غير موجودة في قاعدة البيانات
            await _localStorageService
                .removeFromPendingSyncSentences(sentenceId);
          }
        } catch (e) {
          debugPrint('Error syncing sentence $sentenceId: $e');
          // نستمر في المزامنة حتى لو فشلت جملة واحدة
        }
      }

      debugPrint('Finished syncing pending sentences');
    } catch (e) {
      debugPrint('Error in syncPendingSentences: $e');
    }
  }

  Future<void> clearLocalData(String selectedLanguage) async {
    await _cacheService.clearCache();
  }

  // الحصول على قائمة الجمل المعلقة للمزامنة
  Future<List<String>> getPendingSyncSentences() async {
    return await _localStorageService.getPendingSyncSentences();
  }

  // الحصول على قائمة معرفات الجمل المقروءة محليًا
  Future<List<String>> getLocalReadSentenceIds() async {
    return await _localStorageService.getReadSentenceIds();
  }

  // الحصول على جميع الجمل
  Future<List<SentenceModel>> getAllSentences() async {
    try {
      final snapshot = await _firestore.collection(_sentencesCollection).get();
      return snapshot.docs
          .map((doc) => SentenceModel.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      debugPrint('Error getting all sentences: $e');
      return [];
    }
  }

  // هذه الطريقة لم تعد تستخدم، ولكننا نحتفظ بها في حالة الحاجة إليها في المستقبل
  // Future<SentenceModel?> _getSentenceById(String id) async {
  //   try {
  //     final doc =
  //         await _firestore.collection(_sentencesCollection).doc(id).get();
  //     if (!doc.exists) return null;
  //     return SentenceModel.fromMap(doc.data()!, doc.id);
  //   } catch (e) {
  //     debugPrint('Error getting sentence by ID: $e');
  //     return null;
  //   }
  // }

  Map<String, dynamic> exportLocalData(
      String userId, Map<String, dynamic> data) {
    return data;
  }
}
