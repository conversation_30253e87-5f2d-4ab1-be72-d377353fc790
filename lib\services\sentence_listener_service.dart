import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'native_notification_service.dart';

/// Service to listen for new sentences added to Firestore
class SentenceListenerService {
  // Singleton pattern
  static final SentenceListenerService _instance = SentenceListenerService._internal();
  factory SentenceListenerService() => _instance;
  SentenceListenerService._internal();

  // Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Stream subscription
  StreamSubscription<QuerySnapshot>? _sentencesSubscription;
  
  // Last timestamp we've seen
  DateTime? _lastTimestamp;
  
  // Initialize the service
  void initialize() {
    // Get the current timestamp
    _lastTimestamp = DateTime.now();
    
    // Start listening for changes
    _startListening();
    
    debugPrint('SentenceListenerService initialized');
  }
  
  // Start listening for new sentences
  void _startListening() {
    // Cancel any existing subscription
    _sentencesSubscription?.cancel();
    
    // Listen for new sentences
    _sentencesSubscription = _firestore
        .collection('sentences')
        .orderBy('createdAt', descending: true)
        .limit(10) // Only need to check the most recent ones
        .snapshots()
        .listen(_handleSentencesSnapshot);
        
    debugPrint('Started listening for new sentences');
  }
  
  // Handle a new snapshot from Firestore
  void _handleSentencesSnapshot(QuerySnapshot snapshot) {
    // Skip the initial snapshot
    if (_lastTimestamp == null) {
      return;
    }
    
    // Check for new sentences
    bool hasNewSentences = false;
    
    for (var change in snapshot.docChanges) {
      // Only care about added documents
      if (change.type == DocumentChangeType.added) {
        final data = change.doc.data() as Map<String, dynamic>?;
        
        if (data != null && data.containsKey('createdAt')) {
          final createdAt = (data['createdAt'] as Timestamp).toDate();
          
          // If this sentence was created after our last check
          if (createdAt.isAfter(_lastTimestamp!)) {
            hasNewSentences = true;
            debugPrint('New sentence detected: ${change.doc.id}');
          }
        }
      }
    }
    
    // If we found new sentences, send a notification
    if (hasNewSentences) {
      _sendNewSentencesNotification();
    }
    
    // Update the last timestamp
    _lastTimestamp = DateTime.now();
  }
  
  // Send a notification for new sentences
  Future<void> _sendNewSentencesNotification() async {
    try {
      final notificationService = NativeNotificationService();
      await notificationService.showNewSentencesAddedNotification();
    } catch (e) {
      debugPrint('Error sending new sentences notification: $e');
    }
  }
  
  // Dispose the service
  void dispose() {
    _sentencesSubscription?.cancel();
    _sentencesSubscription = null;
    debugPrint('SentenceListenerService disposed');
  }
}
