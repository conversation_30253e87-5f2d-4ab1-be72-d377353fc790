import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'hive_sentence_service.dart';
import 'sync_service.dart';

/// مدير المزامنة المتقدمة
class SyncManager {
  final HiveSentenceService _hiveSentenceService;
  final SyncService _syncService;
  final Connectivity _connectivity = Connectivity();

  // حالة المزامنة
  bool _isSyncing = false;
  DateTime? _lastSyncAttempt;
  bool _autoSyncEnabled = true;
  int _syncIntervalDays = 3; // الفترة الافتراضية للمزامنة التلقائية (3 أيام)

  // مراقب الاتصال
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // مؤقت المزامنة الدورية
  Timer? _periodicSyncTimer;

  // الحصول على حالة المزامنة
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncAttempt => _lastSyncAttempt;
  bool get autoSyncEnabled => _autoSyncEnabled;
  int get syncIntervalDays => _syncIntervalDays;

  // مستمعو التغييرات
  final List<Function()> _syncListeners = [];

  SyncManager(this._hiveSentenceService, this._syncService) {
    // بدء مراقبة الاتصال
    _startConnectivityMonitoring();

    // بدء المزامنة الدورية
    _startPeriodicSync();
  }

  /// بدء مراقبة الاتصال
  void _startConnectivityMonitoring() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      // لا نقوم بالمزامنة التلقائية عند استعادة الاتصال
      // فقط نسجل حالة الاتصال في سجل التصحيح
      if (result != ConnectivityResult.none) {
        debugPrint('تم استعادة الاتصال، لكن لن تتم المزامنة التلقائية');
        // تحديث حالة الاتصال فقط دون مزامنة
        _notifyListeners();
      }
    });
  }

  /// بدء المزامنة الدورية
  void _startPeriodicSync() {
    // مزامنة كل 24 ساعة للتحقق من الفترة
    _periodicSyncTimer = Timer.periodic(const Duration(hours: 24), (timer) {
      if (_autoSyncEnabled &&
          _hiveSentenceService.isTimeForAutoSync(_syncIntervalDays)) {
        debugPrint(
            'بدء المزامنة الدورية المجدولة (كل $_syncIntervalDays أيام)');
        // لا نقوم بالمزامنة تلقائيًا، فقط نخطر المستمعين بأن الوقت قد حان للمزامنة
        // سيتم عرض إشعار للمستخدم في واجهة المستخدم
        _notifyListeners();
      }
    });
  }

  /// تعيين فترة المزامنة التلقائية
  void setSyncInterval(int days) {
    _syncIntervalDays = days;
    debugPrint('تم تعيين فترة المزامنة التلقائية إلى $days يوم');
  }

  /// تمكين/تعطيل المزامنة التلقائية
  void setAutoSyncEnabled(bool enabled) {
    _autoSyncEnabled = enabled;
    debugPrint('تم ${enabled ? 'تمكين' : 'تعطيل'} المزامنة التلقائية');
  }

  /// مزامنة البيانات مع Firebase
  Future<bool> syncWithFirebase(String? userId) async {
    // إعادة تعيين حالة المزامنة حتى لو كانت قيد التنفيذ بالفعل
    _isSyncing = true;
    _lastSyncAttempt = DateTime.now();
    _notifyListeners();

    debugPrint('بدء المزامنة مع Firebase');

    try {
      bool result = false;

      if (userId != null) {
        // الحصول على عدد العناصر المنتظرة للمزامنة قبل البدء
        final pendingCount = getPendingSyncItemsCount();
        debugPrint('عدد العناصر المنتظرة للمزامنة قبل البدء: $pendingCount');

        // إعادة تعيين حالة المزامنة لجميع السجلات
        await _hiveSentenceService.resetSyncLogsStatus();
        debugPrint('تم إعادة تعيين حالة المزامنة لجميع السجلات قبل المزامنة');

        // إجراء المزامنة
        result = await _syncService.syncWithFirebase(userId);

        // الحصول على عدد العناصر المنتظرة للمزامنة بعد الانتهاء
        final remainingCount = getPendingSyncItemsCount();
        debugPrint(
            'عدد العناصر المنتظرة للمزامنة بعد الانتهاء: $remainingCount');
      } else {
        // إذا لم يتم تحديد معرف المستخدم، فقط تحديث وقت آخر مزامنة
        await _hiveSentenceService.updateLastSyncTime();
        result = true;
      }

      _isSyncing = false;
      _notifyListeners();

      return result;
    } catch (e) {
      debugPrint('خطأ في المزامنة: $e');
      _isSyncing = false;
      _notifyListeners();
      return false;
    }
  }

  /// استعادة البيانات من Firebase بعد إعادة التثبيت
  Future<bool> restoreDataFromFirebase(String userId) async {
    if (_isSyncing) {
      debugPrint('المزامنة قيد التنفيذ بالفعل، تم تخطي طلب الاستعادة');
      return false;
    }

    _isSyncing = true;
    _notifyListeners();

    try {
      // التحقق من الاتصال بالإنترنت
      final isOnline = await _syncService.isOnline();
      if (!isOnline) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي استعادة البيانات');
        _isSyncing = false;
        _notifyListeners();
        return false;
      }

      // استعادة البيانات
      await _syncService.restoreDataFromFirebase(userId);

      _isSyncing = false;
      _notifyListeners();

      return true;
    } catch (e) {
      debugPrint('خطأ في استعادة البيانات: $e');
      _isSyncing = false;
      _notifyListeners();
      return false;
    }
  }

  /// الحصول على عدد العناصر المنتظرة للمزامنة
  int getPendingSyncItemsCount() {
    final unsyncedLogs = _hiveSentenceService.getUnsyncedLogs();
    debugPrint('عدد العناصر المنتظرة للمزامنة: ${unsyncedLogs.length}');
    return unsyncedLogs.length;
  }

  /// إضافة مستمع للتغييرات
  void addListener(Function() listener) {
    _syncListeners.add(listener);
  }

  /// إزالة مستمع للتغييرات
  void removeListener(Function() listener) {
    _syncListeners.remove(listener);
  }

  /// إخطار المستمعين بالتغييرات
  void _notifyListeners() {
    for (final listener in _syncListeners) {
      listener();
    }
  }

  /// التخلص من الموارد
  void dispose() {
    _connectivitySubscription?.cancel();
    _periodicSyncTimer?.cancel();
    _syncListeners.clear();
  }
}
