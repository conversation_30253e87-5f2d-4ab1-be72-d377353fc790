name: test05
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.2
  cloud_firestore: ^5.6.6
  firebase_storage: ^12.4.5
  provider: ^6.1.1
  shared_preferences: ^2.5.3
  image_picker: ^1.0.5
  path_provider: ^2.1.1
  path: ^1.8.3
  crypto: ^3.0.3
  connectivity_plus: ^5.0.2
  intl: ^0.20.2
  shimmer: ^3.0.0
  encrypt: ^5.0.3
  share_plus: ^10.1.4
  fl_chart: ^0.71.0
  persistent_bottom_nav_bar: ^6.2.1
  timezone: ^0.9.2
  rxdart: ^0.27.7
  flutter_native_splash: 2.4.6
  # Hive packages
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  uuid: ^4.3.3
  # Paquetes para el manejo de audio y texto
  flutter_tts: ^4.2.2
  avatar_glow: ^2.0.2
  percent_indicator: ^4.2.3
  # Paquetes para grabación de audio y reconocimiento de voz
  flutter_sound: ^9.28.0
  permission_handler: ^12.0.0+1
  speech_to_text: ^7.0.0
  string_similarity: ^2.0.0
  just_audio: ^0.9.36
  timeline_tile: ^2.0.0
  google_mobile_ads: ^6.0.0
  table_calendar: ^3.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1
  # Hive code generator
  hive_generator: ^2.0.1
  build_runner: ^2.4.8

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/avatars/
    - assets/avatars/avataaars_1.png
    - assets/avatars/avataaars_2.png
    - assets/avatars/avataaars_3.png
    - assets/avatars/avataaars_4.png
    - assets/avatars/avataaars_5.png
    - assets/avatars/avataaars_6.png
    - assets/avatars/avataaars_7.png
    - assets/avatars/avataaars_8.png
    - assets/avatars/avataaars_9.png
    - assets/avatars/avataaars_10.png
    - assets/avatars/avataaars_11.png
    - assets/avatars/avataaars_12.png
    - assets/avatars/avataaars_13.png
    - assets/avatars/avataaars_14.png
    - assets/avatars/avataaars_15.png

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/lingo_10_iconnew.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/lingo_10_iconnew.png"
    background_color: "#ffffff"
    theme_color: "#2196F3"
  windows:
    generate: true
    image_path: "assets/icons/lingo_10_iconnew.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/lingo_10_iconnew.png"

flutter_native_splash:
  # لون الخلفية للوضع العادي (النهاري)
  color: "#FFFFFF"
  image: "assets/images/10again_splash_screen.png"

  # لون الخلفية للوضع المظلم
  color_dark: "#1E1E2E"
  image_dark: "assets/images/10again_splash_screen_dark.png"

  # إعدادات Android 12
  android_12:
    image: "assets/images/10again_splash_screen.png"
    icon_background_color: "#FFFFFF"
    image_dark: "assets/images/10again_splash_screen_dark.png"
    icon_background_color_dark: "#1E1E2E"

  web: false
  fullscreen: true
