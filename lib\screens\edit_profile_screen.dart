import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_view_model.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'dart:io';
import '../theme/app_theme.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  String? _selectedGender;
  DateTime? _selectedDate;
  final _countryController = TextEditingController();
  bool _isLoading = false;

  final List<String> _genders = ['ذكر', 'أنثى'];

  File? _imageFile;
  String? _selectedAvatarPath;

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('ar_SA');
    _loadUserData();
  }

  void _loadUserData() {
    // استخدام read بدلاً من watch في initState
    final authVM = context.read<AuthViewModel>();
    final user = authVM.user;
    if (user != null) {
      _nameController.text = user.displayName ?? '';
      _selectedGender = authVM.gender;
      _selectedDate = authVM.birthDate;
      _countryController.text = authVM.country ?? '';

      // تحميل الصورة المختارة إذا كانت من مجلد الصور
      final photoURL = user.photoURL;
      if (photoURL != null && photoURL.startsWith('assets/avatars/')) {
        setState(() {
          _selectedAvatarPath = photoURL;
        });
      }
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat.yMMMd('ar_SA').format(date);
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('ar', 'SA'),
    );
    if (picked != null && mounted) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _pickImage() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildAvatarSelectionSheet(),
    );
  }

  Widget _buildAvatarSelectionSheet() {
    // قائمة بأسماء ملفات الصور - تأكد من أن هذه المسارات صحيحة
    final List<String> avatarFiles = List.generate(
        15, (index) => 'assets/avatars/avataaars_${index + 1}.png');

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'اختر صورة شخصية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
              ),
              itemCount: avatarFiles.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedAvatarPath = avatarFiles[index];
                      _imageFile =
                          null; // إلغاء الصورة المختارة من المعرض إن وجدت
                    });
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      shape: BoxShape.circle,
                      border: _selectedAvatarPath == avatarFiles[index]
                          ? Border.all(
                              color: AppTheme.primaryColor,
                              width: 3,
                            )
                          : null,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        avatarFiles[index],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // في حالة حدوث خطأ في تحميل الصورة، عرض أيقونة بديلة
                          return Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.person,
                              size: 40,
                              color: Colors.grey,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage() {
    return Center(
      child: Stack(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
              image: _getProfileImage(),
            ),
            child: _shouldShowDefaultIcon()
                ? const Icon(Icons.person, size: 50, color: Colors.grey)
                : null,
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: InkWell(
              onTap: _pickImage,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.camera_alt,
                  size: 20,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DecorationImage? _getProfileImage() {
    if (_selectedAvatarPath != null) {
      return DecorationImage(
        image: AssetImage(_selectedAvatarPath!),
        fit: BoxFit.cover,
      );
    } else if (_imageFile != null) {
      return DecorationImage(
        image: FileImage(_imageFile!),
        fit: BoxFit.cover,
      );
    } else if (context.watch<AuthViewModel>().user?.photoURL != null) {
      final photoURL = context.watch<AuthViewModel>().user!.photoURL!;
      if (photoURL.startsWith('assets/')) {
        return DecorationImage(
          image: AssetImage(photoURL),
          fit: BoxFit.cover,
        );
      } else {
        return DecorationImage(
          image: NetworkImage(photoURL),
          fit: BoxFit.cover,
        );
      }
    }
    return null;
  }

  bool _shouldShowDefaultIcon() {
    return _selectedAvatarPath == null &&
        _imageFile == null &&
        context.watch<AuthViewModel>().user?.photoURL == null;
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authVM = context.read<AuthViewModel>();

        String? photoURL;
        if (_selectedAvatarPath != null) {
          photoURL = _selectedAvatarPath;
        } else if (_imageFile != null) {
          // في حالة استخدام صورة من المعرض (غير مستخدم حاليًا)
          // photoURL = await uploadImage(_imageFile!);
        }

        final success = await authVM.updateUserProfile(
          displayName: _nameController.text,
          gender: _selectedGender,
          birthDate: _selectedDate,
          country: _countryController.text,
          photoURL: photoURL,
        );

        if (!mounted) return;

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث البيانات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authVM.error ?? 'حدث خطأ في تحديث البيانات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        centerTitle: true,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildProfileImage(),
            const SizedBox(height: 24),

            // حقل الاسم
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال الاسم';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // حقل الجنس
            DropdownButtonFormField<String>(
              value: _selectedGender,
              decoration: const InputDecoration(
                labelText: 'الجنس',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person_outline),
              ),
              items: _genders.map((gender) {
                return DropdownMenuItem(
                  value: gender,
                  child: Text(gender),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedGender = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // حقل تاريخ الميلاد
            InkWell(
              onTap: () => _selectDate(context),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ الميلاد',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _selectedDate != null
                      ? _formatDate(_selectedDate)
                      : 'اختر التاريخ',
                ),
              ),
            ),
            const SizedBox(height: 16),

            // حقل الدولة
            TextFormField(
              controller: _countryController,
              decoration: const InputDecoration(
                labelText: 'البلد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
            ),
            const SizedBox(height: 24),

            // زر الحفظ
            ElevatedButton(
              onPressed: _isLoading ? null : _saveProfile,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text(
                      'حفظ التغييرات',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _countryController.dispose();
    super.dispose();
  }
}

// Ya no necesitamos esta extensión porque ahora usamos los getters de AuthViewModel
