import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class CategoryService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Connectivity _connectivity = Connectivity();
  
  // Claves para Hive
  static const String _categoriesBoxName = 'categories';
  static const String _categoriesKey = 'all_categories';
  
  // Colección de categorías
  CollectionReference get _categoriesCollection => 
      _firestore.collection('categories');
  
  // Verificar si hay conexión a Internet
  Future<bool> get isConnected async {
    var connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
  
  // Obtener la caja de Hive para categorías
  Future<Box> _getCategoriesBox() async {
    if (!Hive.isBoxOpen(_categoriesBoxName)) {
      return await Hive.openBox(_categoriesBoxName);
    }
    return Hive.box(_categoriesBoxName);
  }
  
  // Guardar categorías en el almacenamiento local
  Future<void> _saveCategoriesLocally(List<Map<String, dynamic>> categories) async {
    final box = await _getCategoriesBox();
    await box.put(_categoriesKey, categories);
  }
  
  // Obtener categorías del almacenamiento local
  Future<List<Map<String, dynamic>>> _getCategoriesLocally() async {
    final box = await _getCategoriesBox();
    final categories = box.get(_categoriesKey);
    
    if (categories == null) {
      return _getDefaultCategories();
    }
    
    return List<Map<String, dynamic>>.from(categories);
  }
  
  // Categorías predeterminadas (en caso de que no haya conexión y no haya datos locales)
  List<Map<String, dynamic>> _getDefaultCategories() {
    return [
      {'id': 'greetings', 'name': 'التحيات', 'nameEn': 'Greetings'},
      {'id': 'travel', 'name': 'السفر', 'nameEn': 'Travel'},
      {'id': 'food', 'name': 'الطعام', 'nameEn': 'Food'},
      {'id': 'shopping', 'name': 'التسوق', 'nameEn': 'Shopping'},
      {'id': 'work', 'name': 'العمل', 'nameEn': 'Work'},
    ];
  }
  
  // Obtener todas las categorías
  Future<List<Map<String, dynamic>>> getCategories() async {
    // Primero intentar obtener las categorías localmente
    List<Map<String, dynamic>> localCategories = await _getCategoriesLocally();
    
    // Si hay conexión a Internet, intentar obtener las categorías de Firebase
    if (await isConnected) {
      try {
        QuerySnapshot snapshot = await _categoriesCollection.get();
        
        if (snapshot.docs.isNotEmpty) {
          List<Map<String, dynamic>> firebaseCategories = snapshot.docs
              .map((doc) => {
                    'id': doc.id,
                    'name': (doc.data() as Map<String, dynamic>)['name'] ?? '',
                    'nameEn': (doc.data() as Map<String, dynamic>)['nameEn'] ?? '',
                  })
              .toList();
          
          // Guardar las categorías localmente
          await _saveCategoriesLocally(firebaseCategories);
          
          return firebaseCategories;
        }
      } catch (e) {
        // Si hay un error, devolver las categorías locales
      }
    }
    
    // Si no hay conexión o no se encontraron categorías en Firebase, devolver las categorías locales
    return localCategories;
  }
}
