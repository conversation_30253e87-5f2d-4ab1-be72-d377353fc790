import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class DateCheckService {
  static const String _lastUpdateDateKey = 'last_sentences_update_date';
  
  // تحقق مما إذا كان اليوم جديدًا مقارنة بآخر تحديث
  Future<bool> isNewDay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateString = prefs.getString(_lastUpdateDateKey);
      
      // إذا لم يكن هناك تاريخ محفوظ، فهذا يعني أنه يوم جديد
      if (lastUpdateString == null) {
        await saveCurrentDate();
        return true;
      }
      
      // تحويل النص المحفوظ إلى كائن DateTime
      final lastUpdate = DateTime.parse(lastUpdateString);
      final now = DateTime.now();
      
      // مقارنة اليوم والشهر والسنة فقط (بدون الوقت)
      final lastUpdateDay = DateTime(lastUpdate.year, lastUpdate.month, lastUpdate.day);
      final today = DateTime(now.year, now.month, now.day);
      
      // إذا كان اليوم الحالي بعد آخر تحديث، فهذا يعني أنه يوم جديد
      final isNewDay = today.isAfter(lastUpdateDay);
      
      // إذا كان يومًا جديدًا، قم بتحديث التاريخ المحفوظ
      if (isNewDay) {
        await saveCurrentDate();
      }
      
      return isNewDay;
    } catch (e) {
      debugPrint('Error checking for new day: $e');
      return false;
    }
  }
  
  // حفظ التاريخ الحالي
  Future<void> saveCurrentDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      await prefs.setString(_lastUpdateDateKey, now.toIso8601String());
      debugPrint('Saved current date: ${now.toIso8601String()}');
    } catch (e) {
      debugPrint('Error saving current date: $e');
    }
  }
  
  // تحديث التاريخ المحفوظ (يستخدم عند التحديث اليدوي)
  Future<void> updateLastUpdateDate() async {
    await saveCurrentDate();
  }
}
