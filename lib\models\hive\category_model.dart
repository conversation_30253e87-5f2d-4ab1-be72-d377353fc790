import 'package:hive/hive.dart';

part 'category_model.g.dart';

/// نموذج الفئة المخزنة في Hive
@HiveType(typeId: 4)
class CategoryModel extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final DateTime createdAt;
  
  @HiveField(3)
  DateTime? lastModified;

  CategoryModel({
    required this.id,
    required this.name,
    required this.createdAt,
    this.lastModified,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified?.toIso8601String(),
    };
  }
}
