import 'package:flutter/material.dart';

enum SortOrder { newest, oldest }

class FilterOptions {
  final String? category;
  final SortOrder sortOrder;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? difficulty; // إضافة حقل مستوى الصعوبة

  FilterOptions({
    this.category,
    this.sortOrder = SortOrder.newest,
    this.startDate,
    this.endDate,
    this.difficulty, // إضافة حقل مستوى الصعوبة
  });

  FilterOptions copyWith({
    String? category,
    SortOrder? sortOrder,
    DateTime? startDate,
    DateTime? endDate,
    String? difficulty, // إضافة حقل مستوى الصعوبة
    bool clearCategory = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
    bool clearDifficulty = false, // إضافة خيار مسح مستوى الصعوبة
  }) {
    return FilterOptions(
      category: clearCategory ? null : (category ?? this.category),
      sortOrder: sortOrder ?? this.sortOrder,
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      difficulty: clearDifficulty
          ? null
          : (difficulty ?? this.difficulty), // إضافة حقل مستوى الصعوبة
    );
  }
}

class FilterBottomSheet extends StatefulWidget {
  final FilterOptions initialFilters;
  final List<String> categories;
  final Function(FilterOptions) onApplyFilters;

  const FilterBottomSheet({
    super.key,
    required this.initialFilters,
    required this.categories,
    required this.onApplyFilters,
  });

  static Future<void> show({
    required BuildContext context,
    required FilterOptions initialFilters,
    required List<String> categories,
    required Function(FilterOptions) onApplyFilters,
  }) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.7,
          child: FilterBottomSheet(
            initialFilters: initialFilters,
            categories: categories,
            onApplyFilters: onApplyFilters,
          ),
        ),
      ),
    );
  }

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late FilterOptions _currentFilters;

  @override
  void initState() {
    super.initState();
    _currentFilters = widget.initialFilters;
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? _currentFilters.startDate ?? DateTime.now()
          : _currentFilters.endDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Theme.of(context).textTheme.bodyLarge!.color!,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _currentFilters = _currentFilters.copyWith(startDate: picked);
        } else {
          _currentFilters = _currentFilters.copyWith(endDate: picked);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'تصفية النتائج',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category Filter
                const Text(
                  'الفئة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String?>(
                      isExpanded: true,
                      value: _currentFilters.category,
                      hint: const Text('جميع الفئات'),
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('جميع الفئات'),
                        ),
                        ...widget.categories.map((category) {
                          return DropdownMenuItem<String?>(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _currentFilters = _currentFilters.copyWith(
                            category: value,
                            clearCategory: value == null,
                          );
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Difficulty Filter
                const Text(
                  'مستوى الصعوبة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String?>(
                      isExpanded: true,
                      value: _currentFilters.difficulty,
                      hint: const Text('جميع المستويات'),
                      items: const [
                        DropdownMenuItem<String?>(
                          value: null,
                          child: Text('جميع المستويات'),
                        ),
                        DropdownMenuItem<String?>(
                          value: 'easy',
                          child: Text('سهل'),
                        ),
                        DropdownMenuItem<String?>(
                          value: 'medium',
                          child: Text('متوسط'),
                        ),
                        DropdownMenuItem<String?>(
                          value: 'hard',
                          child: Text('صعب'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _currentFilters = _currentFilters.copyWith(
                            difficulty: value,
                            clearDifficulty: value == null,
                          );
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Sort Order
                const Text(
                  'ترتيب حسب',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<SortOrder>(
                        title: const Text('الأحدث'),
                        value: SortOrder.newest,
                        groupValue: _currentFilters.sortOrder,
                        onChanged: (value) {
                          setState(() {
                            _currentFilters = _currentFilters.copyWith(
                              sortOrder: value,
                            );
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<SortOrder>(
                        title: const Text('الأقدم'),
                        value: SortOrder.oldest,
                        groupValue: _currentFilters.sortOrder,
                        onChanged: (value) {
                          setState(() {
                            _currentFilters = _currentFilters.copyWith(
                              sortOrder: value,
                            );
                          });
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Date Range
                const Text(
                  'نطاق التاريخ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, true),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'من',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            suffixIcon: _currentFilters.startDate != null
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      setState(() {
                                        _currentFilters =
                                            _currentFilters.copyWith(
                                          clearStartDate: true,
                                        );
                                      });
                                    },
                                  )
                                : const Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _currentFilters.startDate != null
                                ? '${_currentFilters.startDate!.day}/${_currentFilters.startDate!.month}/${_currentFilters.startDate!.year}'
                                : 'اختر التاريخ',
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, false),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'إلى',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            suffixIcon: _currentFilters.endDate != null
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      setState(() {
                                        _currentFilters =
                                            _currentFilters.copyWith(
                                          clearEndDate: true,
                                        );
                                      });
                                    },
                                  )
                                : const Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _currentFilters.endDate != null
                                ? '${_currentFilters.endDate!.day}/${_currentFilters.endDate!.month}/${_currentFilters.endDate!.year}'
                                : 'اختر التاريخ',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Buttons
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _currentFilters = FilterOptions();
                  });
                },
                child: const Text('إعادة تعيين'),
              ),
              ElevatedButton(
                onPressed: () {
                  widget.onApplyFilters(_currentFilters);
                  Navigator.pop(context);
                },
                child: const Text('تطبيق'),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
