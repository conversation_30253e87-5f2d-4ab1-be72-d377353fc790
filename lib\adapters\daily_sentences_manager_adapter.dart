import 'package:flutter/foundation.dart';
import '../models/sentence_model.dart';
import '../models/hive/hive_sentence_model.dart';
import '../services/daily_sentences_manager.dart';

/// محول بين SentenceViewModel و DailySentencesManager
/// يسمح لـ SentenceViewModel باستخدام DailySentencesManager للتخزين المحلي
class DailySentencesManagerAdapter {
  final DailySentencesManager _dailySentencesManager;

  DailySentencesManagerAdapter(this._dailySentencesManager);

  /// تحويل HiveSentenceModel إلى SentenceModel
  SentenceModel toSentenceModel(HiveSentenceModel hiveModel) {
    return SentenceModel(
      id: hiveModel.id,
      arabicText: hiveModel.arabicText,
      englishText: hiveModel.englishText,
      category: hiveModel.category,
      createdAt: hiveModel.createdAt,
      readBy: {}, // لا نستخدم readBy في النموذج الجديد
      isFavorite: false, // لا نستخدم isFavorite في النموذج الجديد
      difficulty: hiveModel.difficulty,
      audioUrl: hiveModel.audioUrl,
      isReadByCurrentUser: hiveModel.isReadByCurrentUser,
      isFavoriteByCurrentUser: hiveModel.isFavoriteByCurrentUser,
    );
  }

  /// تحويل SentenceModel إلى HiveSentenceModel
  HiveSentenceModel toHiveModel(SentenceModel model) {
    return HiveSentenceModel(
      id: model.id,
      arabicText: model.arabicText,
      englishText: model.englishText,
      category: model.category,
      createdAt: model.createdAt,
      audioUrl: model.audioUrl,
      difficulty: model.difficulty,
      isReadByCurrentUser: model.isReadByCurrentUser,
      isFavoriteByCurrentUser: model.isFavoriteByCurrentUser,
      lastModified: DateTime.now(),
    );
  }

  /// تعيين جملة كمقروءة
  Future<bool> markSentenceAsRead(SentenceModel model) async {
    try {
      final result = await _dailySentencesManager.markSentenceAsRead(model.id);
      debugPrint(
          'تم تعيين الجملة ${model.id} كمقروءة باستخدام DailySentencesManager');
      return result;
    } catch (e) {
      debugPrint('خطأ في تعيين الجملة كمقروءة: $e');
      return false;
    }
  }

  /// الحصول على الجمل اليومية (بما في ذلك المقروءة)
  List<SentenceModel> getDailySentences() {
    try {
      final hiveSentences = _dailySentencesManager.getDailySentences();
      final sentences = hiveSentences.map(toSentenceModel).toList();

      // طباعة معلومات تصحيح
      debugPrint('تم الحصول على ${sentences.length} جملة يومية من المحول');
      for (var sentence in sentences) {
        debugPrint(
            'الجملة ${sentence.id}: مقروءة=${sentence.isReadByCurrentUser}');
      }

      return sentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية: $e');
      return [];
    }
  }

  /// الحصول على الجمل اليومية غير المقروءة
  List<SentenceModel> getUnreadDailySentences() {
    try {
      final hiveSentences =
          _dailySentencesManager.getDailySentences(onlyUnread: true);
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية غير المقروءة: $e');
      return [];
    }
  }

  /// الحصول على عدد الجمل المعروضة اليوم
  int getTodayShownCount() {
    return _dailySentencesManager.getTodayShownCount();
  }

  /// الحصول على عدد الجمل المقروءة اليوم
  int getTodayReadCount() {
    return _dailySentencesManager.getTodayReadCount();
  }

  /// التحقق مما إذا كان يوم جديد قد بدأ
  bool isNewDay() {
    return _dailySentencesManager.isNewDay();
  }

  /// التحقق مما إذا كانت جميع الجمل اليومية مقروءة
  bool areAllDailySentencesRead() {
    return _dailySentencesManager.areAllDailySentencesRead();
  }

  /// الحصول على جمل جديدة للقراءة اليومية
  Future<List<SentenceModel>> getNewDailySentences(int count,
      {String? userId}) async {
    try {
      final hiveSentences = await _dailySentencesManager
          .getNewDailySentences(count, userId: userId);
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل جديدة للقراءة اليومية: $e');
      return [];
    }
  }

  /// الحصول على جمل جديدة للقراءة اليومية بشكل قسري
  Future<List<SentenceModel>> forceGetNewDailySentences(int count,
      {String? userId}) async {
    try {
      final hiveSentences = await _dailySentencesManager
          .forceGetNewDailySentences(count, userId: userId);
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل جديدة للقراءة اليومية بشكل قسري: $e');
      return [];
    }
  }

  /// جلب جميع الجمل المتاحة من Firebase وتخزينها في Hive
  /// يعيد عدد الجمل التي تم تخزينها
  Future<int> fetchAllAvailableSentences({String? userId}) async {
    try {
      if (userId == null) {
        debugPrint('لا يمكن جلب جميع الجمل بدون معرف المستخدم');
        return 0;
      }

      return await _dailySentencesManager.fetchAllAvailableSentences(userId);
    } catch (e) {
      debugPrint('خطأ في جلب جميع الجمل المتاحة: $e');
      return 0;
    }
  }

  /// الحصول على جميع الجمل المخزنة محليًا
  List<SentenceModel> getAllSentences() {
    try {
      final hiveSentences = _dailySentencesManager.getAllSentences();
      return hiveSentences.map(toSentenceModel).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على جميع الجمل: $e');
      return [];
    }
  }
}
