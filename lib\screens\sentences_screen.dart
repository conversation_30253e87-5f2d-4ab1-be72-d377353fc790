import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../viewmodels/sentence_view_model.dart';
import '../viewmodels/auth_view_model.dart';
import '../models/sentence_model.dart';
import '../widgets/filter_bottom_sheet.dart';
import 'add_sentence_screen.dart';

class SentencesScreen extends StatefulWidget {
  const SentencesScreen({super.key});

  @override
  State<SentencesScreen> createState() => _SentencesScreenState();
}

class _SentencesScreenState extends State<SentencesScreen> {
  bool _isLoadingMore = false;
  FilterOptions _filterOptions = FilterOptions();
  List<String> _categories = [];

  // إضافة ScrollController للحفاظ على موضع التمرير
  final ScrollController _scrollController = ScrollController();

  // قائمة محلية لتخزين الجمل المحملة
  final List<SentenceModel> _loadedSentences = [];
  bool _hasMoreSentences = true;
  bool _initialLoadDone = false;
  Map<String, int> _categorySentenceCounts = {};
  int _totalSentencesCount = 0;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  // دالة للتحقق من التمرير للأسفل
  // لن نستخدم التحميل التلقائي عند التمرير، بل سنعتمد على زر "عرض المزيد"
  bool _handleScrollNotification(ScrollNotification notification) {
    // لا نفعل شيئًا عند التمرير
    return false; // السماح باستمرار التمرير
  }

  Future<void> _loadCategories() async {
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
    await viewModel.loadCategories();
    setState(() {
      _categories = viewModel.categories;
    });
    _loadCategorySentenceCounts();
  }

  Future<void> _loadCategorySentenceCounts() async {
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);

    try {
      // الحصول على عدد الجمل لكل فئة
      final categoryCounts = await viewModel.getCategorySentenceCounts();

      // الحصول على العدد الإجمالي للجمل
      final totalCount = await viewModel.getTotalSentencesCount();

      setState(() {
        _categorySentenceCounts = categoryCounts;
        _totalSentencesCount = totalCount;
      });
    } catch (e) {
      debugPrint('Error loading category sentence counts: $e');
    }
  }

  @override
  void dispose() {
    // تنظيف ScrollController عند إغلاق الشاشة
    _scrollController.dispose();
    super.dispose();
  }

  void _showFilterBottomSheet() {
    FilterBottomSheet.show(
      context: context,
      initialFilters: _filterOptions,
      categories: _categories,
      onApplyFilters: (filters) {
        setState(() {
          _filterOptions = filters;
          _resetAndReload();
        });
      },
    );
  }

  Future<void> _loadInitialSentences() async {
    if (_initialLoadDone) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final viewModel = Provider.of<SentenceViewModel>(context, listen: false);

      // إعادة تعيين آخر مستند للتصفح
      viewModel.resetLastLoadedDocument();

      // تحميل العدد الإجمالي للجمل
      final totalCount = await viewModel.getTotalSentencesCount();

      // تحميل الجمل بناءً على الفلتر
      List<SentenceModel> initialSentences;

      // استخدام دالة الحصول على جميع الجمل مع دعم الفلترة
      initialSentences = await viewModel.getAllSentencesAsList(
        limit: 20,
        category: _filterOptions.category,
        startDate: _filterOptions.startDate,
        endDate: _filterOptions.endDate,
        difficulty: _filterOptions.difficulty, // إضافة فلتر الصعوبة
        descending: _filterOptions.sortOrder == SortOrder.newest,
        resetPagination: true, // إعادة تعيين التصفح
      );

      if (mounted) {
        setState(() {
          _loadedSentences.clear();
          _loadedSentences.addAll(initialSentences);
          _hasMoreSentences = initialSentences.length >=
              20; // إذا كان عدد الجمل المحملة يساوي الحد، فربما هناك المزيد
          _isLoadingMore = false;
          _initialLoadDone = true;
          _totalSentencesCount =
              totalCount; // عرض العدد الإجمالي للجمل في قاعدة البيانات
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          _initialLoadDone = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل الجمل: $e')),
        );
      }
    }
  }

  Future<void> _resetAndReload() async {
    setState(() {
      _loadedSentences.clear();
      _hasMoreSentences = true;
      _initialLoadDone = false;
      _isLoadingMore = true; // لإظهار مؤشر التحميل
    });
    await _loadInitialSentences();
  }

  // دالة لإنشاء تأثير shimmer
  Widget _buildShimmerEffect() {
    return Shimmer.fromColors(
      baseColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey[800]!
          : Colors.grey[300]!,
      highlightColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey[700]!
          : Colors.grey[100]!,
      child: Column(
        children: [
          // بطاقة الإحصائيات
          Card(
            margin: const EdgeInsets.all(8),
            child: Container(
              height: 60,
              padding: const EdgeInsets.all(16),
              width: double.infinity,
            ),
          ),

          // بطاقات الفئات
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              itemBuilder: (context, index) {
                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  child: Container(
                    width: 120,
                    padding: const EdgeInsets.all(8),
                  ),
                );
              },
            ),
          ),

          // قائمة الجمل
          Expanded(
            child: ListView.builder(
              itemCount: 5,
              padding: const EdgeInsets.all(8),
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.all(8),
                  child: Container(
                    height: 120,
                    padding: const EdgeInsets.all(16),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadMore() async {
    if (!_isLoadingMore && _hasMoreSentences) {
      // حفظ موضع التمرير الحالي قبل تحميل المزيد من الجمل
      final double previousScrollPosition = _scrollController.position.pixels;
      final int previousItemsCount = _loadedSentences.length;

      setState(() {
        _isLoadingMore = true;
      });

      try {
        final viewModel =
            Provider.of<SentenceViewModel>(context, listen: false);

        // استخدام دالة الحصول على جميع الجمل مع دعم الفلترة
        // سيتم استخدام آخر مستند تم تخزينه في ViewModel
        final newSentences = await viewModel.getAllSentencesAsList(
          limit: 20,
          category: _filterOptions.category,
          startDate: _filterOptions.startDate,
          endDate: _filterOptions.endDate,
          difficulty: _filterOptions.difficulty, // إضافة فلتر الصعوبة
          descending: _filterOptions.sortOrder == SortOrder.newest,
          // لا نحتاج إلى توفير lastDocument أو excludeIds لأن ViewModel يتعامل مع ذلك
        );

        if (mounted) {
          setState(() {
            if (newSentences.isEmpty) {
              _hasMoreSentences = false;
            } else {
              _loadedSentences.addAll(newSentences);
              _hasMoreSentences = newSentences.length >=
                  20; // إذا كان عدد الجمل الجديدة أقل من الحد، فلا يوجد المزيد
            }
            _isLoadingMore = false;
          });

          // استخدام WidgetsBinding.instance.addPostFrameCallback للتأكد من أن setState قد اكتمل
          // وأن ListView قد تم إعادة بنائه قبل محاولة التمرير
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_scrollController.hasClients &&
                previousItemsCount < _loadedSentences.length) {
              // الحفاظ على نفس موضع التمرير بعد تحميل المزيد من الجمل
              _scrollController.jumpTo(previousScrollPosition);

              // إظهار رسالة للمستخدم
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'تم تحميل ${_loadedSentences.length - previousItemsCount} جملة إضافية'),
                  duration: const Duration(seconds: 1),
                ),
              );
            }
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ أثناء تحميل المزيد من الجمل: $e')),
          );
        }
      }
    }
  }

  // دوال مساعدة لعرض مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getDifficultyIcon(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Icons.sentiment_satisfied_alt;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  String _buildFilterDescription() {
    final List<String> parts = [];

    if (_filterOptions.category != null) {
      parts.add('الفئة: ${_filterOptions.category}');
    }

    if (_filterOptions.difficulty != null) {
      parts.add('الصعوبة: ${_getDifficultyText(_filterOptions.difficulty)}');
    }

    if (_filterOptions.sortOrder == SortOrder.newest) {
      parts.add('الترتيب: الأحدث');
    } else {
      parts.add('الترتيب: الأقدم');
    }

    if (_filterOptions.startDate != null) {
      parts.add(
          'من: ${_filterOptions.startDate!.day}/${_filterOptions.startDate!.month}/${_filterOptions.startDate!.year}');
    }

    if (_filterOptions.endDate != null) {
      parts.add(
          'إلى: ${_filterOptions.endDate!.day}/${_filterOptions.endDate!.month}/${_filterOptions.endDate!.year}');
    }

    return parts.join(' | ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('جمل إنجليزية'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
            tooltip: 'تصفية',
          ),
        ],
      ),
      floatingActionButton: Consumer<AuthViewModel>(
        builder: (context, authViewModel, _) {
          // عرض زر الإضافة فقط للمشرفين
          return authViewModel.isAdmin
              ? FloatingActionButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AddSentenceScreen(),
                      ),
                    );
                  },
                  child: const Icon(Icons.add),
                )
              : const SizedBox.shrink(); // إخفاء الزر للمستخدمين العاديين
        },
      ),
      body: FutureBuilder<void>(
        future: _loadInitialSentences(),
        builder: (context, snapshot) {
          if (!_initialLoadDone || _isLoadingMore) {
            return _buildShimmerEffect();
          }

          return Column(
            children: [
              // بطاقة عدد الجمل
              Card(
                margin: const EdgeInsets.all(8),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'عدد الجمل: $_totalSentencesCount',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_filterOptions.category != null ||
                          _filterOptions.startDate != null ||
                          _filterOptions.endDate != null ||
                          _filterOptions.sortOrder != SortOrder.newest)
                        Text(
                          'نتائج البحث: ${_loadedSentences.length}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // بطاقات الفئات
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _categories.length,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    final count = _categorySentenceCounts[category] ?? 0;
                    return Card(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 8),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _filterOptions = _filterOptions.copyWith(
                              category: category,
                              clearCategory:
                                  _filterOptions.category == category,
                            );
                            _resetAndReload();
                          });
                        },
                        child: Container(
                          width: 120,
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                category,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '$count جملة',
                                style: TextStyle(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).primaryColor,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // مؤشر الفلترة النشطة
              if (_filterOptions.category != null ||
                  _filterOptions.startDate != null ||
                  _filterOptions.endDate != null ||
                  _filterOptions.sortOrder != SortOrder.newest)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: Theme.of(context).primaryColor.withAlpha(26),
                  child: Row(
                    children: [
                      const Icon(Icons.filter_list, size: 18),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _buildFilterDescription(),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.clear, size: 18),
                        onPressed: () {
                          setState(() {
                            _filterOptions = FilterOptions();
                            _resetAndReload();
                          });
                        },
                        tooltip: 'مسح التصفية',
                      ),
                    ],
                  ),
                ),

              // قائمة الجمل
              Expanded(
                child: _loadedSentences.isEmpty
                    ? const Center(
                        child: Text('لا توجد جمل مطابقة للبحث'),
                      )
                    : NotificationListener<ScrollNotification>(
                        onNotification: _handleScrollNotification,
                        child: ListView.builder(
                          controller:
                              _scrollController, // استخدام ScrollController
                          itemCount: _loadedSentences.length + 1,
                          padding: const EdgeInsets.all(8),
                          itemBuilder: (context, index) {
                            if (index == _loadedSentences.length) {
                              if (_isLoadingMore) {
                                return const Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: Center(
                                      child: CircularProgressIndicator()),
                                );
                              } else if (!_hasMoreSentences) {
                                return const Padding(
                                  padding: EdgeInsets.all(16.0),
                                  child: Center(
                                    child: Text(
                                      'لا يوجد المزيد من الجمل',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ),
                                );
                              } else {
                                // زر عرض المزيد
                                return Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Center(
                                    child: ElevatedButton.icon(
                                      onPressed: _loadMore,
                                      icon: const Icon(Icons.refresh),
                                      label: const Text('عرض المزيد'),
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 24, vertical: 12),
                                      ),
                                    ),
                                  ),
                                );
                              }
                            }

                            final sentence = _loadedSentences[index];
                            return Card(
                              margin: const EdgeInsets.all(8),
                              elevation: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? 2
                                  : 1,
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      sentence.englishText,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      sentence.arabicText,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        // عرض الفئة
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withAlpha(40)
                                                    : Theme.of(context)
                                                        .primaryColor
                                                        .withAlpha(26),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            'الفئة: ${sentence.category}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Theme.of(context)
                                                      .colorScheme
                                                      .primary
                                                  : Theme.of(context)
                                                      .primaryColor,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        // عرض مستوى الصعوبة
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: _getDifficultyColor(
                                                sentence.difficulty),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                _getDifficultyIcon(
                                                    sentence.difficulty),
                                                size: 14,
                                                color: Colors.white,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                _getDifficultyText(
                                                    sentence.difficulty),
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const Spacer(),
                                        StatefulBuilder(
                                          builder: (context, setIconState) {
                                            return Consumer<SentenceViewModel>(
                                              builder: (context, viewModel, _) {
                                                return IconButton(
                                                  icon: Icon(
                                                    sentence.isFavoriteByCurrentUser
                                                        ? Icons.favorite
                                                        : Icons.favorite_border,
                                                    color: sentence
                                                            .isFavoriteByCurrentUser
                                                        ? Colors.red
                                                        : null,
                                                  ),
                                                  onPressed: () {
                                                    // استخدام toggleFavorite فقط - سيقوم بتحديث الحالة محليًا وفي جميع القوائم
                                                    viewModel.toggleFavorite(
                                                        sentence);

                                                    // تحديث حالة الأيقونة مباشرة (للتأكد من التحديث الفوري في الواجهة)
                                                    setIconState(() {});
                                                  },
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class SentenceCard extends StatefulWidget {
  final SentenceModel sentence;

  const SentenceCard({
    super.key,
    required this.sentence,
  });

  @override
  State<SentenceCard> createState() => _SentenceCardState();
}

class _SentenceCardState extends State<SentenceCard> {
  bool _showTranslation = false;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
  }

  Future<void> _checkFavoriteStatus() async {
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
    final isFavorite = viewModel.isFavorite(widget.sentence.id);
    if (mounted) {
      setState(() {
        _isFavorite = isFavorite;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.sentence.englishText,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    _isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: _isFavorite ? Colors.red : null,
                  ),
                  onPressed: () {
                    final viewModel =
                        Provider.of<SentenceViewModel>(context, listen: false);

                    // تحديث حالة المفضلة محليًا فورًا
                    setState(() {
                      _isFavorite = !_isFavorite;
                    });

                    // استخدام toggleFavorite لتحسين التعامل مع حالات الاتصال الضعيف بالإنترنت
                    viewModel.toggleFavorite(widget.sentence);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_showTranslation) ...[
              const Divider(),
              Text(
                widget.sentence.arabicText,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                setState(() {
                  _showTranslation = !_showTranslation;
                });
              },
              child: Text(_showTranslation ? 'إخفاء الترجمة' : 'إظهار الترجمة'),
            ),
          ],
        ),
      ),
    );
  }
}
