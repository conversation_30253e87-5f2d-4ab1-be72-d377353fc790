import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/hive_sentence_view_model.dart';
import '../viewmodels/auth_view_model.dart';
import '../services/connectivity_service.dart';

/// ويدجت لعرض حالة المزامنة
class SyncStatusWidget extends StatelessWidget {
  const SyncStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final connectivityService = Provider.of<ConnectivityService>(context);
    final isOnline = connectivityService.isOnline;

    return IconButton(
      icon: Icon(
        isOnline ? Icons.cloud_done : Icons.cloud_off,
        color: isOnline ? Colors.green : Colors.grey,
      ),
      onPressed: () => _showSyncDialog(context, isOnline),
      tooltip: isOnline ? 'متصل بالإنترنت' : 'غير متصل بالإنترنت',
    );
  }

  /// عرض حوار المزامنة
  void _showSyncDialog(BuildContext context, bool isOnline) {
    final hiveSentenceViewModel =
        Provider.of<HiveSentenceViewModel>(context, listen: false);
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة المزامنة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isOnline)
              const Text(
                  'أنت متصل بالإنترنت ويمكنك مزامنة البيانات مع السحابة.')
            else
              const Text(
                  'أنت غير متصل بالإنترنت. سيتم تخزين التغييرات محليًا ومزامنتها لاحقًا عند توفر الاتصال.'),
            const SizedBox(height: 16),
            if (isOnline)
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  // عرض مؤشر التقدم
                  _showLoadingDialog(context);

                  // مزامنة البيانات
                  final success = await hiveSentenceViewModel.syncWithFirebase(
                    authViewModel.user?.uid ?? '',
                  );

                  // إغلاق مؤشر التقدم
                  if (context.mounted) {
                    Navigator.pop(context);
                  }

                  // عرض رسالة النجاح أو الفشل
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        success
                            ? 'تمت المزامنة بنجاح'
                            : 'فشلت المزامنة، حاول مرة أخرى لاحقًا',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                },
                child: const Text('مزامنة الآن'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// عرض مؤشر التقدم
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري المزامنة...'),
          ],
        ),
      ),
    );
  }
}
