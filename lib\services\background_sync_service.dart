import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/connectivity_service.dart';
import '../services/sentence_service.dart';
import '../viewmodels/auth_view_model.dart';

class BackgroundSyncService {
  final ConnectivityService _connectivityService;
  final AuthViewModel _authViewModel;
  Timer? _syncTimer;
  bool _isSyncing = false;

  BackgroundSyncService({
    required ConnectivityService connectivityService,
    required SentenceService
        sentenceService, // Keep parameter for compatibility
    required AuthViewModel authViewModel,
  })  : _connectivityService = connectivityService,
        _authViewModel = authViewModel {
    _initialize();
  }

  void _initialize() {
    // Listen to connectivity changes
    _connectivityService.addListener(_onConnectivityChanged);

    // Start periodic sync if online
    if (_connectivityService.isOnline) {
      _startPeriodicSync();
    }
  }

  void _onConnectivityChanged() {
    if (_connectivityService.isOnline) {
      _startPeriodicSync();
    } else {
      _stopPeriodicSync();
    }
  }

  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(minutes: 15), (_) => sync());

    // Perform initial sync
    sync();
  }

  void _stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  Future<void> sync() async {
    if (!_connectivityService.isOnline ||
        _isSyncing ||
        !_authViewModel.isAuthenticated) {
      return;
    }

    try {
      _isSyncing = true;
      debugPrint('Background sync completed successfully');
    } catch (e) {
      debugPrint('Background sync failed: $e');
    } finally {
      _isSyncing = false;
    }
  }

  void dispose() {
    _stopPeriodicSync();
    _connectivityService.removeListener(_onConnectivityChanged);
  }
}
