import 'package:flutter/material.dart';
import '../models/lesson_group.dart';
import '../utils/app_colors.dart';

class LessonGroupCard extends StatelessWidget {
  final LessonGroup lessonGroup;
  final int levelId;
  final int cycleId;
  final bool isLocked;
  final bool canAccess;
  final VoidCallback? onTap;
  final String? additionalInfo; // معلومات إضافية لعرضها في البطاقة

  const LessonGroupCard({
    Key? key,
    required this.lessonGroup,
    required this.levelId,
    required this.cycleId,
    this.isLocked = false,
    this.canAccess = true,
    this.onTap,
    this.additionalInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getBorderColor(),
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: isLocked || !canAccess ? null : (onTap ?? _navigateToLesson),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildTypeIcon(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lessonGroup.title,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isLocked ? Colors.grey : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          lessonGroup.type.arabicName,
                          style: TextStyle(
                            fontSize: 14,
                            color: isLocked ? Colors.grey : Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusIcon(),
                ],
              ),
              const SizedBox(height: 16),
              _buildProgressSection(),
              if (!isLocked && lessonGroup.completedSentences > 0) ...[
                const SizedBox(height: 12),
                _buildAccuracySection(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeIcon() {
    IconData iconData;
    Color iconColor;

    switch (lessonGroup.type) {
      case LessonType.sentenceBatch:
        iconData = Icons.format_list_bulleted;
        iconColor = isLocked ? Colors.grey : AppColors.getLevelColor(levelId);
        break;
      case LessonType.conversation:
        iconData = Icons.chat_bubble_outline;
        iconColor = isLocked ? Colors.grey : AppColors.getLevelColor(levelId);
        break;
      case LessonType.review:
        iconData = Icons.refresh;
        iconColor = isLocked ? Colors.grey : AppColors.getLevelColor(levelId);
        break;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: isLocked ? Colors.grey.shade200 : iconColor.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color:
                isLocked ? Colors.grey.withAlpha(30) : iconColor.withAlpha(50),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Icon(
        iconData,
        color: isLocked ? Colors.grey : iconColor,
        size: 28,
      ),
    );
  }

  Widget _buildStatusIcon() {
    if (isLocked) {
      return const Icon(Icons.lock, color: Colors.grey);
    }

    if (!canAccess) {
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Colors.orange.withAlpha(25), // 0.1 * 255 = 25
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.lock_open,
          color: Colors.orange,
          size: 24,
        ),
      );
    }

    if (lessonGroup.isCompleted) {
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Colors.green.withAlpha(25), // 0.1 * 255 = 25
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 24,
        ),
      );
    }

    if (lessonGroup.completedSentences > 0) {
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Colors.orange.withAlpha(25), // 0.1 * 255 = 25
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.play_circle_filled,
          color: Colors.orange,
          size: 24,
        ),
      );
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppColors.getLevelColor(levelId).withAlpha(25), // 0.1 * 255 = 25
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.arrow_forward,
        color: AppColors.getLevelColor(levelId),
        size: 24,
      ),
    );
  }

  Widget _buildProgressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقدم:',
              style: TextStyle(
                fontSize: 14,
                color: isLocked ? Colors.grey : Colors.black54,
              ),
            ),
            Text(
              '${lessonGroup.completedSentences}/${lessonGroup.totalSentences} جملة',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isLocked ? Colors.grey : Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: isLocked
              ? 0
              : lessonGroup.completedSentences / lessonGroup.totalSentences,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(
            lessonGroup.isCompleted
                ? Colors.green
                : AppColors.getLevelColor(levelId),
          ),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4),
        ),
        // عرض المعلومات الإضافية إذا كانت متوفرة
        if (additionalInfo != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade100),
            ),
            child: Text(
              additionalInfo!,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAccuracySection() {
    final Color accuracyColor = _getAccuracyColor();

    return Row(
      children: [
        Icon(
          Icons.mic,
          size: 16,
          color: accuracyColor,
        ),
        const SizedBox(width: 4),
        const Text(
          'دقة النطق:',
          style: TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: accuracyColor.withAlpha(25), // 0.1 * 255 = 25
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '${(lessonGroup.accuracy * 100).toInt()}%',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: accuracyColor,
            ),
          ),
        ),
      ],
    );
  }

  Color _getBorderColor() {
    if (isLocked) {
      return Colors.grey.shade300;
    }

    if (!canAccess) {
      return Colors.orange.shade300;
    }

    if (lessonGroup.isCompleted) {
      return Colors.green;
    }

    if (lessonGroup.completedSentences > 0) {
      return Colors.orange;
    }

    return AppColors.getLevelColor(levelId);
  }

  Color _getAccuracyColor() {
    if (lessonGroup.accuracy >= 0.8) {
      return Colors.green;
    } else if (lessonGroup.accuracy >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  void _navigateToLesson() {
    if (lessonGroup.routePath != null && onTap != null) {
      onTap!();
    }
  }
}
