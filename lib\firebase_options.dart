// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBxmoYd44s1sGOOv9t6NzDQidR3ES8ybFU',
    appId: '1:296213866037:android:5750a92beb9f31c7dc337c',
    messagingSenderId: '296213866037',
    projectId: 'test-flutterfire-a6366',
    storageBucket: 'test-flutterfire-a6366.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCIyhtcQeMd3Up2pIwK0KPqB9vVVT-GVSU',
    appId: '1:296213866037:ios:2ee1fb20a70d649edc337c',
    messagingSenderId: '296213866037',
    projectId: 'test-flutterfire-a6366',
    storageBucket: 'test-flutterfire-a6366.firebasestorage.app',
    iosBundleId: 'com.example.test05',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBxmoYd44s1sGOOv9t6NzDQidR3ES8ybFU',
    appId: '1:296213866037:android:5750a92beb9f31c7dc337c',
    messagingSenderId: '296213866037',
    projectId: 'test-flutterfire-a6366',
    storageBucket: 'test-flutterfire-a6366.firebasestorage.app',
  );
}
