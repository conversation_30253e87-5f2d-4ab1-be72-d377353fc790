import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../viewmodels/auth_view_model.dart';
import 'points_settings_screen.dart';
import 'levels_settings_screen.dart';

/// شاشة لوحة تحكم المسؤول
class AdminDashboardScreen extends StatelessWidget {
  const AdminDashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authViewModel = Provider.of<AuthViewModel>(context);

    // التحقق من أن المستخدم مسؤول
    if (!authViewModel.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('لوحة تحكم المسؤول')),
        body: const Center(
          child: Text('غير مصرح لك بالوصول إلى هذه الصفحة'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم المسؤول'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إعدادات التطبيق',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              _buildSettingCard(
                context,
                'إعدادات النقاط',
                'تعديل قيم النقاط لمختلف الأنشطة في التطبيق',
                Icons.stars,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PointsSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingCard(
                context,
                'إعدادات المستويات',
                'تعديل عدد الجمل والنقاط المطلوبة لكل مستوى',
                Icons.layers,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LevelsSettingsScreen(),
                  ),
                ),
              ),
              _buildSettingCard(
                context,
                'إدارة المستخدمين',
                'عرض وإدارة حسابات المستخدمين',
                Icons.people,
                () {
                  // سيتم تنفيذ هذه الميزة لاحقًا
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('هذه الميزة قيد التطوير')),
                  );
                },
              ),
              _buildSettingCard(
                context,
                'إحصائيات التطبيق',
                'عرض إحصائيات استخدام التطبيق',
                Icons.bar_chart,
                () {
                  // سيتم تنفيذ هذه الميزة لاحقًا
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('هذه الميزة قيد التطوير')),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء بطاقة إعداد
  Widget _buildSettingCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 20.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).primaryColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
