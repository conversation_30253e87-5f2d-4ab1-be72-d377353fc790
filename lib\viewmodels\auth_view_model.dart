import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../utils/firebase_setup.dart';

class AuthViewModel extends ChangeNotifier {
  late FirebaseAuth _auth;
  late FirebaseFirestore _firestore;
  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  AuthViewModel() {
    _initAsync();
  }

  Future<void> _initAsync() async {
    try {
      // تأكد من أن Firebase تم تهيئته بالفعل
      if (Firebase.apps.isEmpty) {
        debugPrint('تحذير: Firebase لم يتم تهيئته بعد في AuthViewModel');
        await Future.delayed(const Duration(milliseconds: 500));
        await _initAsync();
        return;
      }

      // تهيئة Firebase Auth و Firestore
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _isInitialized = true;

      // ملاحظة: setPersistence غير مدعوم على المنصات غير الويب
      // نستخدم SharedPreferences بدلاً من ذلك للحفاظ على حالة تسجيل الدخول
      debugPrint(
          'تم تهيئة Firebase Auth - استخدام SharedPreferences للاستمرارية');

      // محاولة استعادة حالة تسجيل الدخول من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      final savedEmail = prefs.getString('user_email');
      final savedUid = prefs.getString('user_uid');
      final savedPassword = prefs.getString('user_password');

      debugPrint(
          'محاولة استعادة حالة تسجيل الدخول: البريد=$savedEmail، المعرف=$savedUid');

      // تهيئة بيانات المستخدم
      _user = _auth.currentUser;
      debugPrint('المستخدم الحالي: ${_user?.uid}');

      // إذا كان المستخدم غير مسجل الدخول ولكن لدينا بيانات محفوظة، نحاول إعادة تسجيل الدخول تلقائيًا
      if (_user == null &&
          savedUid != null &&
          savedEmail != null &&
          savedPassword != null) {
        debugPrint('محاولة إعادة تسجيل الدخول تلقائيًا للمستخدم: $savedEmail');

        try {
          // محاولة إعادة تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور المحفوظة
          final userCredential = await _auth.signInWithEmailAndPassword(
            email: savedEmail,
            password: savedPassword,
          );

          _user = userCredential.user;

          if (_user != null) {
            debugPrint('تم إعادة تسجيل الدخول تلقائيًا: ${_user!.uid}');
            await _loadUserData(_user!.uid);
          } else {
            debugPrint('فشل في إعادة تسجيل الدخول تلقائيًا');
          }
        } catch (e) {
          debugPrint('خطأ في محاولة إعادة تسجيل الدخول تلقائيًا: $e');

          // محاولة استعادة الجلسة من Firebase
          try {
            await _auth.authStateChanges().first;
            _user = _auth.currentUser;

            if (_user != null) {
              debugPrint('تم استعادة جلسة المستخدم: ${_user!.uid}');
              await _loadUserData(_user!.uid);
            } else {
              debugPrint('فشل في استعادة جلسة المستخدم، سيتم طلب تسجيل الدخول');
            }
          } catch (e2) {
            debugPrint('خطأ في محاولة استعادة جلسة المستخدم: $e2');
          }
        }
      } else if (_user != null) {
        // إذا كان المستخدم مسجل الدخول بالفعل، تحميل بياناته
        debugPrint('المستخدم مسجل الدخول بالفعل: ${_user!.uid}');
        await _loadUserData(_user!.uid);

        // تحديث بيانات المستخدم في التخزين المحلي
        await prefs.setString('user_uid', _user!.uid);
        if (_user!.email != null) {
          await prefs.setString('user_email', _user!.email!);
        }
      }

      // إعداد مستمع لتغييرات حالة المصادقة
      _auth.authStateChanges().listen((user) {
        debugPrint('تغيير في حالة المصادقة: ${user?.uid}');
        if (_user?.uid != user?.uid) {
          _user = user;
          if (user != null) {
            // حفظ معرف المستخدم في التخزين المحلي
            SharedPreferences.getInstance().then((prefs) {
              prefs.setString('user_uid', user.uid);
              if (user.email != null) {
                prefs.setString('user_email', user.email!);
              }
              debugPrint('تم حفظ بيانات المستخدم في التخزين المحلي');
            });
            _loadUserData(user.uid);
          } else {
            // مسح بيانات المستخدم من التخزين المحلي عند تسجيل الخروج
            SharedPreferences.getInstance().then((prefs) {
              prefs.remove('user_uid');
              prefs.remove('user_email');
              prefs.remove('user_password');
              debugPrint('تم مسح بيانات المستخدم من التخزين المحلي');
            });
            _userModel = null;
          }
          notifyListeners();
        }
      });

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة AuthViewModel: $e');
      // إعادة المحاولة بعد فترة قصيرة
      await Future.delayed(const Duration(milliseconds: 500));
      await _initAsync();
    }
  }

  Future<void> _loadUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists && doc.data() != null) {
        final userData = doc.data()!;
        // Asegurarse de que el ID esté incluido en los datos
        userData['id'] = uid;
        _userModel = UserModel.fromMap(userData);
      } else {
        // Si no existe el documento, crear uno nuevo con datos básicos
        _userModel = UserModel(
          id: uid,
          email: _user?.email,
          displayName: _user?.displayName,
          photoURL: _user?.photoURL,
          typeUser: 'user', // Por defecto, asignar como usuario normal
        );
        // Guardar el modelo de usuario en Firestore
        await _firestore.collection('users').doc(uid).set(_userModel!.toMap());
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error al cargar datos de usuario: $e');
    }
  }

  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isAuthenticated => _user != null;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Getters para los datos extendidos del usuario
  String? get gender => _userModel?.gender;
  DateTime? get birthDate => _userModel?.birthDate;
  String? get country => _userModel?.country;

  // Método para verificar si el usuario es administrador
  bool get isAdmin => _userModel?.typeUser == 'adm';

  // Método para forzar la actualización de datos del usuario
  Future<void> forceRefreshUserData() async {
    if (_user != null) {
      await _loadUserData(_user!.uid);
      notifyListeners();
    }
  }

  Future<bool> signIn(String email, String password) async {
    if (_isLoading) {
      return false; // Prevent concurrent sign-in attempts
    }

    // التأكد من اكتمال التهيئة
    if (!_isInitialized) {
      _setError('جاري تهيئة التطبيق، يرجى المحاولة مرة أخرى بعد قليل');
      return false;
    }

    try {
      _setLoading(true);
      _clearError();

      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = userCredential.user;

      // حفظ بيانات المستخدم في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_email', email);
      await prefs.setString(
          'user_password', password); // حفظ كلمة المرور للتسجيل التلقائي
      if (_user != null) {
        await prefs.setString('user_uid', _user!.uid);
      }

      debugPrint('تم حفظ بيانات المستخدم في التخزين المحلي بعد تسجيل الدخول');

      // تحميل بيانات المستخدم من Firestore
      if (_user != null) {
        await _loadUserData(_user!.uid);

        // إعداد تقدم المستخدم في Firebase (مسار التعلم والنقاط)
        // تعليق: هذا سيضمن أن مسار التعلم يتم إنشاؤه تلقائيًا عند تسجيل الدخول
        await _setupUserLearningPath();
      }

      notifyListeners();

      // التحقق مما إذا كان هذا مستخدم جديد يحتاج إلى بيانات أولية
      final hasInitialData =
          prefs.getBool('has_initial_data_${_user!.uid}') ?? false;
      return !hasInitialData; // إرجاع true إذا كان يحتاج إلى بيانات أولية
    } on FirebaseAuthException catch (e) {
      // التأكد من أن المستخدم غير مسجل الدخول عند حدوث خطأ
      _user = null;
      _handleAuthError(e);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إعداد مسار التعلم للمستخدم
  Future<void> _setupUserLearningPath() async {
    try {
      // استدعاء دالة إعداد تقدم المستخدم من FirebaseSetup
      // تعليق: هذا سيقوم بإنشاء مسار التعلم والنقاط للمستخدم في Firestore
      await FirebaseSetup.setupUserProgress();

      // استدعاء دالة إعداد المستويات إذا لم تكن موجودة
      await FirebaseSetup.setupLevelsData();

      // استدعاء دالة إعداد إعدادات النقاط إذا لم تكن موجودة
      await FirebaseSetup.setupPointsSettings();

      debugPrint('تم إعداد مسار التعلم للمستخدم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعداد مسار التعلم للمستخدم: $e');
    }
  }

  Future<void> signUp(String email, String password, {String? name}) async {
    // التأكد من اكتمال التهيئة
    if (!_isInitialized) {
      _setError('جاري تهيئة التطبيق، يرجى المحاولة مرة أخرى بعد قليل');
      return;
    }

    try {
      _setLoading(true);
      _clearError();

      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = userCredential.user;

      // تحديث اسم المستخدم إذا تم توفيره
      if (name != null && name.isNotEmpty) {
        await _user!.updateDisplayName(name);
        // تحديث المستخدم في الذاكرة
        _user = _auth.currentUser;
      }

      // إنشاء وثيقة المستخدم في Firestore
      _userModel = UserModel(
        id: _user!.uid,
        email: email,
        displayName: name ?? _user?.displayName,
        photoURL: _user?.photoURL,
        typeUser: 'user', // Por defecto, asignar como usuario normal
      );

      // حفظ بيانات المستخدم في Firestore
      await _firestore
          .collection('users')
          .doc(_user!.uid)
          .set(_userModel!.toMap());

      // حفظ بيانات المستخدم في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_email', email);
      await prefs.setString(
          'user_password', password); // حفظ كلمة المرور للتسجيل التلقائي
      if (_user != null) {
        await prefs.setString('user_uid', _user!.uid);
      }

      debugPrint('تم حفظ بيانات المستخدم في التخزين المحلي بعد التسجيل');

      notifyListeners();
    } on FirebaseAuthException catch (e) {
      _handleAuthError(e);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> resetPassword(String email) async {
    // التأكد من اكتمال التهيئة
    if (!_isInitialized) {
      _setError('جاري تهيئة التطبيق، يرجى المحاولة مرة أخرى بعد قليل');
      return;
    }

    try {
      _setLoading(true);
      _clearError();

      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      _handleAuthError(e);
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      _clearError();

      // مسح بيانات المستخدم من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_uid');
      await prefs.remove('user_email');
      await prefs.remove('user_password'); // مسح كلمة المرور أيضًا
      debugPrint('تم مسح بيانات المستخدم من التخزين المحلي');

      await _auth.signOut();
      _user = null;
      _userModel = null;
      notifyListeners();

      debugPrint('تم تسجيل الخروج بنجاح');
    } catch (e) {
      debugPrint('خطأ أثناء تسجيل الخروج: $e');
      _setError('حدث خطأ أثناء تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  void _handleAuthError(FirebaseAuthException e) {
    String message;
    switch (e.code) {
      case 'user-not-found':
        message = 'لم يتم العثور على حساب بهذا البريد الإلكتروني';
        break;
      case 'wrong-password':
        message = 'كلمة المرور غير صحيحة';
        break;
      case 'email-already-in-use':
        message = 'البريد الإلكتروني مستخدم بالفعل';
        break;
      case 'weak-password':
        message = 'كلمة المرور ضعيفة جداً';
        break;
      case 'invalid-email':
        message = 'البريد الإلكتروني غير صالح';
        break;
      case 'operation-not-allowed':
        message = 'تسجيل الدخول بالبريد الإلكتروني غير مفعل';
        break;
      case 'too-many-requests':
        message = 'تم تجاوز عدد المحاولات المسموح بها، يرجى المحاولة لاحقاً';
        break;
      case 'user-disabled':
        message = 'تم تعطيل هذا الحساب، يرجى التواصل مع الدعم الفني';
        break;
      case 'network-request-failed':
        message = 'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';
        break;
      case 'requires-recent-login':
        message = 'يرجى تسجيل الخروج وإعادة تسجيل الدخول ثم المحاولة مرة أخرى';
        break;
      default:
        message = 'حدث خطأ أثناء تسجيل الدخول، يرجى المحاولة مرة أخرى';
    }
    _setError(message);
  }

  Future<String?> getLastUsedEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_used_email');
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String? value) {
    _error = value;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // دالة لتحديث بيانات المستخدم من Firebase
  Future<bool> refreshUserData() async {
    try {
      // التحقق من وجود مستخدم حالي
      if (_user != null) {
        // تحديث بيانات المستخدم من Firebase Auth
        await _user!.reload();
        _user = _auth.currentUser;

        // تحديث بيانات المستخدم من Firestore
        await _loadUserData(_user!.uid);

        notifyListeners();
        return true;
      } else {
        // محاولة استعادة حالة تسجيل الدخول من التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        final savedUid = prefs.getString('user_uid');
        final savedEmail = prefs.getString('user_email');

        if (savedUid != null && savedEmail != null) {
          debugPrint(
              'محاولة استعادة حالة تسجيل الدخول من التخزين المحلي: $savedEmail');

          // محاولة استعادة الجلسة من Firebase
          await _auth.authStateChanges().first;
          _user = _auth.currentUser;

          if (_user != null) {
            debugPrint('تم استعادة جلسة المستخدم: ${_user!.uid}');

            // تحديث بيانات المستخدم من Firestore
            await _loadUserData(_user!.uid);

            notifyListeners();
            return true;
          } else {
            debugPrint('فشل في استعادة جلسة المستخدم، سيتم طلب تسجيل الدخول');
          }
        }

        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }

  Future<bool> updateUserProfile({
    required String displayName,
    String? gender,
    DateTime? birthDate,
    required String country,
    String? photoURL,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      if (_user == null) {
        _setError('يرجى تسجيل الدخول أولاً');
        return false;
      }

      // Actualizar el perfil en Firebase Auth
      await _user!.updateDisplayName(displayName);
      if (photoURL != null) {
        await _user!.updatePhotoURL(photoURL);
        // تحديث الصورة في الذاكرة المؤقتة
        _user = _auth.currentUser;
      }

      // Actualizar el modelo de usuario
      _userModel = _userModel?.copyWith(
        displayName: displayName,
        gender: gender,
        birthDate: birthDate,
        country: country,
        photoURL: photoURL ?? _userModel?.photoURL,
      );

      // Guardar en Firestore
      await _firestore.collection('users').doc(_user!.uid).update({
        'displayName': displayName,
        'gender': gender,
        'birthDate': birthDate?.toIso8601String(),
        'country': country,
        if (photoURL != null) 'photoURL': photoURL,
      });

      notifyListeners();
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث الملف الشخصي، يرجى المحاولة مرة أخرى');
      return false;
    } finally {
      _setLoading(false);
    }
  }
}
