import 'package:hive/hive.dart';

part 'hive_sentence_model.g.dart';

/// نموذج الجملة المخزنة في Hive
@HiveType(typeId: 1)
class HiveSentenceModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String arabicText;

  @HiveField(2)
  final String englishText;

  @HiveField(3)
  final String category;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final String? audioUrl;

  @HiveField(6)
  final String? difficulty;

  @HiveField(7)
  bool isReadByCurrentUser;

  @HiveField(8)
  bool isFavoriteByCurrentUser;

  @HiveField(9)
  DateTime? lastModified;

  HiveSentenceModel({
    required this.id,
    required this.arabicText,
    required this.englishText,
    required this.category,
    required this.createdAt,
    this.audioUrl,
    this.difficulty,
    this.isReadByCurrentUser = false,
    this.isFavoriteByCurrentUser = false,
    this.lastModified,
  });

  // تحويل من نموذج SentenceModel إلى HiveSentenceModel
  factory HiveSentenceModel.fromSentenceModel(dynamic sentenceModel) {
    return HiveSentenceModel(
      id: sentenceModel.id,
      arabicText: sentenceModel.arabicText,
      englishText: sentenceModel.englishText,
      category: sentenceModel.category,
      createdAt: sentenceModel.createdAt,
      audioUrl: sentenceModel.audioUrl,
      difficulty: sentenceModel.difficulty,
      isReadByCurrentUser: sentenceModel.isReadByCurrentUser,
      isFavoriteByCurrentUser: sentenceModel.isFavoriteByCurrentUser,
      lastModified: DateTime.now(),
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'arabicText': arabicText,
      'englishText': englishText,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'audioUrl': audioUrl,
      'difficulty': difficulty,
      'isReadByCurrentUser': isReadByCurrentUser,
      'isFavoriteByCurrentUser': isFavoriteByCurrentUser,
      'lastModified': lastModified?.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  HiveSentenceModel copyWith({
    String? arabicText,
    String? englishText,
    String? category,
    DateTime? createdAt,
    String? audioUrl,
    String? difficulty,
    bool? isReadByCurrentUser,
    bool? isFavoriteByCurrentUser,
    DateTime? lastModified,
  }) {
    return HiveSentenceModel(
      id: id,
      arabicText: arabicText ?? this.arabicText,
      englishText: englishText ?? this.englishText,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      audioUrl: audioUrl ?? this.audioUrl,
      difficulty: difficulty ?? this.difficulty,
      isReadByCurrentUser: isReadByCurrentUser ?? this.isReadByCurrentUser,
      isFavoriteByCurrentUser:
          isFavoriteByCurrentUser ?? this.isFavoriteByCurrentUser,
      lastModified: lastModified ?? DateTime.now(),
    );
  }

  // تعيين الجملة كمقروءة
  void markAsRead() {
    isReadByCurrentUser = true;
    lastModified = DateTime.now();
    save();
  }

  // تبديل حالة المفضلة
  void toggleFavorite() {
    isFavoriteByCurrentUser = !isFavoriteByCurrentUser;
    lastModified = DateTime.now();
    save();
  }
}
