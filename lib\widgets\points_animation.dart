import 'dart:math';
import 'package:flutter/material.dart';
import '../models/points.dart';
import '../utils/app_colors.dart';

/// مكون لعرض تأثير بصري عند كسب النقاط
class PointsAnimation extends StatefulWidget {
  final int points;
  final PointType type;
  final VoidCallback? onComplete;

  const PointsAnimation({
    Key? key,
    required this.points,
    required this.type,
    this.onComplete,
  }) : super(key: key);

  @override
  State<PointsAnimation> createState() => _PointsAnimationState();
}

class _PointsAnimationState extends State<PointsAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  final List<_CoinParticle> _particles = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
      ),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
    );

    // إنشاء جسيمات العملات
    _createParticles();

    // بدء الرسوم المتحركة
    _controller.forward().then((_) {
      if (widget.onComplete != null) {
        widget.onComplete!();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _createParticles() {
    final int count = min(widget.points, 20); // الحد الأقصى 20 جسيم
    
    for (int i = 0; i < count; i++) {
      final double angle = _random.nextDouble() * 2 * pi;
      final double distance = _random.nextDouble() * 100 + 50;
      final double delay = _random.nextDouble() * 0.5;
      
      _particles.add(_CoinParticle(
        angle: angle,
        distance: distance,
        delay: delay,
        size: _random.nextDouble() * 10 + 15,
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          children: [
            // جسيمات العملات
            ..._particles.map((particle) {
              final double progress = (_controller.value - particle.delay).clamp(0.0, 1.0) / (1.0 - particle.delay);
              if (progress <= 0) return const SizedBox();
              
              final double x = cos(particle.angle) * particle.distance * progress;
              final double y = sin(particle.angle) * particle.distance * progress - 50 * progress * progress; // إضافة تأثير الجاذبية
              
              return Positioned(
                left: MediaQuery.of(context).size.width / 2 + x - particle.size / 2,
                top: MediaQuery.of(context).size.height / 2 + y - particle.size / 2,
                child: Opacity(
                  opacity: 1.0 - progress,
                  child: Container(
                    width: particle.size,
                    height: particle.size,
                    decoration: BoxDecoration(
                      color: widget.type == PointType.educational
                          ? AppColors.educationalPointsColor
                          : AppColors.rewardPointsColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: widget.type == PointType.educational
                              ? AppColors.educationalPointsColor.withOpacity(0.5)
                              : AppColors.rewardPointsColor.withOpacity(0.5),
                          blurRadius: 5,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
            
            // نص النقاط
            Center(
              child: FadeTransition(
                opacity: _opacityAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: widget.type == PointType.educational
                          ? AppColors.educationalPointsColor.withOpacity(0.2)
                          : AppColors.rewardPointsColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: widget.type == PointType.educational
                            ? AppColors.educationalPointsColor
                            : AppColors.rewardPointsColor,
                        width: 2,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          widget.type == PointType.educational
                              ? Icons.school
                              : Icons.star,
                          color: widget.type == PointType.educational
                              ? AppColors.educationalPointsColor
                              : AppColors.rewardPointsColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '+${widget.points}',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: widget.type == PointType.educational
                                ? AppColors.educationalPointsColor
                                : AppColors.rewardPointsColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// فئة تمثل جسيم عملة في الرسوم المتحركة
class _CoinParticle {
  final double angle;
  final double distance;
  final double delay;
  final double size;

  _CoinParticle({
    required this.angle,
    required this.distance,
    required this.delay,
    required this.size,
  });
}
