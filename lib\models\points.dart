/// أنواع النقاط في التطبيق
enum PointType {
  educational, // نقاط تعليمية (للتقدم في المستويات)
  reward // نقاط مكافأة (للميزات الإضافية)
}

/// تحويل نوع النقاط من وإلى نص
extension PointTypeExtension on PointType {
  String toJson() {
    switch (this) {
      case PointType.educational:
        return 'educational';
      case PointType.reward:
        return 'reward';
      default:
        return 'educational'; // Default case to handle future enum values
    }
  }

  static PointType fromJson(String json) {
    switch (json) {
      case 'educational':
        return PointType.educational;
      case 'reward':
        return PointType.reward;
      default:
        throw ArgumentError('Invalid PointType: $json');
    }
  }

  String get arabicName {
    switch (this) {
      case PointType.educational:
        return 'تعليمية';
      case PointType.reward:
        return 'مكافأة';
      default:
        return 'تعليمية'; // Default case to handle future enum values
    }
  }
}

/// نموذج النقاط في التطبيق
class Points {
  final int educationalPoints; // النقاط التعليمية
  final int rewardPoints; // نقاط المكافأة
  final Map<String, int> pointsSettings; // إعدادات النقاط (قيمة كل نشاط)

  Points({
    required this.educationalPoints,
    required this.rewardPoints,
    required this.pointsSettings,
  });

  /// إنشاء نسخة من النقاط من بيانات JSON
  factory Points.fromJson(Map<String, dynamic> json) {
    return Points(
      educationalPoints: json['educationalPoints'] as int,
      rewardPoints: json['rewardPoints'] as int,
      pointsSettings: Map<String, int>.from(json['pointsSettings'] as Map),
    );
  }

  /// تحويل النقاط إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'educationalPoints': educationalPoints,
      'rewardPoints': rewardPoints,
      'pointsSettings': pointsSettings,
    };
  }

  /// إنشاء نسخة جديدة من النقاط مع تحديث بعض الخصائص
  Points copyWith({
    int? educationalPoints,
    int? rewardPoints,
    Map<String, int>? pointsSettings,
  }) {
    return Points(
      educationalPoints: educationalPoints ?? this.educationalPoints,
      rewardPoints: rewardPoints ?? this.rewardPoints,
      pointsSettings: pointsSettings ?? this.pointsSettings,
    );
  }

  @override
  String toString() {
    return 'Points{educationalPoints: $educationalPoints, rewardPoints: $rewardPoints, pointsSettings: $pointsSettings}';
  }
}

/// نموذج سجل النقاط (لتتبع النقاط المكتسبة)
class PointsRecord {
  final String id;
  final int points;
  final PointType type;
  final String activity; // النشاط الذي تم اكتساب النقاط منه
  final DateTime timestamp;

  PointsRecord({
    required this.id,
    required this.points,
    required this.type,
    required this.activity,
    required this.timestamp,
  });

  /// إنشاء نسخة من سجل النقاط من بيانات JSON
  factory PointsRecord.fromJson(Map<String, dynamic> json) {
    return PointsRecord(
      id: json['id'] as String,
      points: json['points'] as int,
      type: PointTypeExtension.fromJson(json['type'] as String),
      activity: json['activity'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// تحويل سجل النقاط إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'points': points,
      'type': type.toJson(),
      'activity': activity,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PointsRecord{id: $id, points: $points, type: $type, activity: $activity, timestamp: $timestamp}';
  }
}
