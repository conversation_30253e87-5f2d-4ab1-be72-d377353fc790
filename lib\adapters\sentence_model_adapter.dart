import '../models/sentence_model.dart';
import '../models/hive/hive_sentence_model.dart';

/// محول بين نموذج الجملة القديم والجديد
class SentenceModelAdapter {
  /// تحويل من نموذج SentenceModel إلى HiveSentenceModel
  static HiveSentenceModel toHiveModel(SentenceModel model) {
    return HiveSentenceModel(
      id: model.id,
      arabicText: model.arabicText,
      englishText: model.englishText,
      category: model.category,
      createdAt: model.createdAt,
      audioUrl: model.audioUrl,
      difficulty: model.difficulty,
      isReadByCurrentUser: model.isReadByCurrentUser,
      isFavoriteByCurrentUser: model.isFavoriteByCurrentUser,
      lastModified: DateTime.now(),
    );
  }

  /// تحويل من نموذج HiveSentenceModel إلى SentenceModel
  static SentenceModel fromHiveModel(HiveSentenceModel model) {
    return SentenceModel(
      id: model.id,
      arabicText: model.arabicText,
      englishText: model.englishText,
      category: model.category,
      createdAt: model.createdAt,
      readBy: {}, // لا نستخدم هذا الحقل بعد الآن
      isFavorite: false, // لا نستخدم هذا الحقل بعد الآن
      audioUrl: model.audioUrl,
      difficulty: model.difficulty,
      isReadByCurrentUser: model.isReadByCurrentUser,
      isFavoriteByCurrentUser: model.isFavoriteByCurrentUser,
    );
  }

  /// تحويل قائمة من نموذج SentenceModel إلى قائمة من HiveSentenceModel
  static List<HiveSentenceModel> toHiveModelList(List<SentenceModel> models) {
    return models.map((model) => toHiveModel(model)).toList();
  }

  /// تحويل قائمة من نموذج HiveSentenceModel إلى قائمة من SentenceModel
  static List<SentenceModel> fromHiveModelList(List<HiveSentenceModel> models) {
    return models.map((model) => fromHiveModel(model)).toList();
  }
}
