rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // التحكم في بيانات الأعضاء
    match /users/{userId} {
      allow read: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");

      allow update: if request.auth != null && request.auth.uid == userId && 
        !(request.resource.data.keys().hasAny(['typeUser', 'isDisabled']) && request.resource.data.typeUser == "adm");

      allow update: if request.auth != null && request.auth.uid == userId && request.resource.data.typeUser != "adm" && !request.resource.data.isDisabled;

                
      allow delete: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
  

      allow create: if true;

      // التحكم في الجمل المقروءة والمفضلة
      match /readSentences/{sentenceId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }

      match /favorites/{sentenceId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }

      match /dailySentences/{dailySentenceId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }

      match /readingLog/{logId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }

      match /stats/{statId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }
      
      // التحكم في تقدم المستخدم
      match /progress/{progressId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }
      
      // التحكم في نقاط المستخدم
      match /points/{pointsId} {
        allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
        
        match /history/{historyId} {
          allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
          
          match /records/{recordId} {
            allow read, write: if request.auth != null && (request.auth.uid == userId || get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
          }
        }
      }
    }

    // التحكم في إعدادات الإشعارات
    match /notificationSettings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read, write: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
    }

    // التحكم في الجمل
    match /sentences/{sentenceId} {
      allow read: if request.auth != null;
      allow create, update, delete: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
    }

    // التحكم في الفئات
    match /categories/{categoryId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
    }

    // التحكم في المحادثات
    match /conversations/{conversationId} {
      // السماح بالقراءة لجميع المستخدمين المسجلين
      allow read: if request.auth != null;
      
      // السماح مؤقتًا بالإنشاء والتعديل والحذف لجميع المستخدمين المسجلين
      // سيتم تغييرها لاحقًا لتكون للمشرفين فقط
      allow create, update, delete: if request.auth != null;
      
      // عندما نريد تقييد الإنشاء للمشرفين فقط، نستخدم هذه القاعدة بدلاً من السابقة
      // allow create, update, delete: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
    }
    
    // السماح بقراءة المستويات لجميع المستخدمين المسجلين
    match /levels/{levelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
      
      // السماح بقراءة مجموعات الدروس
      match /lessonGroups/{groupId} {
        allow read: if request.auth != null;
        allow write: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
      }
    }
    
    // السماح بقراءة إعدادات النقاط لجميع المستخدمين المسجلين
    match /settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm";
    }
  }
}

service firebase.storage {
  match /b/{bucket}/o {
    match /user_images/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
