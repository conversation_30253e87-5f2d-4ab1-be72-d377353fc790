import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/sentence_model.dart';
import 'native_notification_service.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Future<List<SentenceModel>> getSentences({
    String? category,
    int? limit,
    String? lastDocumentId,
    bool onlyFavorites = false,
    String? userId,
  }) async {
    try {
      Query query = _firestore.collection('sentences');

      if (category != null) {
        query = query.where('category', isEqualTo: category);
      }

      if (onlyFavorites) {
        query = query.where('isFavorite', isEqualTo: true);
      }

      if (lastDocumentId != null) {
        DocumentSnapshot lastDoc =
            await _firestore.collection('sentences').doc(lastDocumentId).get();
        query = query.startAfterDocument(lastDoc);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      query = query.orderBy('createdAt', descending: true);

      QuerySnapshot snapshot = await query.get();

      return snapshot.docs.map((doc) {
        return SentenceModel.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error fetching sentences: $e');
      return [];
    }
  }

  // هذه الطريقة لم تعد تستخدم، نحن الآن نستخدم SentenceService.markSentenceAsRead
  Future<void> markSentenceAsRead(String sentenceId, String userId) async {
    try {
      // إضافة إلى مجموعة readSentences للمستخدم
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .doc(sentenceId)
          .set({
        'readAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error marking sentence as read: $e');
      rethrow;
    }
  }

  // هذه الطريقة لم تعد تستخدم، نحن الآن نستخدم SentenceService.toggleFavorite
  Future<void> toggleFavorite(String sentenceId, bool isFavorite) async {
    // لا ينبغي استخدام هذه الطريقة بعد الآن
    throw UnimplementedError(
        'This method is no longer used, we now use SentenceService.toggleFavorite');
  }

  Future<List<String>> getCategories() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection('categories').get();
      return snapshot.docs.map((doc) => doc['name'] as String).toList();
    } catch (e) {
      debugPrint('Error fetching categories: $e');
      return [];
    }
  }

  Future<List<SentenceModel>> searchSentences(String query) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('sentences')
          .where('text', isGreaterThanOrEqualTo: query)
          .where('text', isLessThan: '${query}z')
          .get();

      return snapshot.docs.map((doc) {
        return SentenceModel.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error searching sentences: $e');
      return [];
    }
  }

  Future<void> addSentence(SentenceModel sentence) async {
    try {
      await _firestore.collection('sentences').add(sentence.toMap());

      // Send notification about new sentences
      // Use both immediate and scheduled notifications to ensure it appears
      final notificationService = NativeNotificationService();
      await notificationService.showNewSentencesAddedNotification();

      // Add a small delay and send another notification to ensure it appears
      await Future.delayed(const Duration(milliseconds: 500));
      await notificationService.showNewSentencesAddedNotification();

      debugPrint('New sentence added and notifications sent');
    } catch (e) {
      debugPrint('Error adding sentence: $e');
      rethrow;
    }
  }

  Future<void> updateSentence(String id, SentenceModel sentence) async {
    try {
      await _firestore.collection('sentences').doc(id).update(sentence.toMap());
    } catch (e) {
      debugPrint('Error updating sentence: $e');
      rethrow;
    }
  }

  Future<void> deleteSentence(String id) async {
    try {
      await _firestore.collection('sentences').doc(id).delete();
    } catch (e) {
      debugPrint('Error deleting sentence: $e');
      rethrow;
    }
  }

  Future<List<SentenceModel>> getSentencesByCategory(String category) async {
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('sentences')
          .where('category', isEqualTo: category)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        return SentenceModel.fromMap(
          doc.data() as Map<String, dynamic>,
          doc.id,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error fetching sentences by category: $e');
      return [];
    }
  }

  Future<void> addCategory(String category) async {
    try {
      // Verificar si la categoría ya existe
      final snapshot = await _firestore
          .collection('categories')
          .where('name', isEqualTo: category)
          .get();

      if (snapshot.docs.isNotEmpty) {
        // La categoría ya existe
        return;
      }

      // Agregar la nueva categoría
      await _firestore.collection('categories').add({
        'name': category,
        'createdAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Category added: $category');
    } catch (e) {
      debugPrint('Error adding category: $e');
      rethrow;
    }
  }
}
