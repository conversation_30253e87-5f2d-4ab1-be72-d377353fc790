import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/material.dart';

class RewardedAdService {
  static final RewardedAdService _instance = RewardedAdService._internal();
  factory RewardedAdService() => _instance;
  RewardedAdService._internal();

  RewardedAd? _rewardedAd;
  bool _isLoading = false;

  final String adUnitId =
      'ca-app-pub-7432441079508576/8581709316'; // وحدة اختبار القراءة

  void loadAd({VoidCallback? onLoaded, Function(String)? onFailed}) {
    if (_isLoading) return;
    _isLoading = true;
    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isLoading = false;
          onLoaded?.call();
        },
        onAdFailedToLoad: (error) {
          _isLoading = false;
          onFailed?.call(error.message);
        },
      ),
    );
  }

  void showAd({
    required VoidCallback onRewarded,
    required VoidCallback onClosed,
    Function(String)? onFailed,
  }) {
    if (_rewardedAd == null) {
      onFailed?.call('Ad not loaded');
      return;
    }
    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _rewardedAd = null;
        onClosed();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _rewardedAd = null;
        onFailed?.call(error.message);
      },
    );
    _rewardedAd!.show(
      onUserEarnedReward: (ad, reward) {
        onRewarded();
      },
    );
  }

  bool get isAdLoaded => _rewardedAd != null;
}
