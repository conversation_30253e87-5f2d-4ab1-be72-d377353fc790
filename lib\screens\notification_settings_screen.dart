import 'package:flutter/material.dart';
import '../services/simple_notification_service.dart';
import '../services/native_notification_service.dart';
import '../theme/app_theme.dart';
import '../widgets/app_card.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  final SimpleNotificationService _notificationService =
      SimpleNotificationService();
  final NativeNotificationService _nativeNotificationService =
      NativeNotificationService();
  bool _isLoading = true;

  // Notification settings
  bool _unfinishedSentencesEnabled = true;
  bool _newSentencesAddedEnabled = true;
  bool _morningReminderEnabled = true;
  bool _newDayMotivationEnabled = true;

  TimeOfDay _unfinishedSentencesTime = const TimeOfDay(hour: 18, minute: 0);
  TimeOfDay _morningReminderTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _newDayMotivationTime = const TimeOfDay(hour: 8, minute: 0);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = await _notificationService.getNotificationSettings();

      setState(() {
        _unfinishedSentencesEnabled = settings['unfinishedSentencesEnabled'];
        _newSentencesAddedEnabled = settings['newSentencesAddedEnabled'];
        _morningReminderEnabled = settings['morningReminderEnabled'];
        _newDayMotivationEnabled = settings['newDayMotivationEnabled'];

        _unfinishedSentencesTime = _notificationService
            .parseTimeString(settings['unfinishedSentencesTime']);
        _morningReminderTime = _notificationService
            .parseTimeString(settings['morningReminderTime']);
        _newDayMotivationTime = _notificationService
            .parseTimeString(settings['newDayMotivationTime']);
      });
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحميل إعدادات الإشعارات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateSettings({bool showSnackbar = true}) async {
    if (showSnackbar) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      await _notificationService.updateNotificationSettings(
        unfinishedSentencesEnabled: _unfinishedSentencesEnabled,
        newSentencesAddedEnabled: _newSentencesAddedEnabled,
        morningReminderEnabled: _morningReminderEnabled,
        newDayMotivationEnabled: _newDayMotivationEnabled,
        unfinishedSentencesTime:
            _notificationService.formatTimeOfDay(_unfinishedSentencesTime),
        morningReminderTime:
            _notificationService.formatTimeOfDay(_morningReminderTime),
        newDayMotivationTime:
            _notificationService.formatTimeOfDay(_newDayMotivationTime),
      );

      // Schedule all notifications after updating settings
      await _notificationService.scheduleAllNotifications();

      if (mounted && showSnackbar) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ إعدادات الإشعارات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error updating notification settings: $e');
      if (mounted && showSnackbar) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'حدث خطأ أثناء حفظ إعدادات الإشعارات: ${e.toString().contains('MissingPluginException') ? 'يرجى إعادة تشغيل التطبيق' : ''}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted && showSnackbar) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectTime(BuildContext context, TimeOfDay initialTime,
      Function(TimeOfDay) onTimeSelected) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: Theme.of(context).cardColor,
              hourMinuteTextColor: Theme.of(context).textTheme.bodyLarge?.color,
              dayPeriodTextColor: Theme.of(context).textTheme.bodyLarge?.color,
              dialHandColor: AppTheme.primaryColor,
              dialBackgroundColor: AppTheme.primaryColor.withAlpha(25),
              hourMinuteColor: AppTheme.primaryColor.withAlpha(25),
              dayPeriodColor: AppTheme.primaryColor.withAlpha(25),
              entryModeIconColor: AppTheme.primaryColor,
            ),
            colorScheme: ColorScheme.light(
              primary: AppTheme.primaryColor,
              onSurface:
                  Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      onTimeSelected(pickedTime);
    }
  }

  // Method to send a test notification
  Future<void> _sendTestNotification() async {
    try {
      await _nativeNotificationService.showTestNotification();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال إشعار تجريبي'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error sending test notification: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إرسال الإشعار التجريبي: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen size for responsive design
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.height < 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        centerTitle: true,
        actions: [
          // Test notification button
          IconButton(
            icon: const Icon(Icons.notifications_active),
            tooltip: 'اختبار الإشعارات',
            onPressed: _sendTestNotification,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildInfoCard(),
                        SizedBox(height: isSmallScreen ? 4 : 8),
                        _buildUnfinishedSentencesCard(),
                        SizedBox(height: isSmallScreen ? 4 : 8),
                        _buildNewSentencesAddedCard(),
                        SizedBox(height: isSmallScreen ? 4 : 8),
                        _buildMorningReminderCard(),
                        SizedBox(height: isSmallScreen ? 4 : 8),
                        _buildNewDayMotivationCard(),
                        // Add extra space at the bottom for padding
                        SizedBox(height: isSmallScreen ? 8 : 16),
                      ],
                    ),
                  ),
                ),
                // Fixed save button at the bottom
                Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 8.0 : 16.0),
                  child: _buildSaveButton(),
                ),
              ],
            ),
    );
  }

  Widget _buildInfoCard() {
    return const AppCard(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'معلومات عن الإشعارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            Text(
              'يمكنك تخصيص إشعارات التطبيق حسب احتياجاتك. يمكنك تفعيل أو تعطيل كل نوع من الإشعارات، وتحديد الوقت المناسب لظهورها.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              'عند الضغط على "تذكيري لاحقاً" في الإشعار، سيظهر الإشعار مرة أخرى بعد 30 دقيقة.',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnfinishedSentencesCard() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Text(
                    'تذكير بالجمل غير المكتملة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Switch(
                      value: _unfinishedSentencesEnabled,
                      onChanged: (value) {
                        setState(() {
                          _unfinishedSentencesEnabled = value;
                        });
                        // Auto-save when toggled
                        _updateSettings(showSnackbar: false);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                    IconButton(
                      icon: const Icon(Icons.send, size: 20),
                      tooltip: 'اختبار هذا الإشعار',
                      onPressed: () async {
                        await _nativeNotificationService
                            .showUnfinishedSentencesNotification();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  'تم إرسال إشعار تجريبي للجمل غير المكتملة'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 1),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'تذكير يومي إذا كان لديك جمل لم تكملها بعد.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_unfinishedSentencesEnabled)
              InkWell(
                onTap: () {
                  _selectTime(context, _unfinishedSentencesTime, (time) {
                    setState(() {
                      _unfinishedSentencesTime = time;
                    });
                    // Auto-save when time is changed
                    _updateSettings(showSnackbar: false);
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('وقت الإشعار'),
                      Text(
                        '${_unfinishedSentencesTime.hour.toString().padLeft(2, '0')}:${_unfinishedSentencesTime.minute.toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewSentencesAddedCard() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Text(
                    'إشعار بإضافة جمل جديدة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Switch(
                      value: _newSentencesAddedEnabled,
                      onChanged: (value) {
                        setState(() {
                          _newSentencesAddedEnabled = value;
                        });
                        // Auto-save when toggled
                        _updateSettings(showSnackbar: false);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                    IconButton(
                      icon: const Icon(Icons.send, size: 20),
                      tooltip: 'اختبار هذا الإشعار',
                      onPressed: () async {
                        await _nativeNotificationService
                            .showNewSentencesAddedNotification();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text('تم إرسال إشعار تجريبي للجمل الجديدة'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 1),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'إشعار عند إضافة جمل جديدة إلى قاعدة البيانات.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMorningReminderCard() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Text(
                    'تذكير صباحي بالجمل غير المقروءة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Switch(
                      value: _morningReminderEnabled,
                      onChanged: (value) {
                        setState(() {
                          _morningReminderEnabled = value;
                        });
                        // Auto-save when toggled
                        _updateSettings(showSnackbar: false);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                    IconButton(
                      icon: const Icon(Icons.send, size: 20),
                      tooltip: 'اختبار هذا الإشعار',
                      onPressed: () async {
                        await _nativeNotificationService
                            .showMorningReminderNotification();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text('تم إرسال إشعار تجريبي للتذكير الصباحي'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 1),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'تذكير صباحي بالجمل المتبقية من اليوم السابق.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_morningReminderEnabled)
              InkWell(
                onTap: () {
                  _selectTime(context, _morningReminderTime, (time) {
                    setState(() {
                      _morningReminderTime = time;
                    });
                    // Auto-save when time is changed
                    _updateSettings(showSnackbar: false);
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('وقت الإشعار'),
                      Text(
                        '${_morningReminderTime.hour.toString().padLeft(2, '0')}:${_morningReminderTime.minute.toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewDayMotivationCard() {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Expanded(
                  child: Text(
                    'إشعار تحفيزي ليوم جديد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Switch(
                      value: _newDayMotivationEnabled,
                      onChanged: (value) {
                        setState(() {
                          _newDayMotivationEnabled = value;
                        });
                        // Auto-save when toggled
                        _updateSettings(showSnackbar: false);
                      },
                      activeColor: AppTheme.primaryColor,
                    ),
                    IconButton(
                      icon: const Icon(Icons.send, size: 20),
                      tooltip: 'اختبار هذا الإشعار',
                      onPressed: () async {
                        await _nativeNotificationService
                            .showNewDayMotivationNotification();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text('تم إرسال إشعار تجريبي للتحفيز اليومي'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 1),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'إشعار تحفيزي في بداية اليوم الجديد عند إكمال جميع الجمل السابقة.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            if (_newDayMotivationEnabled)
              InkWell(
                onTap: () {
                  _selectTime(context, _newDayMotivationTime, (time) {
                    setState(() {
                      _newDayMotivationTime = time;
                    });
                    // Auto-save when time is changed
                    _updateSettings(showSnackbar: false);
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('وقت الإشعار'),
                      Text(
                        '${_newDayMotivationTime.hour.toString().padLeft(2, '0')}:${_newDayMotivationTime.minute.toString().padLeft(2, '0')}',
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      child: ElevatedButton(
        onPressed: () => _updateSettings(showSnackbar: true),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.save, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'حفظ الإعدادات',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
      ),
    );
  }
}
