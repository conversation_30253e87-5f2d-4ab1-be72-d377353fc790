import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/sync_manager.dart';
import '../viewmodels/hive_sentence_view_model.dart';
import '../viewmodels/auth_view_model.dart';

/// شاشة إعدادات المزامنة
class SyncSettingsScreen extends StatefulWidget {
  const SyncSettingsScreen({Key? key}) : super(key: key);

  @override
  State<SyncSettingsScreen> createState() => _SyncSettingsScreenState();
}

class _SyncSettingsScreenState extends State<SyncSettingsScreen> {
  bool _isSyncing = false;
  int _pendingItems = 0;
  int _selectedSyncInterval = 3; // الفترة الافتراضية للمزامنة (3 أيام)
  bool _autoSyncEnabled = true;
  DateTime? _lastSyncAttempt;

  // Helper method to show snackbar safely
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _loadSyncSettings();

    // تحديث عدد العناصر المنتظرة للمزامنة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshPendingItemsCount();
    });
  }

  /// تحديث عدد العناصر المنتظرة للمزامنة
  void _refreshPendingItemsCount() {
    final syncManager = Provider.of<SyncManager>(context, listen: false);
    final pendingItems = syncManager.getPendingSyncItemsCount();

    if (mounted) {
      setState(() {
        _pendingItems = pendingItems;
      });
    }

    debugPrint('تم تحديث عدد العناصر المنتظرة للمزامنة: $_pendingItems');
  }

  /// تحميل إعدادات المزامنة
  void _loadSyncSettings() {
    final syncManager = Provider.of<SyncManager>(context, listen: false);
    setState(() {
      _isSyncing = syncManager.isSyncing;
      _pendingItems = syncManager.getPendingSyncItemsCount();
      _selectedSyncInterval = syncManager.syncIntervalDays;
      _autoSyncEnabled = syncManager.autoSyncEnabled;
      _lastSyncAttempt = syncManager.lastSyncAttempt;
    });

    debugPrint('تم تحميل إعدادات المزامنة: العناصر المنتظرة = $_pendingItems');

    // إضافة مستمع للتغييرات
    syncManager.addListener(_onSyncStateChanged);
  }

  /// استجابة لتغيير حالة المزامنة
  void _onSyncStateChanged() {
    final syncManager = Provider.of<SyncManager>(context, listen: false);
    final pendingItems = syncManager.getPendingSyncItemsCount();

    setState(() {
      _isSyncing = syncManager.isSyncing;
      _pendingItems = pendingItems;
      _lastSyncAttempt = syncManager.lastSyncAttempt;
    });

    debugPrint('تم تحديث حالة المزامنة: العناصر المنتظرة = $_pendingItems');
  }

  @override
  void dispose() {
    // إزالة المستمع عند التخلص من الشاشة
    Provider.of<SyncManager>(context, listen: false)
        .removeListener(_onSyncStateChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final syncManager = Provider.of<SyncManager>(context, listen: false);
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    final hiveSentenceViewModel =
        Provider.of<HiveSentenceViewModel>(context, listen: false);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المزامنة'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة حالة المزامنة
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة المزامنة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('العناصر المنتظرة للمزامنة:'),
                        Text(
                          '$_pendingItems',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('آخر محاولة مزامنة:'),
                        Text(
                          _lastSyncAttempt != null
                              ? '${_lastSyncAttempt!.day}/${_lastSyncAttempt!.month}/${_lastSyncAttempt!.year} ${_lastSyncAttempt!.hour}:${_lastSyncAttempt!.minute}'
                              : 'لم تتم المزامنة بعد',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSyncing
                            ? null
                            : () async {
                                if (authViewModel.user != null) {
                                  final result =
                                      await syncManager.syncWithFirebase(
                                          authViewModel.user!.uid);
                                  if (result) {
                                    // تحديث البيانات بعد المزامنة
                                    await hiveSentenceViewModel
                                        .loadStatistics();

                                    // تحديث عدد العناصر المنتظرة للمزامنة
                                    _refreshPendingItemsCount();

                                    _showSnackBar('تمت المزامنة بنجاح');
                                  } else {
                                    _showSnackBar('فشلت المزامنة');

                                    // تحديث عدد العناصر المنتظرة للمزامنة حتى في حالة الفشل
                                    _refreshPendingItemsCount();
                                  }
                                }
                              },
                        child: _isSyncing
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('جاري المزامنة...'),
                                ],
                              )
                            : const Text('مزامنة الآن'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // بطاقة إعدادات المزامنة التلقائية
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات المزامنة التلقائية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('تمكين المزامنة التلقائية'),
                      value: _autoSyncEnabled,
                      onChanged: (value) {
                        setState(() {
                          _autoSyncEnabled = value;
                        });
                        syncManager.setAutoSyncEnabled(value);
                      },
                    ),
                    const SizedBox(height: 8),
                    const Text('فترة المزامنة التلقائية:'),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _selectedSyncInterval,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 1,
                          child: Text('كل يوم'),
                        ),
                        DropdownMenuItem(
                          value: 3,
                          child: Text('كل 3 أيام'),
                        ),
                        DropdownMenuItem(
                          value: 7,
                          child: Text('كل أسبوع'),
                        ),
                        DropdownMenuItem(
                          value: 14,
                          child: Text('كل أسبوعين'),
                        ),
                        DropdownMenuItem(
                          value: 30,
                          child: Text('كل شهر'),
                        ),
                      ],
                      onChanged: _autoSyncEnabled
                          ? (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedSyncInterval = value;
                                });
                                syncManager.setSyncInterval(value);
                              }
                            }
                          : null,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // بطاقة استعادة البيانات
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'استعادة البيانات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'استخدم هذا الخيار لاستعادة بياناتك من Firebase بعد إعادة تثبيت التطبيق أو تغيير الجهاز.',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSyncing
                            ? null
                            : () async {
                                if (authViewModel.user != null) {
                                  // عرض مربع حوار للتأكيد
                                  final confirm = await showDialog<bool>(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('استعادة البيانات'),
                                      content: const Text(
                                          'هل أنت متأكد من رغبتك في استعادة البيانات من Firebase؟ قد يؤدي ذلك إلى استبدال البيانات الحالية.'),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.of(context).pop(false),
                                          child: const Text('إلغاء'),
                                        ),
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.of(context).pop(true),
                                          child: const Text('استعادة'),
                                        ),
                                      ],
                                    ),
                                  );

                                  if (confirm == true) {
                                    final result = await hiveSentenceViewModel
                                        .restoreDataFromFirebase(
                                            authViewModel.user!.uid);
                                    if (result) {
                                      _showSnackBar(
                                          'تمت استعادة البيانات بنجاح');
                                    } else {
                                      _showSnackBar('فشلت استعادة البيانات');
                                    }
                                  }
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                        ),
                        child: const Text('استعادة البيانات'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
