import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../constants/hive_constants.dart';
import '../models/hive/hive_sentence_model.dart';
import '../models/hive/sync_log_model.dart';
import '../models/hive/statistics_model.dart';
import '../models/hive/category_model.dart';
import '../models/hive/timestamp_adapter.dart';

/// خدمة إدارة Hive
class HiveService {
  /// تهيئة Hive
  static Future<void> init() async {
    try {
      // تهيئة Hive مع مسار التخزين
      await Hive.initFlutter();

      // تسجيل محولات النماذج - استخدام try/catch لتجنب الأخطاء
      try {
        if (!Hive.isAdapterRegistered(HiveConstants.sentenceTypeId)) {
          Hive.registerAdapter(HiveSentenceModelAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول HiveSentenceModel: $e');
      }

      try {
        if (!Hive.isAdapterRegistered(HiveConstants.syncOperationTypeId)) {
          Hive.registerAdapter(SyncOperationTypeAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول SyncOperationType: $e');
      }

      try {
        if (!Hive.isAdapterRegistered(HiveConstants.syncLogTypeId)) {
          Hive.registerAdapter(SyncLogModelAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول SyncLogModel: $e');
      }

      try {
        if (!Hive.isAdapterRegistered(HiveConstants.statisticsTypeId)) {
          Hive.registerAdapter(StatisticsModelAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول StatisticsModel: $e');
      }

      try {
        if (!Hive.isAdapterRegistered(HiveConstants.categoryTypeId)) {
          Hive.registerAdapter(CategoryModelAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول CategoryModel: $e');
      }

      // تسجيل محول Timestamp
      try {
        if (!Hive.isAdapterRegistered(HiveConstants.timestampTypeId)) {
          Hive.registerAdapter(TimestampAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول TimestampAdapter: $e');
      }

      // تسجيل محول DateTime
      try {
        if (!Hive.isAdapterRegistered(HiveConstants.dateTimeTypeId)) {
          Hive.registerAdapter(DateTimeAdapter());
        }
      } catch (e) {
        debugPrint('تم تجاوز تسجيل محول DateTimeAdapter: $e');
      }

      // فتح صناديق Hive
      try {
        await Hive.openBox<HiveSentenceModel>(HiveConstants.sentencesBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق الجمل: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.sentencesBox);
        await Hive.openBox<HiveSentenceModel>(HiveConstants.sentencesBox);
      }

      try {
        await Hive.openBox<String>(HiveConstants.readSentencesBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق الجمل المقروءة: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.readSentencesBox);
        await Hive.openBox<String>(HiveConstants.readSentencesBox);
      }

      try {
        await Hive.openBox<String>(HiveConstants.favoritesBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق المفضلة: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.favoritesBox);
        await Hive.openBox<String>(HiveConstants.favoritesBox);
      }

      try {
        await Hive.openBox<CategoryModel>(HiveConstants.categoriesBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق الفئات: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.categoriesBox);
        await Hive.openBox<CategoryModel>(HiveConstants.categoriesBox);
      }

      try {
        await Hive.openBox<StatisticsModel>(HiveConstants.statisticsBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق الإحصائيات: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.statisticsBox);
        await Hive.openBox<StatisticsModel>(HiveConstants.statisticsBox);
      }

      try {
        await Hive.openBox<String>(HiveConstants.displayedSentencesBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق الجمل المعروضة: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.displayedSentencesBox);
        await Hive.openBox<String>(HiveConstants.displayedSentencesBox);
      }

      // فتح صندوق المحادثات
      try {
        await Hive.openBox(HiveConstants.conversationsBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق المحادثات: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.conversationsBox);
        await Hive.openBox(HiveConstants.conversationsBox);
      }

      try {
        await Hive.openBox<SyncLogModel>(HiveConstants.syncLogBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق سجلات المزامنة: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.syncLogBox);
        await Hive.openBox<SyncLogModel>(HiveConstants.syncLogBox);
      }

      try {
        await Hive.openBox(HiveConstants.userBox);
      } catch (e) {
        debugPrint('خطأ في فتح صندوق المستخدم: $e');
        await Hive.deleteBoxFromDisk(HiveConstants.userBox);
        await Hive.openBox(HiveConstants.userBox);
      }

      debugPrint('تم تهيئة Hive بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة Hive: $e');
      // محاولة إعادة تهيئة Hive بعد حذف جميع الصناديق
      try {
        await Hive.deleteFromDisk();
        await Hive.initFlutter();

        // إعادة تسجيل المحولات بشكل آمن
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.sentenceTypeId)) {
            Hive.registerAdapter(HiveSentenceModelAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.syncOperationTypeId)) {
            Hive.registerAdapter(SyncOperationTypeAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.syncLogTypeId)) {
            Hive.registerAdapter(SyncLogModelAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.statisticsTypeId)) {
            Hive.registerAdapter(StatisticsModelAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.categoryTypeId)) {
            Hive.registerAdapter(CategoryModelAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }

        // تسجيل محول Timestamp
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.timestampTypeId)) {
            Hive.registerAdapter(TimestampAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }

        // تسجيل محول DateTime
        try {
          if (!Hive.isAdapterRegistered(HiveConstants.dateTimeTypeId)) {
            Hive.registerAdapter(DateTimeAdapter());
          }
        } catch (e) {
          debugPrint('تجاوز: $e');
        }

        // إعادة فتح الصناديق
        await Hive.openBox<HiveSentenceModel>(HiveConstants.sentencesBox);
        await Hive.openBox<String>(HiveConstants.readSentencesBox);
        await Hive.openBox<String>(HiveConstants.favoritesBox);
        await Hive.openBox<CategoryModel>(HiveConstants.categoriesBox);
        await Hive.openBox<StatisticsModel>(HiveConstants.statisticsBox);
        await Hive.openBox<SyncLogModel>(HiveConstants.syncLogBox);
        await Hive.openBox(HiveConstants.userBox);
        await Hive.openBox<String>(HiveConstants.displayedSentencesBox);
        await Hive.openBox(HiveConstants.conversationsBox);

        debugPrint('تم إعادة تهيئة Hive بنجاح بعد حذف البيانات');
      } catch (e2) {
        debugPrint('فشل في إعادة تهيئة Hive: $e2');
        rethrow;
      }
    }
  }

  /// إغلاق Hive
  static Future<void> close() async {
    await Hive.close();
    debugPrint('تم إغلاق Hive');
  }

  /// مسح جميع البيانات
  static Future<void> clearAll() async {
    try {
      final sentencesBox =
          Hive.box<HiveSentenceModel>(HiveConstants.sentencesBox);
      final readSentencesBox = Hive.box<String>(HiveConstants.readSentencesBox);
      final favoritesBox = Hive.box<String>(HiveConstants.favoritesBox);
      final categoriesBox =
          Hive.box<CategoryModel>(HiveConstants.categoriesBox);
      final statisticsBox =
          Hive.box<StatisticsModel>(HiveConstants.statisticsBox);
      final syncLogBox = Hive.box<SyncLogModel>(HiveConstants.syncLogBox);
      final userBox = Hive.box(HiveConstants.userBox);
      final displayedSentencesBox =
          Hive.box<String>(HiveConstants.displayedSentencesBox);
      final conversationsBox = Hive.box(HiveConstants.conversationsBox);

      await sentencesBox.clear();
      await readSentencesBox.clear();
      await favoritesBox.clear();
      await categoriesBox.clear();
      await statisticsBox.clear();
      await syncLogBox.clear();
      await userBox.clear();
      await displayedSentencesBox.clear();
      await conversationsBox.clear();

      debugPrint('تم مسح جميع بيانات Hive');
    } catch (e) {
      debugPrint('خطأ في مسح بيانات Hive: $e');
      rethrow;
    }
  }
}
