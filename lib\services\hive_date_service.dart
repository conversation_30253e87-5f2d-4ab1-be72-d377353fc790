import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../constants/hive_constants.dart';

/// خدمة إدارة التاريخ باستخدام Hive
class HiveDateService {
  final Box _userBox = Hive.box(HiveConstants.userBox);
  
  /// التحقق مما إذا كان اليوم جديدًا
  Future<bool> isNewDay() async {
    try {
      final lastUpdateString = _userBox.get(HiveConstants.todayDateKey);
      
      // إذا لم يكن هناك تاريخ محفوظ، فهذا يعني أنه يوم جديد
      if (lastUpdateString == null) {
        await saveCurrentDate();
        return true;
      }
      
      // تحويل النص المحفوظ إلى كائن DateTime
      final lastUpdate = DateTime.parse(lastUpdateString);
      final now = DateTime.now();
      
      // مقارنة اليوم والشهر والسنة فقط (بدون الوقت)
      final lastUpdateDay = DateTime(lastUpdate.year, lastUpdate.month, lastUpdate.day);
      final today = DateTime(now.year, now.month, now.day);
      
      // إذا كان اليوم الحالي بعد آخر تحديث، فهذا يعني أنه يوم جديد
      final isNewDay = today.isAfter(lastUpdateDay);
      
      // إذا كان يومًا جديدًا، قم بتحديث التاريخ المحفوظ
      if (isNewDay) {
        await saveCurrentDate();
        
        // إعادة تعيين العدادات اليومية
        await _userBox.put(HiveConstants.todayShownCountKey, 0);
        await _userBox.put(HiveConstants.todayReadCountKey, 0);
      }
      
      return isNewDay;
    } catch (e) {
      debugPrint('خطأ في التحقق من اليوم الجديد: $e');
      return false;
    }
  }
  
  /// حفظ التاريخ الحالي
  Future<void> saveCurrentDate() async {
    try {
      final now = DateTime.now();
      await _userBox.put(HiveConstants.todayDateKey, now.toIso8601String());
      debugPrint('تم حفظ التاريخ الحالي: ${now.toIso8601String()}');
    } catch (e) {
      debugPrint('خطأ في حفظ التاريخ الحالي: $e');
    }
  }
  
  /// تحديث التاريخ المحفوظ (يستخدم عند التحديث اليدوي)
  Future<void> updateLastUpdateDate() async {
    await saveCurrentDate();
  }
}
