import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// import 'package:calendar_timeline/calendar_timeline.dart'; // تم التعطيل
import 'package:table_calendar/table_calendar.dart'; // تم الاستبدال
import 'package:shimmer/shimmer.dart';
import '../viewmodels/sentence_view_model.dart';
import '../providers/points_provider.dart';
import '../models/points.dart';
import '../theme/app_theme.dart';
import '../widgets/app_card.dart';

class CalendarStatsScreen extends StatefulWidget {
  static const routeName = '/calendar-stats';

  const CalendarStatsScreen({Key? key}) : super(key: key);

  @override
  State<CalendarStatsScreen> createState() => _CalendarStatsScreenState();
}

class _CalendarStatsScreenState extends State<CalendarStatsScreen> {
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  Map<String, dynamic> _statsForSelectedDate = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    // تحميل بيانات النقاط أولاً
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);
    await pointsProvider.fetchPoints();

    // ثم تحميل إحصائيات التاريخ المحدد
    _loadStatsForDate(_selectedDate);
  }

  Future<void> _loadStatsForDate(DateTime date) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل إحصائيات القراءة للتاريخ المحدد
      final sentenceViewModel =
          Provider.of<SentenceViewModel>(context, listen: false);
      final pointsProvider =
          Provider.of<PointsProvider>(context, listen: false);

      // الحصول على الجمل المقروءة في التاريخ المحدد
      final readSentences =
          await sentenceViewModel.getReadSentencesForDate(date);

      // محاولة الحصول على النقاط المكتسبة في التاريخ المحدد
      List<PointsRecord> pointsRecords = [];
      try {
        pointsRecords = await pointsProvider.getPointsRecordsForDate(date);
      } catch (pointsError) {
        debugPrint('خطأ في جلب سجلات النقاط: $pointsError');
        // استخدام البيانات المحلية إذا كان هناك خطأ
        pointsRecords = [];
      }

      // حساب إجمالي النقاط المكتسبة
      int totalEducationalPoints = 0;
      int totalRewardPoints = 0;

      for (var record in pointsRecords) {
        if (record.type == PointType.educational) {
          totalEducationalPoints += record.points;
        } else if (record.type == PointType.reward) {
          totalRewardPoints += record.points;
        }
      }

      // استخدام البيانات المحلية إذا لم تكن هناك سجلات
      if (pointsRecords.isEmpty) {
        // الحصول على إجمالي النقاط من مزود النقاط
        totalEducationalPoints = pointsProvider.educationalPoints;
        totalRewardPoints = pointsProvider.rewardPoints;
      }

      // تحديث الإحصائيات
      setState(() {
        _statsForSelectedDate = {
          'readSentencesCount': readSentences.length,
          'totalEducationalPoints': totalEducationalPoints,
          'totalRewardPoints': totalRewardPoints,
          'pointsRecords': pointsRecords,
        };
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');

      // في حالة الخطأ، عرض بيانات فارغة
      setState(() {
        _statsForSelectedDate = {
          'readSentencesCount': 0,
          'totalEducationalPoints': 0,
          'totalRewardPoints': 0,
          'pointsRecords': [],
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إحصائيات القراءة'),
        centerTitle: true,
        actions: [
          _isLoading
              ? const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث البيانات',
                  onPressed: () {
                    _loadInitialData();
                  },
                ),
        ],
      ),
      body: Column(
        children: [
          // تقويم لاختيار التاريخ (تم استبداله بـ table_calendar)
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: TableCalendar(
              locale: 'ar',
              firstDay: DateTime.now().subtract(const Duration(days: 365)),
              lastDay: DateTime.now(),
              focusedDay: _selectedDate,
              selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDate = selectedDay;
                });
                _loadStatsForDate(selectedDay);
              },
              calendarStyle: CalendarStyle(
                selectedDecoration: const BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                weekendTextStyle: const TextStyle(color: Colors.red),
              ),
              headerStyle: const HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _isLoading ? _buildLoadingView() : _buildStatsForSelectedDate(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Column(
          children: [
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsForSelectedDate() {
    final readSentencesCount = _statsForSelectedDate['readSentencesCount'] ?? 0;
    final totalEducationalPoints =
        _statsForSelectedDate['totalEducationalPoints'] ?? 0;
    final totalRewardPoints = _statsForSelectedDate['totalRewardPoints'] ?? 0;
    final pointsRecords = _statsForSelectedDate['pointsRecords'] ?? [];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التاريخ المحدد
          Text(
            '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // بطاقة إحصائيات القراءة
          AppCard(
            title: 'إحصائيات القراءة',
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildStatRow(
                      'الجمل المقروءة', '$readSentencesCount', Icons.book),
                  const Divider(),
                  _buildStatRow(
                      'نقاط التعلم', '$totalEducationalPoints', Icons.school),
                  const Divider(),
                  _buildStatRow('نقاط المكافآت', '$totalRewardPoints',
                      Icons.emoji_events),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // بطاقة سجل النقاط
          if (pointsRecords.isNotEmpty) ...[
            AppCard(
              title: 'سجل النقاط',
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: pointsRecords.length,
                itemBuilder: (context, index) {
                  final record = pointsRecords[index];
                  return ListTile(
                    leading: Icon(
                      record.type == PointType.educational
                          ? Icons.school
                          : Icons.emoji_events,
                      color: record.type == PointType.educational
                          ? Colors.blue
                          : Colors.orange,
                    ),
                    title: Text(record.activity),
                    subtitle: Text(
                      '${record.timestamp.hour}:${record.timestamp.minute}',
                    ),
                    trailing: Text(
                      '+${record.points}',
                      style: TextStyle(
                        color: record.type == PointType.educational
                            ? Colors.blue
                            : Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                },
              ),
            ),
          ] else ...[
            const AppCard(
              title: 'سجل النقاط',
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: Text('لا توجد نقاط مسجلة في هذا اليوم'),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
