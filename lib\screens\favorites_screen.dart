import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';
import '../models/sentence_model.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../services/hive_sentence_service.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  FilterOptions _filterOptions = FilterOptions();
  List<String> _categories = [];

  // Lista local para almacenar las frases cargadas
  final List<SentenceModel> _loadedSentences = [];
  bool _hasMoreSentences = true;
  bool _initialLoadDone = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();

    // Configurar el listener para la paginación
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    // Verificar si estamos cerca del final de la lista
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreSentences) {
        _loadMore();
      }
    }
  }

  Future<void> _loadCategories() async {
    final viewModel = Provider.of<SentenceViewModel>(context, listen: false);
    await viewModel.loadCategories();
    setState(() {
      _categories = viewModel.categories;
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _showFilterBottomSheet() {
    FilterBottomSheet.show(
      context: context,
      initialFilters: _filterOptions,
      categories: _categories,
      onApplyFilters: (filters) {
        setState(() {
          _filterOptions = filters;
          _resetAndReload();
        });
      },
    );
  }

  Future<void> _loadInitialSentences() async {
    if (_initialLoadDone) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final viewModel = Provider.of<SentenceViewModel>(context, listen: false);

      // الحصول على الجمل المفضلة من التخزين المحلي أولاً
      List<SentenceModel> localFavoriteSentences = [];

      // استخدام HiveSentenceService مباشرة للحصول على الجمل المفضلة
      final hiveSentenceService =
          Provider.of<HiveSentenceService>(context, listen: false);
      final hiveSentences = hiveSentenceService.getFavoriteSentences();
      localFavoriteSentences = hiveSentences
          .map((s) => viewModel.adapter!.toSentenceModel(s))
          .toList();
      debugPrint(
          'تم العثور على ${localFavoriteSentences.length} جملة مفضلة في التخزين المحلي');

      // الحصول على الجمل المفضلة من قاعدة البيانات
      final firebaseSentences = await viewModel.getFavoriteSentencesAsList(
        limit: 10,
        category: _filterOptions.category,
        startDate: _filterOptions.startDate,
        endDate: _filterOptions.endDate,
        difficulty: _filterOptions.difficulty, // إضافة فلتر الصعوبة
        descending: _filterOptions.sortOrder == SortOrder.newest,
      );
      debugPrint(
          'تم العثور على ${firebaseSentences.length} جملة مفضلة في Firebase');

      // دمج الجمل من التخزين المحلي وقاعدة البيانات
      final List<SentenceModel> allSentences = [];

      // إضافة الجمل المحلية أولاً
      for (final localSentence in localFavoriteSentences) {
        if (!allSentences.any((s) => s.id == localSentence.id)) {
          allSentences.add(localSentence);
        }
      }

      // إضافة الجمل من Firebase
      for (final firebaseSentence in firebaseSentences) {
        if (!allSentences.any((s) => s.id == firebaseSentence.id)) {
          allSentences.add(firebaseSentence);
        }
      }

      debugPrint('إجمالي الجمل المفضلة بعد الدمج: ${allSentences.length}');

      if (mounted) {
        setState(() {
          _loadedSentences.clear();
          _loadedSentences.addAll(allSentences);
          _hasMoreSentences = firebaseSentences.length >= 10;
          _isLoadingMore = false;
          _initialLoadDone = true;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الجمل المفضلة: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          _initialLoadDone = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('حدث خطأ أثناء تحميل الجمل المفضلة')),
        );
      }
    }
  }

  Future<void> _resetAndReload() async {
    setState(() {
      _loadedSentences.clear();
      _hasMoreSentences = true;
      _initialLoadDone = false;
    });
    await _loadInitialSentences();
  }

  Future<void> _loadMore() async {
    if (!_isLoadingMore && _hasMoreSentences) {
      setState(() {
        _isLoadingMore = true;
      });

      try {
        final viewModel =
            Provider.of<SentenceViewModel>(context, listen: false);
        final lastTimestamp = _loadedSentences.isNotEmpty
            ? await viewModel.getFavoriteTimestamp(_loadedSentences.last.id)
            : null;

        final newSentences = await viewModel.getFavoriteSentencesAsList(
          limit: 10,
          category: _filterOptions.category,
          startDate: _filterOptions.startDate,
          endDate: _filterOptions.endDate,
          difficulty: _filterOptions.difficulty, // إضافة فلتر الصعوبة
          descending: _filterOptions.sortOrder == SortOrder.newest,
          lastTimestamp: lastTimestamp,
          excludeIds: _loadedSentences.map((s) => s.id).toList(),
        );

        if (mounted) {
          setState(() {
            if (newSentences.isEmpty) {
              _hasMoreSentences = false;
            } else {
              _loadedSentences.addAll(newSentences);
            }
            _isLoadingMore = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('حدث خطأ أثناء تحميل المزيد من الجمل المفضلة')),
          );
        }
      }
    }
  }

  String _buildFilterDescription() {
    final List<String> parts = [];

    if (_filterOptions.category != null) {
      parts.add('الفئة: ${_filterOptions.category}');
    }

    if (_filterOptions.difficulty != null) {
      parts.add('الصعوبة: ${_getDifficultyText(_filterOptions.difficulty)}');
    }

    if (_filterOptions.sortOrder == SortOrder.newest) {
      parts.add('الترتيب: الأحدث');
    } else {
      parts.add('الترتيب: الأقدم');
    }

    if (_filterOptions.startDate != null) {
      parts.add(
          'من: ${_filterOptions.startDate!.day}/${_filterOptions.startDate!.month}/${_filterOptions.startDate!.year}');
    }

    if (_filterOptions.endDate != null) {
      parts.add(
          'إلى: ${_filterOptions.endDate!.day}/${_filterOptions.endDate!.month}/${_filterOptions.endDate!.year}');
    }

    return parts.join(' | ');
  }

  // دالة مساعدة لعرض نص مستوى الصعوبة
  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  // دالة مساعدة للحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // دالة مساعدة للحصول على أيقونة مستوى الصعوبة
  IconData _getDifficultyIcon(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Icons.sentiment_satisfied_alt;
      case 'medium':
        return Icons.sentiment_neutral;
      case 'hard':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
            tooltip: 'تصفية',
          ),
        ],
      ),
      body: FutureBuilder<void>(
        future: _loadInitialSentences(),
        builder: (context, snapshot) {
          if (!_initialLoadDone) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_loadedSentences.isEmpty) {
            return RefreshIndicator(
              onRefresh: () async {
                await _resetAndReload();
              },
              child: ListView(
                // إضافة physics للسماح بالسحب للأسفل حتى عندما تكون القائمة فارغة
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  // عنصر رئيسي بارتفاع كافٍ للسماح بالسحب
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.9,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.favorite_border,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد جمل مفضلة',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'اضغط على أيقونة القلب لإضافة جملة إلى المفضلة',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'اسحب للأسفل للتحديث',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Active filters indicator
              if (_filterOptions.category != null ||
                  _filterOptions.difficulty != null ||
                  _filterOptions.startDate != null ||
                  _filterOptions.endDate != null ||
                  _filterOptions.sortOrder != SortOrder.newest)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: Theme.of(context).primaryColor.withAlpha(26),
                  child: Row(
                    children: [
                      const Icon(Icons.filter_list, size: 18),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _buildFilterDescription(),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.clear, size: 18),
                        onPressed: () {
                          setState(() {
                            _filterOptions = FilterOptions();
                            _resetAndReload();
                          });
                        },
                        tooltip: 'مسح التصفية',
                      ),
                    ],
                  ),
                ),

              // Sentences list with pull-to-refresh
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await _resetAndReload();
                  },
                  child: ListView.builder(
                    // إضافة physics للسماح بالسحب للأسفل حتى عندما تكون القائمة قصيرة
                    physics: const AlwaysScrollableScrollPhysics(),
                    controller: _scrollController,
                    // إضافة عنصر إضافي في نهاية القائمة لضمان إمكانية السحب
                    itemCount: _loadedSentences.length + 2,
                    itemBuilder: (context, index) {
                      if (index == _loadedSentences.length) {
                        if (_isLoadingMore) {
                          return const Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        } else if (!_hasMoreSentences) {
                          return const Padding(
                            padding: EdgeInsets.all(16.0),
                            child:
                                Center(child: Text('لا توجد المزيد من الجمل')),
                          );
                        } else {
                          // Mostrar un espacio en blanco cuando hay más elementos pero aún no se están cargando
                          return const SizedBox(height: 80);
                        }
                      } else if (index == _loadedSentences.length + 1) {
                        // عنصر إضافي في نهاية القائمة لضمان إمكانية السحب
                        return SizedBox(
                            height: MediaQuery.of(context).size.height * 0.3);
                      }

                      final sentence = _loadedSentences[index];
                      return Card(
                        margin: const EdgeInsets.all(8),
                        elevation:
                            Theme.of(context).brightness == Brightness.dark
                                ? 2
                                : 1,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                sentence.englishText,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                sentence.arabicText,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  // عرض الفئة
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withAlpha(40)
                                          : Theme.of(context)
                                              .primaryColor
                                              .withAlpha(26),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Border.all(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .primary
                                                  .withAlpha(100),
                                              width: 1.0,
                                            )
                                          : null,
                                    ),
                                    child: Text(
                                      'الفئة: ${sentence.category}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Theme.of(context)
                                                .colorScheme
                                                .primary
                                            : Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  // عرض مستوى الصعوبة
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: _getDifficultyColor(
                                          sentence.difficulty),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          _getDifficultyIcon(
                                              sentence.difficulty),
                                          size: 14,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _getDifficultyText(
                                              sentence.difficulty),
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  Consumer<SentenceViewModel>(
                                    builder: (context, viewModel, _) {
                                      return IconButton(
                                        icon: const Icon(
                                          Icons.favorite,
                                          color: Colors.red,
                                        ),
                                        onPressed: () {
                                          // إزالة الجملة من القائمة المحلية فورًا لتحسين تجربة المستخدم
                                          setState(() {
                                            _loadedSentences.removeWhere(
                                                (s) => s.id == sentence.id);
                                          });

                                          // استخدام toggleFavorite بدلاً من toggleSentenceFavorite
                                          // لتحسين التعامل مع حالات الاتصال الضعيف بالإنترنت
                                          viewModel.toggleFavorite(sentence);
                                        },
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
