import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';

class AddSentenceScreen extends StatefulWidget {
  const AddSentenceScreen({super.key});

  @override
  AddSentenceScreenState createState() => AddSentenceScreenState();
}

class AddSentenceScreenState extends State<AddSentenceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _englishController = TextEditingController();
  final _arabicController = TextEditingController();
  final _categoryController = TextEditingController();
  String? _selectedCategory;
  String _selectedDifficulty = 'medium'; // القيمة الافتراضية للصعوبة

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SentenceViewModel>(context, listen: false).loadCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة جملة جديدة'),
        centerTitle: true,
      ),
      body: Consumer<SentenceViewModel>(
        builder: (context, sentenceViewModel, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  TextFormField(
                    controller: _englishController,
                    decoration: const InputDecoration(
                      labelText: 'النص الإنجليزي',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال النص الإنجليزي';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _arabicController,
                    decoration: const InputDecoration(
                      labelText: 'النص العربي',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال النص العربي';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedCategory,
                          decoration: const InputDecoration(
                            labelText: 'الفئة',
                            border: OutlineInputBorder(),
                          ),
                          items: [
                            ...sentenceViewModel.categories.map((category) {
                              return DropdownMenuItem(
                                value: category,
                                child: Text(category),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCategory = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء اختيار الفئة';
                            }
                            return null;
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          _showAddCategoryDialog(context);
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // إضافة حقل اختيار مستوى الصعوبة
                  DropdownButtonFormField<String>(
                    value: _selectedDifficulty,
                    decoration: const InputDecoration(
                      labelText: 'مستوى الصعوبة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'easy',
                        child: Text('سهل'),
                      ),
                      DropdownMenuItem(
                        value: 'medium',
                        child: Text('متوسط'),
                      ),
                      DropdownMenuItem(
                        value: 'hard',
                        child: Text('صعب'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDifficulty = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: sentenceViewModel.isLoading
                        ? null
                        : () async {
                            if (_formKey.currentState!.validate()) {
                              try {
                                await sentenceViewModel.addSentence(
                                  _englishController.text,
                                  _arabicController.text,
                                  _selectedCategory!,
                                  difficulty:
                                      _selectedDifficulty, // إضافة مستوى الصعوبة
                                );
                                if (context.mounted) {
                                  Navigator.pop(context);
                                }
                              } catch (e) {
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(e.toString()),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              }
                            }
                          },
                    child: sentenceViewModel.isLoading
                        ? const CircularProgressIndicator()
                        : const Text('إضافة الجملة'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _showAddCategoryDialog(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة فئة جديدة'),
          content: TextFormField(
            controller: _categoryController,
            decoration: const InputDecoration(
              labelText: 'اسم الفئة',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_categoryController.text.isNotEmpty) {
                  final viewModel = Provider.of<SentenceViewModel>(
                    context,
                    listen: false,
                  );

                  try {
                    // إضافة الفئة الجديدة
                    await viewModel.addCategory(_categoryController.text);
                    // تحديث القائمة
                    await viewModel.loadCategories();

                    setState(() {
                      _selectedCategory = _categoryController.text;
                    });

                    _categoryController.clear();
                    if (context.mounted) {
                      Navigator.pop(context);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _englishController.dispose();
    _arabicController.dispose();
    _categoryController.dispose();
    super.dispose();
  }
}
