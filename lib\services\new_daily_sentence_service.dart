import 'package:flutter/foundation.dart';
import '../models/hive/hive_sentence_model.dart';
import '../services/daily_sentences_manager.dart';
import '../services/hive_sentence_service.dart';
import '../services/sync_service.dart';

/// خدمة إدارة الجمل اليومية (نسخة جديدة تمامًا)
/// تم إعادة تصميم هذه الخدمة لتحاكي طريقة عمل Firestore مع التخزين المحلي
class NewDailySentenceService {
  final HiveSentenceService _hiveSentenceService;
  final SyncService _syncService;
  final DailySentencesManager _dailySentencesManager;

  NewDailySentenceService({
    required HiveSentenceService hiveSentenceService,
    required SyncService syncService,
  })  : _hiveSentenceService = hiveSentenceService,
        _syncService = syncService,
        _dailySentencesManager = DailySentencesManager();

  /// الحصول على الجمل اليومية
  Future<List<HiveSentenceModel>> getDailySentences(String userId,
      {bool forceRefresh = false}) async {
    try {
      debugPrint('الحصول على الجمل اليومية (النسخة الجديدة تمامًا)');

      // التحقق مما إذا كان اليوم جديدًا
      final isNewDay = _dailySentencesManager.isNewDay();
      debugPrint('هل اليوم جديد؟ $isNewDay');

      // الحصول على الجمل اليومية الحالية
      final currentDailySentences = _dailySentencesManager.getDailySentences();
      debugPrint('عدد الجمل اليومية الحالية: ${currentDailySentences.length}');

      // إذا كان اليوم جديدًا أو تم طلب تحديث إجباري أو لا توجد جمل يومية
      if (isNewDay || forceRefresh || currentDailySentences.isEmpty) {
        debugPrint(
            'يوم جديد أو تحديث إجباري أو لا توجد جمل يومية، جاري جلب جمل جديدة');

        // تحديث حالة القراءة من سجلات المزامنة
        await _hiveSentenceService.updateReadStatusFromSyncLogs();

        // جلب جمل جديدة
        return await _getNewDailySentences(userId);
      }

      debugPrint('إرجاع ${currentDailySentences.length} جملة يومية');
      return currentDailySentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية: $e');
      return [];
    }
  }

  /// الحصول على جمل يومية جديدة
  Future<List<HiveSentenceModel>> _getNewDailySentences(String userId) async {
    try {
      debugPrint('الحصول على جمل يومية جديدة');

      // جلب 10 جمل جديدة من مدير الجمل اليومية
      final newSentences =
          await _dailySentencesManager.getNewDailySentences(10);

      if (newSentences.isNotEmpty) {
        debugPrint(
            'تم الحصول على ${newSentences.length} جملة جديدة من التخزين المحلي');

        // زيادة عدد الجمل المعروضة
        await _hiveSentenceService
            .incrementTodayShownCount(newSentences.length);

        return newSentences;
      } else {
        // إذا لم تكن هناك جمل متاحة محليًا، جلب جمل من Firebase
        debugPrint(
            'لا توجد جمل متاحة في التخزين المحلي، جاري جلب جمل من Firebase');

        // جلب جمل عشوائية من Firebase
        final readSentenceIds =
            _hiveSentenceService.getReadSentencesBox().values.toList();
        final newSentencesFromFirebase = await _syncService
            .fetchRandomSentencesNotInList(userId, readSentenceIds, 10);

        if (newSentencesFromFirebase.isNotEmpty) {
          debugPrint(
              'تم جلب ${newSentencesFromFirebase.length} جملة جديدة من Firebase');

          // حفظ الجمل الجديدة في التخزين المحلي
          for (var sentence in newSentencesFromFirebase) {
            await _hiveSentenceService
                .getSentencesBox()
                .put(sentence.id, sentence);
          }

          // تعيين الجمل الجديدة كجمل يومية
          final sentenceIds =
              newSentencesFromFirebase.map((s) => s.id).toList();
          await _dailySentencesManager.setDailySentences(sentenceIds);

          // زيادة عدد الجمل المعروضة
          await _hiveSentenceService
              .incrementTodayShownCount(newSentencesFromFirebase.length);

          return newSentencesFromFirebase;
        } else {
          debugPrint('لم يتم العثور على جمل جديدة في Firebase');

          // إذا لم يتم العثور على جمل جديدة، إعادة تعيين حالة القراءة لجميع الجمل
          await _dailySentencesManager.resetAllSentencesReadStatus();
          debugPrint('تم إعادة تعيين حالة القراءة لجميع الجمل');

          // إعادة المحاولة بعد إعادة تعيين حالة القراءة
          return await _getNewDailySentences(userId);
        }
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل يومية جديدة: $e');
      return [];
    }
  }

  /// الحصول على المزيد من الجمل
  Future<List<HiveSentenceModel>> getMoreSentences(String userId) async {
    try {
      debugPrint('الحصول على المزيد من الجمل (النسخة الجديدة تمامًا)');

      // تحديث حالة القراءة من سجلات المزامنة
      await _hiveSentenceService.updateReadStatusFromSyncLogs();

      // مسح الجمل اليومية الحالية
      await _dailySentencesManager.clearDailySentences();

      // جلب جمل جديدة
      return await _getNewDailySentences(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على المزيد من الجمل: $e');
      return [];
    }
  }

  /// تعليم جملة كمقروءة
  Future<bool> markSentenceAsRead(String sentenceId, String userId) async {
    try {
      debugPrint('تعليم الجملة كمقروءة: $sentenceId');

      // تعليم الجملة كمقروءة في مدير الجمل اليومية
      final success =
          await _dailySentencesManager.markSentenceAsRead(sentenceId);

      // زيادة عدد الجمل المقروءة
      if (success) {
        await _hiveSentenceService.incrementTodayReadCount();
      }

      debugPrint('تم تعليم الجملة كمقروءة في الخدمة: $sentenceId');
      return success;
    } catch (e) {
      debugPrint('خطأ في تعليم الجملة كمقروءة: $e');
      return false;
    }
  }

  /// التحقق مما إذا كانت جميع الجمل اليومية مقروءة
  bool areAllDailySentencesRead() {
    return _dailySentencesManager.areAllDailySentencesRead();
  }

  /// الحصول على عدد الجمل المعروضة اليوم
  int getTodayShownCount() {
    return _hiveSentenceService.getTodayShownCount();
  }

  /// الحصول على عدد الجمل المقروءة اليوم
  int getTodayReadCount() {
    return _hiveSentenceService.getTodayReadCount();
  }
}
