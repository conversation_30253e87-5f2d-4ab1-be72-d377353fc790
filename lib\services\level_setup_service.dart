import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

/// خدمة إعداد المستويات والدورات في قاعدة البيانات
class LevelSetupService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// إعادة هيكلة المستويات والدورات في قاعدة البيانات
  Future<void> restructureLevels() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('المستخدم غير مسجل الدخول');
        return;
      }

      // إنشاء المستويات الثلاثة
      await _setupLevel1();
      await _setupLevel2();
      await _setupLevel3();

      // إعداد تقدم المستخدم
      await _setupUserProgress(user.uid);

      debugPrint('تم إعادة هيكلة المستويات بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعادة هيكلة المستويات: $e');
      rethrow;
    }
  }

  /// إعداد المستوى الأول (سهل) مع 7 مجموعات
  Future<void> _setupLevel1() async {
    // إنشاء المستوى الأول
    await _firestore.collection('levels').doc('1').set({
      'title': 'المستوى الأول',
      'requiredSentences': 420, // إجمالي 420 جملة (7 مجموعات × 60 جملة)
      'totalEducationalPoints': 1000,
      'difficulty': 'easy',
    });

    // إنشاء 7 مجموعات للمستوى الأول
    for (int groupId = 1; groupId <= 7; groupId++) {
      await _firestore
          .collection('levels')
          .doc('1')
          .collection('cycles')
          .doc(groupId.toString())
          .set({
        'title': 'المجموعة $groupId',
        'day': 'اليوم $groupId',
        'isLocked': groupId > 1, // المجموعة الأولى فقط مفتوحة
        'isCompleted': false,
        'totalSentences': 60, // كل مجموعة تحتوي على 60 جملة
      });

      // إنشاء 4 دروس لكل مجموعة في المستوى الأول
      await _createLevel1LessonGroups('1', groupId.toString());
    }
  }

  /// إعداد المستوى الثاني (متوسط) مع 12 مجموعة
  Future<void> _setupLevel2() async {
    // إنشاء المستوى الثاني
    await _firestore.collection('levels').doc('2').set({
      'title': 'المستوى الثاني',
      'requiredSentences': 960, // إجمالي 960 جملة (12 مجموعة × 80 جملة)
      'totalEducationalPoints': 1500,
      'difficulty': 'medium',
    });

    // إنشاء 12 مجموعة للمستوى الثاني
    for (int groupId = 1; groupId <= 12; groupId++) {
      await _firestore
          .collection('levels')
          .doc('2')
          .collection('cycles')
          .doc(groupId.toString())
          .set({
        'title': 'المجموعة $groupId',
        'day': 'اليوم $groupId',
        'isLocked': true, // جميع المجموعات مغلقة حتى يتم فتح المستوى
        'isCompleted': false,
        'totalSentences': 80, // كل مجموعة تحتوي على 80 جملة
      });

      // إنشاء 5 دروس لكل مجموعة في المستوى الثاني
      await _createLevel2LessonGroups('2', groupId.toString());
    }
  }

  /// إعداد المستوى الثالث (صعب) مع 14 مجموعة
  Future<void> _setupLevel3() async {
    // إنشاء المستوى الثالث
    await _firestore.collection('levels').doc('3').set({
      'title': 'المستوى الثالث',
      'requiredSentences': 1400, // إجمالي 1400 جملة (14 مجموعة × 100 جملة)
      'totalEducationalPoints': 2000,
      'difficulty': 'hard',
    });

    // إنشاء 14 مجموعة للمستوى الثالث
    for (int groupId = 1; groupId <= 14; groupId++) {
      await _firestore
          .collection('levels')
          .doc('3')
          .collection('cycles')
          .doc(groupId.toString())
          .set({
        'title': 'المجموعة $groupId',
        'day': 'اليوم $groupId',
        'isLocked': true, // جميع المجموعات مغلقة حتى يتم فتح المستوى
        'isCompleted': false,
        'totalSentences': 100, // كل مجموعة تحتوي على 100 جملة
      });

      // إنشاء 6 دروس لكل مجموعة في المستوى الثالث
      await _createLevel3LessonGroups('3', groupId.toString());
    }
  }

  /// إنشاء مجموعات الدروس للمستوى الأول
  Future<void> _createLevel1LessonGroups(String levelId, String cycleId) async {
    // إنشاء 4 دروس لكل مجموعة في المستوى الأول (دفعة 1، محادثة 1، دفعة 2، مراجعة)
    final lessonGroups = [
      {
        'id': '1',
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': false, // الدرس الأول فقط مفتوح
        'routePath': '/daily-sentences',
      },
      {
        'id': '2',
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '3',
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/daily-sentences',
      },
      {
        'id': '4',
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 30, // المراجعة تحتوي على 30 جملة
        'isLocked': true,
        'routePath': '/review',
      },
    ];

    // إنشاء المجموعات في قاعدة البيانات
    for (var group in lessonGroups) {
      final groupId = group['id'] as String;
      await _firestore
          .collection('levels')
          .doc(levelId)
          .collection('cycles')
          .doc(cycleId)
          .collection('lessonGroups')
          .doc(groupId)
          .set(group);
    }
  }

  /// إنشاء مجموعات الدروس للمستوى الثاني
  Future<void> _createLevel2LessonGroups(String levelId, String cycleId) async {
    // إنشاء 5 دروس لكل مجموعة في المستوى الثاني (دفعة 1، محادثة 1، محادثة 2، دفعة 2، مراجعة)
    final lessonGroups = [
      {
        'id': '1',
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': false, // الدرس الأول فقط مفتوح
        'routePath': '/daily-sentences',
      },
      {
        'id': '2',
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '3',
        'title': 'محادثة 2',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '4',
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/daily-sentences',
      },
      {
        'id': '5',
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 40, // المراجعة تحتوي على 40 جملة
        'isLocked': true,
        'routePath': '/review',
      },
    ];

    // إنشاء المجموعات في قاعدة البيانات
    for (var group in lessonGroups) {
      final groupId = group['id'] as String;
      await _firestore
          .collection('levels')
          .doc(levelId)
          .collection('cycles')
          .doc(cycleId)
          .collection('lessonGroups')
          .doc(groupId)
          .set(group);
    }
  }

  /// إنشاء مجموعات الدروس للمستوى الثالث
  Future<void> _createLevel3LessonGroups(String levelId, String cycleId) async {
    // إنشاء 6 دروس لكل مجموعة في المستوى الثالث (دفعة 1، محادثة 1، محادثة 2، محادثة 3، دفعة 2، مراجعة)
    final lessonGroups = [
      {
        'id': '1',
        'title': 'دفعة 1',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': false, // الدرس الأول فقط مفتوح
        'routePath': '/daily-sentences',
      },
      {
        'id': '2',
        'title': 'محادثة 1',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '3',
        'title': 'محادثة 2',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '4',
        'title': 'محادثة 3',
        'type': 'conversation',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/conversation',
      },
      {
        'id': '5',
        'title': 'دفعة 2',
        'type': 'sentenceBatch',
        'totalSentences': 10,
        'isLocked': true,
        'routePath': '/daily-sentences',
      },
      {
        'id': '6',
        'title': 'مراجعة',
        'type': 'review',
        'totalSentences': 50, // المراجعة تحتوي على 50 جملة
        'isLocked': true,
        'routePath': '/review',
      },
    ];

    // إنشاء المجموعات في قاعدة البيانات
    for (var group in lessonGroups) {
      final groupId = group['id'] as String;
      await _firestore
          .collection('levels')
          .doc(levelId)
          .collection('cycles')
          .doc(cycleId)
          .collection('lessonGroups')
          .doc(groupId)
          .set(group);
    }
  }

  /// إعداد تقدم المستخدم
  Future<void> _setupUserProgress(String userId) async {
    // التحقق من وجود تقدم للمستخدم
    final userProgressDoc = await _firestore
        .collection('users')
        .doc(userId)
        .collection('progress')
        .doc('levels')
        .get();

    // إذا لم يكن هناك تقدم، قم بإنشاء تقدم افتراضي
    if (!userProgressDoc.exists) {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('progress')
          .doc('levels')
          .set({
        'currentLevel': 1,
        'level_1_unlocked': true,
        'level_2_unlocked': false,
        'level_3_unlocked': false,
        'level_1_completedSentences': 0,
        'level_1_points': 0,
        'level_2_points': 0,
        'level_3_points': 0,
      });
    }
  }
}
