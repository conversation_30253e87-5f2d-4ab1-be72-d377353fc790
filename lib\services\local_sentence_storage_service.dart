import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentence_model.dart';

/// خدمة لتخزين الجمل محليًا باستخدام SharedPreferences
class LocalSentenceStorageService {
  // مفاتيح التخزين المحلي
  static const String _dailySentencesKey = 'daily_sentences';
  static const String _lastUpdateDateKey = 'last_sentences_update_date';
  static const String _readSentencesKey = 'read_sentences';
  static const String _todayShownCountKey = 'today_shown_count';
  static const String _todayReadCountKey = 'today_read_count';
  static const String _todayDateKey = 'today_date';
  static const String _pendingSyncSentencesKey = 'pending_sync_sentences';

  // الحصول على الجمل اليومية المخزنة محليًا
  Future<List<SentenceModel>> getLocalDailySentences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sentencesJson = prefs.getString(_dailySentencesKey);

      if (sentencesJson == null || sentencesJson.isEmpty) {
        return [];
      }

      final List<dynamic> decoded = jsonDecode(sentencesJson);
      final sentences =
          decoded.map((json) => SentenceModel.fromJson(json)).toList();

      // تحديث حالة القراءة من قائمة الجمل المقروءة
      final readSentenceIds = await getReadSentenceIds();

      // تحديث حالة القراءة للجمل المخزنة محليًا
      for (var sentence in sentences) {
        if (readSentenceIds.contains(sentence.id)) {
          sentence.isReadByCurrentUser = true;
        }
      }

      debugPrint('تم تحميل ${sentences.length} جملة يومية من التخزين المحلي');
      debugPrint(
          'منها ${sentences.where((s) => !s.isReadByCurrentUser).length} جملة غير مقروءة');

      return sentences;
    } catch (e) {
      debugPrint('Error getting local daily sentences: $e');
      return [];
    }
  }

  // تخزين الجمل اليومية محليًا
  Future<void> storeLocalDailySentences(List<SentenceModel> sentences) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sentencesJson = sentences.map((s) => s.toJson()).toList();
      await prefs.setString(_dailySentencesKey, jsonEncode(sentencesJson));
      debugPrint('Stored ${sentences.length} sentences locally');
    } catch (e) {
      debugPrint('Error storing local daily sentences: $e');
    }
  }

  // تحديث حالة قراءة جملة محليًا
  Future<void> markSentenceAsReadLocally(String sentenceId) async {
    try {
      final sentences = await getLocalDailySentences();

      if (sentences.isEmpty) {
        debugPrint('No local sentences found to mark as read');
        // إضافة الجملة إلى قائمة الجمل المقروءة على أي حال
        await _addToReadSentences(sentenceId);
        return;
      }

      // تحديث حالة القراءة للجملة المحددة فقط
      final updatedSentences = sentences.map((sentence) {
        if (sentence.id == sentenceId) {
          debugPrint('Found sentence to mark as read: ${sentence.id}');
          return sentence.copyWith(isReadByCurrentUser: true);
        }
        return sentence;
      }).toList();

      // تخزين جميع الجمل (بما في ذلك المقروءة) للحفاظ على القائمة كاملة
      await storeLocalDailySentences(updatedSentences);

      // إضافة الجملة إلى قائمة الجمل المقروءة
      await _addToReadSentences(sentenceId);

      // إضافة الجملة إلى قائمة الجمل المعلقة للمزامنة
      await addToPendingSyncSentences(sentenceId);

      // ملاحظة: لا نقوم بزيادة عداد الجمل المقروءة هنا
      // لأن ذلك يتم في SentenceViewModel.markAsRead

      debugPrint('Marked sentence $sentenceId as read locally');
    } catch (e) {
      debugPrint('Error marking sentence as read locally: $e');
    }
  }

  // إضافة جملة إلى قائمة الجمل المقروءة
  Future<void> _addToReadSentences(String sentenceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readSentencesJson = prefs.getString(_readSentencesKey);

      List<String> readSentences = [];
      if (readSentencesJson != null && readSentencesJson.isNotEmpty) {
        readSentences = List<String>.from(jsonDecode(readSentencesJson));
      }

      if (!readSentences.contains(sentenceId)) {
        readSentences.add(sentenceId);
        await prefs.setString(_readSentencesKey, jsonEncode(readSentences));
      }
    } catch (e) {
      debugPrint('Error adding to read sentences: $e');
    }
  }

  // الحصول على قائمة معرفات الجمل المقروءة
  Future<List<String>> getReadSentenceIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readSentencesJson = prefs.getString(_readSentencesKey);

      if (readSentencesJson == null || readSentencesJson.isEmpty) {
        return [];
      }

      return List<String>.from(jsonDecode(readSentencesJson));
    } catch (e) {
      debugPrint('Error getting read sentence IDs: $e');
      return [];
    }
  }

  // فحص ما إذا كانت هناك جمل غير مقروءة محليًا
  Future<bool> hasUnreadSentencesLocally() async {
    try {
      // الحصول على الجمل اليومية مع تحديث حالة القراءة
      final sentences = await getLocalDailySentences();

      // التحقق من وجود جمل غير مقروءة
      final hasUnread = sentences.any((s) => !s.isReadByCurrentUser);

      debugPrint(
          'التحقق من وجود جمل غير مقروءة محليًا: ${sentences.length} جملة، غير مقروءة: $hasUnread');

      // طباعة تفاصيل الجمل للتشخيص
      for (var i = 0; i < sentences.length; i++) {
        final s = sentences[i];
        debugPrint(
            'الجملة $i: المعرف=${s.id}, مقروءة=${s.isReadByCurrentUser}, النص=${s.arabicText.substring(0, min(20, s.arabicText.length))}...');
      }

      return hasUnread;
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود جمل غير مقروءة محليًا: $e');
      return false;
    }
  }

  // فحص ما إذا كان اليوم جديدًا
  Future<bool> isNewDayLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateDateStr = prefs.getString(_lastUpdateDateKey);

      if (lastUpdateDateStr == null) {
        await _updateLastUpdateDate();
        return true;
      }

      final lastUpdateDate = DateTime.parse(lastUpdateDateStr);
      final now = DateTime.now();

      // مقارنة اليوم والشهر والسنة فقط (بدون الوقت)
      final lastUpdateDay = DateTime(
          lastUpdateDate.year, lastUpdateDate.month, lastUpdateDate.day);
      final today = DateTime(now.year, now.month, now.day);

      // إذا كان اليوم الحالي بعد آخر تحديث، فهذا يعني أنه يوم جديد
      final isNewDay = today.isAfter(lastUpdateDay);

      // إذا كان يومًا جديدًا، قم بتحديث التاريخ المحفوظ
      if (isNewDay) {
        await _updateLastUpdateDate();
        // إعادة تعيين عداد الجمل المعروضة اليوم
        await _resetTodayShownCount();
      }

      return isNewDay;
    } catch (e) {
      debugPrint('Error checking for new day locally: $e');
      return false;
    }
  }

  // تحديث تاريخ آخر تحديث
  Future<void> _updateLastUpdateDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      await prefs.setString(_lastUpdateDateKey, now.toIso8601String());

      // تحديث تاريخ اليوم
      final today = DateTime(now.year, now.month, now.day);
      await prefs.setString(_todayDateKey, today.toIso8601String());

      debugPrint('Updated last update date: ${now.toIso8601String()}');
    } catch (e) {
      debugPrint('Error updating last update date: $e');
    }
  }

  // تحديث تاريخ آخر تحديث (للاستخدام العام)
  Future<void> updateLastUpdateDate() async {
    await _updateLastUpdateDate();
  }

  // إعادة تعيين عدادات اليوم
  Future<void> _resetTodayShownCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_todayShownCountKey, 0);
      await prefs.setInt(_todayReadCountKey, 0);
      debugPrint('Reset today shown and read counts to 0');
    } catch (e) {
      debugPrint('Error resetting today counts: $e');
    }
  }

  // زيادة عداد الجمل المعروضة اليوم
  Future<void> incrementTodayShownCount(int count) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_todayShownCountKey) ?? 0;
      await prefs.setInt(_todayShownCountKey, currentCount + count);
      debugPrint(
          'Incremented today shown count by $count to ${currentCount + count}');
    } catch (e) {
      debugPrint('Error incrementing today shown count: $e');
    }
  }

  // الحصول على عداد الجمل المعروضة اليوم
  Future<int> getTodayShownCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من تاريخ اليوم
      final todayDateStr = prefs.getString(_todayDateKey);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // إذا كان تاريخ اليوم غير محفوظ أو مختلف عن اليوم الحالي، قم بإعادة تعيين العداد
      if (todayDateStr == null) {
        await prefs.setString(_todayDateKey, today.toIso8601String());
        await prefs.setInt(_todayShownCountKey, 0);
        await prefs.setInt(_todayReadCountKey, 0);
        return 0;
      }

      final savedDate = DateTime.parse(todayDateStr);
      final savedDay = DateTime(savedDate.year, savedDate.month, savedDate.day);

      if (today.isAfter(savedDay)) {
        // إذا كان اليوم الحالي بعد التاريخ المحفوظ، قم بإعادة تعيين العداد
        await prefs.setString(_todayDateKey, today.toIso8601String());
        await prefs.setInt(_todayShownCountKey, 0);
        await prefs.setInt(_todayReadCountKey, 0);
        return 0;
      }

      // إرجاع العداد الحالي
      return prefs.getInt(_todayShownCountKey) ?? 0;
    } catch (e) {
      debugPrint('Error getting today shown count: $e');
      return 0;
    }
  }

  // الحصول على عداد الجمل المقروءة اليوم
  Future<int> getTodayReadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من تاريخ اليوم
      final todayDateStr = prefs.getString(_todayDateKey);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // إذا كان تاريخ اليوم غير محفوظ أو مختلف عن اليوم الحالي، قم بإعادة تعيين العداد
      if (todayDateStr == null) {
        await prefs.setString(_todayDateKey, today.toIso8601String());
        await prefs.setInt(_todayReadCountKey, 0);
        return 0;
      }

      final savedDate = DateTime.parse(todayDateStr);
      final savedDay = DateTime(savedDate.year, savedDate.month, savedDate.day);

      if (today.isAfter(savedDay)) {
        // إذا كان اليوم الحالي بعد التاريخ المحفوظ، قم بإعادة تعيين العداد
        await prefs.setString(_todayDateKey, today.toIso8601String());
        await prefs.setInt(_todayReadCountKey, 0);
        return 0;
      }

      // إرجاع العداد الحالي
      return prefs.getInt(_todayReadCountKey) ?? 0;
    } catch (e) {
      debugPrint('Error getting today read count: $e');
      return 0;
    }
  }

  // زيادة عداد الجمل المقروءة اليوم
  Future<void> incrementTodayReadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_todayReadCountKey) ?? 0;
      await prefs.setInt(_todayReadCountKey, currentCount + 1);
      debugPrint('Incremented today read count to ${currentCount + 1}');
    } catch (e) {
      debugPrint('Error incrementing today read count: $e');
    }
  }

  // إضافة جملة إلى قائمة الجمل المعلقة للمزامنة
  Future<void> addToPendingSyncSentences(String sentenceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingSentences =
          prefs.getStringList(_pendingSyncSentencesKey) ?? [];

      // التحقق من عدم وجود الجملة بالفعل في القائمة
      if (!pendingSentences.contains(sentenceId)) {
        pendingSentences.add(sentenceId);
        await prefs.setStringList(_pendingSyncSentencesKey, pendingSentences);
        debugPrint(
            'Added sentence $sentenceId to pending sync list. Total: ${pendingSentences.length}');
      }
    } catch (e) {
      debugPrint('Error adding to pending sync sentences: $e');
    }
  }

  // الحصول على قائمة الجمل المعلقة للمزامنة
  Future<List<String>> getPendingSyncSentences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_pendingSyncSentencesKey) ?? [];
    } catch (e) {
      debugPrint('Error getting pending sync sentences: $e');
      return [];
    }
  }

  // حذف جملة من قائمة الجمل المعلقة للمزامنة
  Future<void> removeFromPendingSyncSentences(String sentenceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingSentences =
          prefs.getStringList(_pendingSyncSentencesKey) ?? [];

      if (pendingSentences.contains(sentenceId)) {
        pendingSentences.remove(sentenceId);
        await prefs.setStringList(_pendingSyncSentencesKey, pendingSentences);
        debugPrint(
            'Removed sentence $sentenceId from pending sync list. Remaining: ${pendingSentences.length}');
      }
    } catch (e) {
      debugPrint('Error removing from pending sync sentences: $e');
    }
  }

  // مسح قائمة الجمل المعلقة للمزامنة
  Future<void> clearPendingSyncSentences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_pendingSyncSentencesKey, []);
      debugPrint('Cleared pending sync sentences list');
    } catch (e) {
      debugPrint('Error clearing pending sync sentences: $e');
    }
  }

  // فحص ما إذا كانت القائمة فارغة
  Future<bool> isDailySentencesEmpty() async {
    final sentences = await getLocalDailySentences();
    return sentences.isEmpty;
  }

  // مسح الجمل اليومية فقط
  Future<void> clearDailySentences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dailySentencesKey);
      debugPrint('Cleared daily sentences from local storage');
    } catch (e) {
      debugPrint('Error clearing daily sentences: $e');
    }
  }

  // مسح جميع البيانات المحلية
  Future<void> clearAllLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dailySentencesKey);
      await prefs.remove(_lastUpdateDateKey);
      await prefs.remove(_readSentencesKey);
      await prefs.remove(_todayShownCountKey);
      await prefs.remove(_todayReadCountKey);
      await prefs.remove(_todayDateKey);
      debugPrint('Cleared all local sentence data');
    } catch (e) {
      debugPrint('Error clearing local sentence data: $e');
    }
  }
}
