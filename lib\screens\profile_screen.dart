import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/sentence_view_model.dart';
import '../viewmodels/auth_view_model.dart';
import '../theme/app_theme.dart';
import '../widgets/app_card.dart';
import 'package:fl_chart/fl_chart.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  // بناء زر تحميل جميع الجمل المتاحة
  Widget _buildLoadAllSentencesButton(
      BuildContext context, SentenceViewModel sentenceVM) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.cloud_download_outlined,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'تحميل جميع الجمل المتاحة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (sentenceVM.isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'تحميل جميع الجمل المتاحة للاستخدام بدون إنترنت',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.download),
                  label: const Text('تحميل الجمل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  onPressed: sentenceVM.isLoading
                      ? null
                      : () async {
                          // عرض مربع حوار للتأكيد
                          final shouldProceed = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('تحميل جميع الجمل'),
                              content: const Text(
                                'سيتم تحميل جميع الجمل المتاحة للاستخدام بدون إنترنت. قد يستغرق هذا بعض الوقت ويستهلك بيانات الإنترنت. هل تريد المتابعة؟',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(false),
                                  child: const Text('إلغاء'),
                                ),
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  child: const Text('تحميل'),
                                ),
                              ],
                            ),
                          );

                          if (shouldProceed == true) {
                            final count =
                                await sentenceVM.fetchAllAvailableSentences();

                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    count > 0
                                        ? 'تم تحميل $count جملة جديدة بنجاح'
                                        : 'لم يتم العثور على جمل جديدة للتحميل',
                                  ),
                                  backgroundColor:
                                      count > 0 ? Colors.green : Colors.orange,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final titleFontSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 18.0 : 20.0);
    final contentPadding =
        isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0);
    final spacingHeight1 =
        isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0);
    final spacingHeight2 =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 16.0 : 24.0);
    final sectionTitleFontSize =
        isVerySmallScreen ? 14.0 : (isSmallScreen ? 16.0 : 18.0);
    final chartHeight =
        isVerySmallScreen ? 160.0 : (isSmallScreen ? 180.0 : 200.0);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إحصائيات المستخدم',
          style: TextStyle(
            fontSize: titleFontSize,
          ),
        ),
        centerTitle: true,
        actions: [
          // إضافة زر لتحديث البيانات
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: () {
              // تحديث بيانات المستخدم
              final authVM = Provider.of<AuthViewModel>(context, listen: false);
              if (authVM.user != null) {
                authVM.refreshUserData();
              }

              // تحديث بيانات الجمل
              final sentenceVM =
                  Provider.of<SentenceViewModel>(context, listen: false);
              sentenceVM.loadSentences();
            },
          ),
        ],
      ),
      body: Consumer2<AuthViewModel, SentenceViewModel>(
        builder: (context, authVM, sentenceVM, _) {
          if (!authVM.isAuthenticated) {
            return const Center(
              child: Text('يجب تسجيل الدخول لعرض الإحصائيات'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              // Refresh stats
              await sentenceVM.loadSentences();
            },
            child: ListView(
              padding: EdgeInsets.all(contentPadding),
              children: [
                UserInfoCard(user: authVM.user!),
                SizedBox(height: spacingHeight1),
                StatsGrid(sentenceVM: sentenceVM),
                SizedBox(height: spacingHeight1),

                // زر الانتقال إلى صفحة إحصائيات التقويم
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    onTap: () {
                      // استخدام Navigator.of(context, rootNavigator: true) للتنقل من خلال المتصفح الجذري
                      Navigator.of(context, rootNavigator: true)
                          .pushNamed('/calendar-stats');
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            color: Colors.blue,
                            size: 24,
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'إحصائيات التقويم',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'عرض إحصائيات القراءة والنقاط حسب التاريخ',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.grey,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                SizedBox(height: spacingHeight1),

                // زر تحميل جميع الجمل المتاحة
                _buildLoadAllSentencesButton(context, sentenceVM),

                SizedBox(height: spacingHeight2),
                Text(
                  'نشاط القراءة الأسبوعي',
                  style: TextStyle(
                    fontSize: sectionTitleFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: spacingHeight1),
                SizedBox(
                  height: chartHeight,
                  child: WeeklyActivityChart(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class UserInfoCard extends StatelessWidget {
  final dynamic user;

  const UserInfoCard({super.key, required this.user});

  Widget _buildUserAvatar(String? photoURL, double radius, double iconSize) {
    if (photoURL == null) {
      return CircleAvatar(
        radius: radius,
        backgroundColor: Colors.grey,
        child: Icon(Icons.person, size: iconSize, color: Colors.white),
      );
    }

    if (photoURL.startsWith('assets/')) {
      // استخدام AssetImage بشكل صحيح بدون إضافة file:///
      return CircleAvatar(
        radius: radius,
        backgroundImage: AssetImage(photoURL),
      );
    } else if (photoURL.startsWith('file:///assets/')) {
      // إصلاح مسار الملف الخاطئ
      final correctedPath = photoURL.replaceFirst('file:///', '');
      return CircleAvatar(
        radius: radius,
        backgroundImage: AssetImage(correctedPath),
      );
    } else {
      // استخدام NetworkImage للروابط
      try {
        return CircleAvatar(
          radius: radius,
          backgroundImage: NetworkImage(photoURL),
          onBackgroundImageError: (exception, stackTrace) {
            debugPrint('Error loading profile image: $exception');
          },
        );
      } catch (e) {
        debugPrint('Error creating NetworkImage: $e');
        return CircleAvatar(
          radius: radius,
          backgroundColor: Colors.grey,
          child: Icon(Icons.person, size: iconSize, color: Colors.white),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // استخدام userModel بدلاً من user للحصول على الاسم المحدث
    // استخدام listen: true لتحديث الواجهة عند تغيير بيانات المستخدم
    final authVM = Provider.of<AuthViewModel>(context);
    final userModel = authVM.userModel;

    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final avatarRadius =
        isVerySmallScreen ? 30.0 : (isSmallScreen ? 35.0 : 40.0);
    final avatarIconSize =
        isVerySmallScreen ? 30.0 : (isSmallScreen ? 35.0 : 40.0);
    final nameFontSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 18.0 : 20.0);
    final badgeFontSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 13.0 : 14.0);
    final verticalSpacing =
        isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0);
    final badgePadding = isVerySmallScreen
        ? const EdgeInsets.symmetric(horizontal: 12, vertical: 6)
        : (isSmallScreen
            ? const EdgeInsets.symmetric(horizontal: 14, vertical: 7)
            : const EdgeInsets.symmetric(horizontal: 16, vertical: 8));

    return AppCard(
      padding: EdgeInsets.all(
          isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUserAvatar(userModel?.photoURL ?? user.photoURL, avatarRadius,
              avatarIconSize),
          SizedBox(height: verticalSpacing),
          Text(
            userModel?.displayName ??
                user.displayName ??
                user.email ??
                'المستخدم',
            style: TextStyle(
              fontSize: nameFontSize,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: verticalSpacing),
          Container(
            padding: badgePadding,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(30),
              borderRadius:
                  BorderRadius.circular(isVerySmallScreen ? 16.0 : 20.0),
            ),
            child: Text(
              'إحصائيات المستخدم',
              style: TextStyle(
                fontSize: badgeFontSize,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class StatsGrid extends StatelessWidget {
  final SentenceViewModel sentenceVM;

  const StatsGrid({super.key, required this.sentenceVM});

  @override
  Widget build(BuildContext context) {
    // Usar datos directamente del SentenceViewModel
    return Builder(
      builder: (context) {
        // Obtener estadísticas directamente del modelo
        int readSentencesCount = 0;
        int favoritesCount = 0;

        // Intentar obtener los contadores desde el adaptador si está disponible
        if (sentenceVM.adapter != null) {
          try {
            final readSentences = sentenceVM.adapter!.getReadSentences();
            final favoriteSentences =
                sentenceVM.adapter!.getFavoriteSentences();

            readSentencesCount = readSentences.length;
            favoritesCount = favoriteSentences.length;

            debugPrint(
                'Estadísticas desde el adaptador - Leídas: $readSentencesCount, Favoritas: $favoritesCount');
          } catch (e) {
            debugPrint('Error al obtener estadísticas desde el adaptador: $e');
            // Usar los contadores diarios como respaldo
            readSentencesCount = sentenceVM.todayReadCount;
          }
        } else {
          // Si no hay adaptador, usar los contadores diarios
          readSentencesCount = sentenceVM.todayReadCount;
        }

        final stats = <String, dynamic>{
          'readCount': readSentencesCount,
          'favoriteCount': favoritesCount,
          'progressPercentage': sentenceVM.todayReadCount > 0 &&
                  sentenceVM.todayShownCount > 0
              ? ((sentenceVM.todayReadCount / sentenceVM.todayShownCount) * 100)
                  .toInt()
                  .toString()
              : "0",
          'todayReadCount': sentenceVM.todayReadCount,
        };

        debugPrint('إحصائيات محلية: $stats');

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: [
            StatCard(
              title: 'الجمل المقروءة',
              value: '${stats['readCount']}',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
            StatCard(
              title: 'المفضلة',
              value: '${stats['favoriteCount']}',
              icon: Icons.favorite,
              color: Colors.red,
            ),
            StatCard(
              title: 'نسبة التقدم',
              value: '${stats['progressPercentage']}%',
              icon: Icons.trending_up,
              color: Colors.blue,
            ),
            StatCard(
              title: 'اليوم',
              value: '${stats['todayReadCount']}',
              icon: Icons.today,
              color: Colors.orange,
            ),
          ],
        );
      },
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final iconSize = isVerySmallScreen ? 24.0 : (isSmallScreen ? 28.0 : 32.0);
    final valueFontSize =
        isVerySmallScreen ? 18.0 : (isSmallScreen ? 20.0 : 24.0);
    final titleFontSize =
        isVerySmallScreen ? 10.0 : (isSmallScreen ? 12.0 : 14.0);
    final verticalSpacing1 =
        isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);
    final verticalSpacing2 =
        isVerySmallScreen ? 2.0 : (isSmallScreen ? 3.0 : 4.0);

    return AppCard(
      padding: EdgeInsets.all(
          isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: iconSize,
            color: color,
          ),
          SizedBox(height: verticalSpacing1),
          Text(
            value,
            style: TextStyle(
              fontSize: valueFontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: verticalSpacing2),
          Text(
            title,
            style: TextStyle(
              fontSize: titleFontSize,
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class WeeklyActivityChart extends StatelessWidget {
  WeeklyActivityChart({super.key});

  final List<String> weekDays = [
    'Sun',
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
  ];

  @override
  Widget build(BuildContext context) {
    final sentenceVM = Provider.of<SentenceViewModel>(context, listen: false);

    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final dayLabelFontSize =
        isVerySmallScreen ? 9.0 : (isSmallScreen ? 10.0 : 12.0);
    final valueLabelFontSize =
        isVerySmallScreen ? 8.0 : (isSmallScreen ? 9.0 : 10.0);
    final barWidth = isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0);
    final reservedSize =
        isVerySmallScreen ? 20.0 : (isSmallScreen ? 25.0 : 30.0);
    final bottomPadding = isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);
    final rightPadding = isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);
    final captionFontSize =
        isVerySmallScreen ? 10.0 : (isSmallScreen ? 11.0 : 12.0);
    final verticalSpacing =
        isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);

    // Usar datos locales en lugar de datos de Firebase
    return Builder(
      builder: (context) {
        // Crear un mapa para los días de la semana
        final weeklyLog = <String, int>{for (var day in weekDays) day: 0};

        // Usar datos locales (simplificado para esta versión)
        // En una implementación real, obtendríamos estos datos del almacenamiento local
        final today = DateTime.now();

        // Asignar el valor de hoy como el número de oraciones leídas hoy
        final todayKey = weekDays[today.weekday % 7];
        weeklyLog[todayKey] = sentenceVM.todayReadCount;

        // تحديد القيمة القصوى للمحور Y
        final maxValue = weeklyLog.values.isEmpty
            ? 10
            : (weeklyLog.values.reduce((a, b) => a > b ? a : b) + 1);

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: maxValue.toDouble(),
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchCallback:
                        (FlTouchEvent event, BarTouchResponse? response) {
                      // يمكن إضافة تفاعل إضافي هنا إذا لزم الأمر
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: reservedSize,
                        getTitlesWidget: (value, meta) {
                          return Padding(
                            padding: EdgeInsets.only(top: bottomPadding),
                            child: Text(
                              weekDays[value.toInt() % 7].substring(0, 3),
                              style: TextStyle(
                                fontSize: dayLabelFontSize,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: reservedSize,
                        getTitlesWidget: (value, meta) {
                          if (value == 0) return Container();
                          if (value % 1 == 0) {
                            return Padding(
                              padding: EdgeInsets.only(right: rightPadding),
                              child: Text(
                                value.toInt().toString(),
                                style: TextStyle(
                                  fontSize: valueLabelFontSize,
                                  color: Colors.grey,
                                ),
                              ),
                            );
                          }
                          return Container();
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawHorizontalLine: true,
                    horizontalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: AppTheme.textSecondary.withAlpha(50),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: weeklyLog.entries.map((entry) {
                    return BarChartGroupData(
                      x: weekDays.indexOf(entry.key),
                      barRods: [
                        BarChartRodData(
                          toY: entry.value.toDouble(),
                          color: AppTheme.primaryColor,
                          width: barWidth,
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(4),
                          ),
                        ),
                      ],
                      showingTooltipIndicators: entry.value > 0 ? [0] : [],
                    );
                  }).toList(),
                ),
              ),
            ),
            SizedBox(height: verticalSpacing),
            Text(
              'عدد الجمل المقروءة في كل يوم من الأسبوع',
              style: TextStyle(
                fontSize: captionFontSize,
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }
}
