import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import '../models/points.dart';

class PointsProvider with ChangeNotifier {
  Points _points = Points(
    educationalPoints: 0,
    rewardPoints: 0,
    pointsSettings: {},
  );
  List<PointsRecord> _pointsHistory = [];
  bool _isLoading = false;
  String? _error;

  // الحصول على النقاط
  Points get points => _points;

  // الحصول على سجل النقاط
  List<PointsRecord> get pointsHistory => [..._pointsHistory];

  // حالة التحميل
  bool get isLoading => _isLoading;

  // رسالة الخطأ
  String? get error => _error;

  // الحصول على النقاط التعليمية
  int get educationalPoints => _points.educationalPoints;

  // الحصول على نقاط المكافأة
  int get rewardPoints => _points.rewardPoints;

  // الحصول على إعدادات النقاط
  Map<String, int> get pointsSettings => {..._points.pointsSettings};

  // جلب النقاط وإعداداتها من Firebase وتخزينها محليًا
  Future<void> fetchPoints() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من وجود بيانات محلية أولاً
      final box = await Hive.openBox('pointsBox');
      final localData = box.get('points');
      final localHistory = box.get('pointsHistory');

      if (localData != null) {
        // استخدام البيانات المحلية
        _points = Points.fromJson(jsonDecode(localData));

        if (localHistory != null) {
          final List<dynamic> historyData = jsonDecode(localHistory);
          _pointsHistory =
              historyData.map((data) => PointsRecord.fromJson(data)).toList();
        }

        _isLoading = false;
        notifyListeners();
      }

      // محاولة جلب البيانات من Firebase
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // جلب إعدادات النقاط العامة
        final settingsDoc = await FirebaseFirestore.instance
            .collection('settings')
            .doc('points')
            .get();

        Map<String, int> settings = {};
        if (settingsDoc.exists) {
          final data = settingsDoc.data() as Map<String, dynamic>;
          data.forEach((key, value) {
            if (value is num) {
              settings[key] = value.toInt();
            }
          });
        }

        // جلب نقاط المستخدم
        final userPointsDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('summary')
            .get();

        int educational = 0;
        int reward = 0;
        if (userPointsDoc.exists) {
          final data = userPointsDoc.data() as Map<String, dynamic>;
          educational = (data['educational'] as num?)?.toInt() ?? 0;
          reward = (data['reward'] as num?)?.toInt() ?? 0;
        }

        // جلب سجل النقاط
        List<PointsRecord> history = [];

        // محاولة جلب السجلات من مسارات مختلفة
        bool historyFetched = false;

        // 1. محاولة استخدام المسار الأول: users/{userId}/points/history/records
        try {
          debugPrint(
              'محاولة استخدام المسار الأول: users/${user.uid}/points/history/records');
          final historySnapshot = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('points')
              .doc('history')
              .collection('records')
              .orderBy('timestamp', descending: true)
              .limit(50)
              .get();

          if (historySnapshot.docs.isNotEmpty) {
            for (var doc in historySnapshot.docs) {
              final data = doc.data();
              history.add(PointsRecord(
                id: doc.id,
                points: (data['points'] as num).toInt(),
                type: PointTypeExtension.fromJson(data['type']),
                activity: data['activity'],
                timestamp: (data['timestamp'] as Timestamp).toDate(),
              ));
            }
            historyFetched = true;
            debugPrint('تم جلب ${history.length} سجل من المسار الأول');
          }
        } catch (e) {
          debugPrint('خطأ في جلب سجل النقاط من المسار الأول: $e');
        }

        // 2. إذا فشل المسار الأول، نحاول المسار الثاني: users/{userId}/points/summary/records
        if (!historyFetched) {
          try {
            debugPrint(
                'محاولة استخدام المسار الثاني: users/${user.uid}/points/summary/records');
            final historySnapshot = await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .collection('points')
                .doc('summary')
                .collection('records')
                .orderBy('timestamp', descending: true)
                .limit(50)
                .get();

            if (historySnapshot.docs.isNotEmpty) {
              for (var doc in historySnapshot.docs) {
                final data = doc.data();
                history.add(PointsRecord(
                  id: doc.id,
                  points: (data['points'] as num).toInt(),
                  type: PointTypeExtension.fromJson(data['type']),
                  activity: data['activity'],
                  timestamp: (data['timestamp'] as Timestamp).toDate(),
                ));
              }
              historyFetched = true;
              debugPrint('تم جلب ${history.length} سجل من المسار الثاني');
            }
          } catch (e) {
            debugPrint('خطأ في جلب سجل النقاط من المسار الثاني: $e');
          }
        }

        // 3. إذا فشلت المسارات السابقة، نحاول استخراج السجلات من وثيقة summary
        if (!historyFetched) {
          try {
            debugPrint(
                'محاولة استخراج السجلات من وثيقة summary: users/${user.uid}/points/summary');
            final summaryDoc = await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .collection('points')
                .doc('summary')
                .get();

            if (summaryDoc.exists &&
                summaryDoc.data()!.containsKey('records')) {
              final recordsArray =
                  summaryDoc.data()!['records'] as List<dynamic>;

              for (var recordData in recordsArray) {
                history.add(PointsRecord(
                  id: recordData['id'],
                  points: (recordData['points'] as num).toInt(),
                  type: PointTypeExtension.fromJson(recordData['type']),
                  activity: recordData['activity'],
                  timestamp: (recordData['timestamp'] as Timestamp).toDate(),
                ));
              }

              // ترتيب السجلات تنازلياً حسب التاريخ
              history.sort((a, b) => b.timestamp.compareTo(a.timestamp));

              // تحديد عدد السجلات إلى 50 كحد أقصى
              if (history.length > 50) {
                history = history.sublist(0, 50);
              }

              historyFetched = true;
              debugPrint('تم استخراج ${history.length} سجل من وثيقة summary');
            }
          } catch (e) {
            debugPrint('خطأ في استخراج السجلات من وثيقة summary: $e');
          }
        }

        // 4. إذا فشلت جميع المحاولات، نستخدم البيانات المحلية
        if (!historyFetched && localHistory != null) {
          debugPrint('استخدام البيانات المحلية');
          final List<dynamic> historyData = jsonDecode(localHistory);
          history =
              historyData.map((data) => PointsRecord.fromJson(data)).toList();
        }

        // تحديث النقاط وتخزينها محليًا
        _points = Points(
          educationalPoints: educational,
          rewardPoints: reward,
          pointsSettings: settings,
        );
        _pointsHistory = history;

        await box.put('points', jsonEncode(_points.toJson()));
        await box.put('pointsHistory',
            jsonEncode(_pointsHistory.map((e) => e.toJson()).toList()));
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء جلب النقاط: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إضافة نقاط للمستخدم
  Future<void> addPoints(int points, PointType type, String activity) async {
    if (points <= 0) return;

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // إنشاء سجل جديد
      final recordId = const Uuid().v4();
      final record = PointsRecord(
        id: recordId,
        points: points,
        type: type,
        activity: activity,
        timestamp: DateTime.now(),
      );

      // تحديث النقاط محليًا
      if (type == PointType.educational) {
        _points = _points.copyWith(
          educationalPoints: _points.educationalPoints + points,
        );
      } else {
        _points = _points.copyWith(
          rewardPoints: _points.rewardPoints + points,
        );
      }

      _pointsHistory.insert(0, record);

      // محاولة تحديث البيانات في Firebase (إذا فشلت، سنعتمد على التخزين المحلي)
      try {
        // استخدام عمليات منفصلة بدلاً من batch
        // 1. تحديث ملخص النقاط
        final now = DateTime.now();
        final currentTimestamp = Timestamp.fromDate(now);

        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('summary')
            .set({
          type == PointType.educational ? 'educational' : 'reward':
              FieldValue.increment(points),
          'total': FieldValue.increment(points),
          'lastUpdated': currentTimestamp,
        }, SetOptions(merge: true));

        debugPrint('تم تحديث ملخص النقاط بنجاح');

        // 2. إضافة سجل جديد في المسار الرئيسي
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('history')
            .collection('records')
            .doc(recordId)
            .set({
          'points': points,
          'type': type.toJson(),
          'activity': activity,
          'timestamp': currentTimestamp,
          'date': Timestamp.fromDate(DateTime(now.year, now.month, now.day)),
        });

        debugPrint('تم إضافة سجل النقاط بنجاح');

        // 3. تحديث وثيقة التاريخ
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('history')
            .set({
          'lastUpdated': currentTimestamp,
        }, SetOptions(merge: true));

        debugPrint('تم تحديث وثيقة التاريخ بنجاح');

        debugPrint('تم تحديث النقاط في Firebase بنجاح');
      } catch (e) {
        // تجاهل أخطاء الكتابة إلى Firestore والاعتماد على التخزين المحلي
        debugPrint('خطأ في تحديث النقاط في Firebase: $e');
        debugPrint('سيتم الاعتماد على التخزين المحلي');
      }

      // تحديث التخزين المحلي
      final box = await Hive.openBox('pointsBox');
      await box.put('points', jsonEncode(_points.toJson()));
      await box.put('pointsHistory',
          jsonEncode(_pointsHistory.map((e) => e.toJson()).toList()));

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في إضافة النقاط: $e');
    }
  }

  // استخدام نقاط المكافأة
  Future<bool> useRewardPoints(int points, String activity) async {
    if (points <= 0 || _points.rewardPoints < points) return false;

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return false;

      // إنشاء سجل جديد
      final recordId = const Uuid().v4();
      final record = PointsRecord(
        id: recordId,
        points: -points, // قيمة سالبة لأنها استخدام
        type: PointType.reward,
        activity: activity,
        timestamp: DateTime.now(),
      );

      // تحديث النقاط محليًا
      _points = _points.copyWith(
        rewardPoints: _points.rewardPoints - points,
      );

      _pointsHistory.insert(0, record);

      // محاولة تحديث البيانات في Firebase (إذا فشلت، سنعتمد على التخزين المحلي)
      try {
        // استخدام عمليات منفصلة بدلاً من batch
        // 1. تحديث ملخص النقاط
        final now = DateTime.now();
        final currentTimestamp = Timestamp.fromDate(now);

        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('summary')
            .set({
          'reward': FieldValue.increment(-points),
          'total': FieldValue.increment(-points),
          'lastUpdated': currentTimestamp,
        }, SetOptions(merge: true));

        debugPrint('تم تحديث ملخص النقاط بنجاح');

        // 2. إضافة سجل جديد في المسار الرئيسي
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('history')
            .collection('records')
            .doc(recordId)
            .set({
          'points': -points,
          'type': PointType.reward.toJson(),
          'activity': activity,
          'timestamp': currentTimestamp,
          'date': Timestamp.fromDate(DateTime(now.year, now.month, now.day)),
        });

        debugPrint('تم إضافة سجل النقاط بنجاح');

        // 3. تحديث وثيقة التاريخ
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('history')
            .set({
          'lastUpdated': currentTimestamp,
        }, SetOptions(merge: true));

        debugPrint('تم تحديث وثيقة التاريخ بنجاح');

        debugPrint('تم تحديث النقاط في Firebase بنجاح');
      } catch (e) {
        // تجاهل أخطاء الكتابة إلى Firestore والاعتماد على التخزين المحلي
        debugPrint('خطأ في تحديث النقاط في Firebase: $e');
        debugPrint('سيتم الاعتماد على التخزين المحلي');
      }

      // تحديث التخزين المحلي
      final box = await Hive.openBox('pointsBox');
      await box.put('points', jsonEncode(_points.toJson()));
      await box.put('pointsHistory',
          jsonEncode(_pointsHistory.map((e) => e.toJson()).toList()));

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في استخدام النقاط: $e');
      return false;
    }
  }

  // الحصول على قيمة النقاط لنشاط معين
  int getPointsForActivity(String activityKey) {
    return _points.pointsSettings[activityKey] ?? 0;
  }

  // الحصول على سجلات النقاط لتاريخ محدد
  Future<List<PointsRecord>> getPointsRecordsForDate(DateTime date) async {
    // تحويل التاريخ إلى بداية اليوم ونهايته
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    // التحقق من وجود مستخدم مسجل
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return [];

    debugPrint('جلب سجلات النقاط للمستخدم: ${user.uid}');
    debugPrint('تاريخ البداية: $startOfDay');
    debugPrint('تاريخ النهاية: $endOfDay');

    // محاولة جلب السجلات من Firebase
    List<PointsRecord> firebaseRecords = [];
    bool firebaseSuccess = false;

    try {
      // تحويل التواريخ إلى Timestamp لاستخدامها في استعلام Firestore
      final startTimestamp = Timestamp.fromDate(startOfDay);
      final endTimestamp = Timestamp.fromDate(endOfDay);

      // محاولة استخراج السجلات من مصفوفة records في وثيقة summary
      try {
        debugPrint(
            'محاولة استخراج السجلات من وثيقة summary: users/${user.uid}/points/summary');
        final summaryDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('summary')
            .get();

        if (summaryDoc.exists && summaryDoc.data()!.containsKey('records')) {
          final recordsArray = summaryDoc.data()!['records'] as List<dynamic>;

          // تصفية السجلات حسب التاريخ
          final filteredRecords = recordsArray.where((record) {
            if (record['timestamp'] == null) return false;

            final recordTimestamp = record['timestamp'] as Timestamp;
            return recordTimestamp.compareTo(startTimestamp) >= 0 &&
                recordTimestamp.compareTo(endTimestamp) <= 0;
          }).toList();

          if (filteredRecords.isNotEmpty) {
            debugPrint(
                'تم العثور على ${filteredRecords.length} سجل في مصفوفة records');

            // تحويل البيانات إلى كائنات PointsRecord
            for (var recordData in filteredRecords) {
              firebaseRecords.add(PointsRecord(
                id: recordData['id'],
                points: (recordData['points'] as num).toInt(),
                type: PointTypeExtension.fromJson(recordData['type']),
                activity: recordData['activity'],
                timestamp: (recordData['timestamp'] as Timestamp).toDate(),
              ));
            }

            // ترتيب السجلات تنازلياً حسب التاريخ
            firebaseRecords.sort((a, b) => b.timestamp.compareTo(a.timestamp));
            firebaseSuccess = true;
          }
        }
      } catch (e) {
        debugPrint('خطأ في استخراج السجلات من وثيقة summary: $e');
      }

      // إذا لم نجد أي سجلات في وثيقة summary، نحاول استخدام المسار الجديد
      if (!firebaseSuccess) {
        try {
          debugPrint(
              'محاولة استخدام المسار الجديد: users/${user.uid}/points/summary/records');
          final snapshot = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection('points')
              .doc('summary')
              .collection('records')
              .where('timestamp', isGreaterThanOrEqualTo: startTimestamp)
              .where('timestamp', isLessThanOrEqualTo: endTimestamp)
              .orderBy('timestamp', descending: true)
              .get();

          if (snapshot.docs.isNotEmpty) {
            debugPrint(
                'تم العثور على ${snapshot.docs.length} سجل باستخدام المسار الجديد');

            // تحويل البيانات إلى كائنات PointsRecord
            for (var doc in snapshot.docs) {
              final data = doc.data();
              firebaseRecords.add(PointsRecord(
                id: doc.id,
                points: (data['points'] as num).toInt(),
                type: PointTypeExtension.fromJson(data['type']),
                activity: data['activity'],
                timestamp: (data['timestamp'] as Timestamp).toDate(),
              ));
            }

            firebaseSuccess = true;
          }
        } catch (e) {
          debugPrint('خطأ في استخدام المسار الجديد: $e');
        }
      }
    } catch (e) {
      debugPrint('خطأ في جلب سجلات النقاط من Firebase: $e');
    }

    // إذا نجحت عملية جلب السجلات من Firebase، نعيد النتائج
    if (firebaseSuccess && firebaseRecords.isNotEmpty) {
      return firebaseRecords;
    }

    // إذا فشلت عملية جلب السجلات من Firebase، نحاول استخدام البيانات المحلية
    debugPrint('محاولة استخدام البيانات المحلية');
    try {
      final box = await Hive.openBox('pointsBox');
      final localHistory = box.get('pointsHistory');

      if (localHistory != null) {
        final List<dynamic> historyData = jsonDecode(localHistory);
        final allRecords =
            historyData.map((data) => PointsRecord.fromJson(data)).toList();

        // تصفية السجلات حسب التاريخ
        final filteredRecords = allRecords.where((record) {
          return record.timestamp.isAfter(startOfDay) &&
              record.timestamp.isBefore(endOfDay);
        }).toList();

        if (filteredRecords.isNotEmpty) {
          debugPrint(
              'تم العثور على ${filteredRecords.length} سجل في البيانات المحلية');
          return filteredRecords;
        }
      }
    } catch (localError) {
      debugPrint('خطأ في جلب سجلات النقاط المحلية: $localError');
    }

    // إذا لم نجد أي سجلات، نعيد قائمة فارغة
    debugPrint('لم يتم العثور على أي سجلات');
    return [];
  }
}
