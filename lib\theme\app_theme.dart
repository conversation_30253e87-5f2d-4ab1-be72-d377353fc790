import 'package:flutter/material.dart';

/// App theme constants and styles
class AppTheme {
  // Primary Colors
  static const Color primaryColor = Color(0xFF3DBBFF); // أزرق فاتح

  // Secondary Colors
  static const Color secondaryYellow = Color(0xFFFFD166); // أصفر عصري
  static const Color secondaryPurple = Color(0xFFB388EB); // بنفسجي خفيف

  // Background Colors
  static const Color backgroundLight = Color(0xFFF9FAFB); // رمادي شبه أبيض
  static const Color backgroundDark = Color(0xFF1E1E2E); // رمادي أزرق داكن

  // Text Colors
  static const Color textPrimary = Color(0xFF2E2E2E); // أسود خفيف
  static const Color textSecondary = Color(0xFF6B7280); // رمادي
  static const Color textLight = Color(0xFFFFFFFF); // أبيض

  // Alert Colors
  static const Color success = Color(0xFF22C55E); // أخضر حديث
  static const Color warning = Color(0xFFF59E0B); // برتقالي
  static const Color error = Color(0xFFEF4444); // أحمر ساطع

  // Card Shadow
  static List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Colors.grey.withAlpha(50), // 0.2 * 255 = 51, rounded to 50
      spreadRadius: 1,
      blurRadius: 6,
      offset: const Offset(0, 3),
    ),
  ];

  // Light Theme
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryColor,
      primary: primaryColor,
      secondary: secondaryPurple,
      surface: Colors.white,
      surfaceContainerHighest: backgroundLight,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: textPrimary,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: backgroundLight,
    cardTheme: const CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      color: Colors.white,
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      centerTitle: true,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
      ),
    ),
    textTheme: const TextTheme(
      titleLarge: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: textPrimary,
      ),
      titleMedium: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: textPrimary,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        color: textPrimary,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        color: textSecondary,
      ),
    ),
  );

  // Dark Theme
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryColor,
      primary: primaryColor,
      secondary: secondaryPurple,
      surface: const Color(0xFF2A2A3C),
      surfaceContainerHighest: backgroundDark,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: Colors.white,
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: backgroundDark,
    cardTheme: const CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      color: Color(0xFF2A2A3C),
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF2A2A3C),
      foregroundColor: Colors.white,
      centerTitle: true,
      elevation: 0,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
      ),
    ),
    textTheme: const TextTheme(
      titleLarge: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      titleMedium: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        color: Colors.white,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        color: Colors.white70,
      ),
    ),
  );
}
