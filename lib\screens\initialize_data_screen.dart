import 'package:flutter/material.dart';
import '../utils/firebase_setup.dart';

/// شاشة لتهيئة بيانات المستويات والنقاط في Firebase
class InitializeDataScreen extends StatefulWidget {
  static const routeName = '/initialize-data';

  const InitializeDataScreen({Key? key}) : super(key: key);

  @override
  State<InitializeDataScreen> createState() => _InitializeDataScreenState();
}

class _InitializeDataScreenState extends State<InitializeDataScreen> {
  bool _isLoading = false;
  String _status = '';
  final List<String> _logs = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تهيئة البيانات'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تهيئة بيانات المستويات والنقاط',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه العملية ستقوم بإنشاء بيانات المستويات والنقاط في Firebase إذا لم تكن موجودة بالفعل.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ElevatedButton(
                          onPressed: _isLoading ? null : _initializeLevelsData,
                          child: const Text('تهيئة المستويات'),
                        ),
                        ElevatedButton(
                          onPressed:
                              _isLoading ? null : _initializePointsSettings,
                          child: const Text('تهيئة النقاط'),
                        ),
                        ElevatedButton(
                          onPressed:
                              _isLoading ? null : _initializeUserProgress,
                          child: const Text('تهيئة تقدم المستخدم'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _initializeAllData,
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 48),
                      ),
                      child: const Text('تهيئة جميع البيانات'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            if (_status.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  _status,
                  style: TextStyle(
                    color: _status.contains('خطأ') ? Colors.red : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            const SizedBox(height: 8),
            const Text(
              'سجل العمليات:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(
                        _logs[index],
                        style: TextStyle(
                          color: _logs[index].contains('خطأ')
                              ? Colors.red
                              : Colors.black87,
                          fontSize: 12,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _initializeLevelsData() async {
    _setLoading(true);
    _addLog('جاري تهيئة بيانات المستويات...');

    try {
      await FirebaseSetup.setupLevelsData();
      _setStatus('تم تهيئة بيانات المستويات بنجاح');
      _addLog('تم تهيئة بيانات المستويات بنجاح');
    } catch (e) {
      _setStatus('خطأ في تهيئة بيانات المستويات: $e');
      _addLog('خطأ في تهيئة بيانات المستويات: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _initializePointsSettings() async {
    _setLoading(true);
    _addLog('جاري تهيئة إعدادات النقاط...');

    try {
      await FirebaseSetup.setupPointsSettings();
      _setStatus('تم تهيئة إعدادات النقاط بنجاح');
      _addLog('تم تهيئة إعدادات النقاط بنجاح');
    } catch (e) {
      _setStatus('خطأ في تهيئة إعدادات النقاط: $e');
      _addLog('خطأ في تهيئة إعدادات النقاط: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _initializeUserProgress() async {
    _setLoading(true);
    _addLog('جاري تهيئة تقدم المستخدم...');

    try {
      await FirebaseSetup.setupUserProgress();
      _setStatus('تم تهيئة تقدم المستخدم بنجاح');
      _addLog('تم تهيئة تقدم المستخدم بنجاح');
    } catch (e) {
      _setStatus('خطأ في تهيئة تقدم المستخدم: $e');
      _addLog('خطأ في تهيئة تقدم المستخدم: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _initializeAllData() async {
    _setLoading(true);
    _addLog('جاري تهيئة جميع البيانات...');

    try {
      await FirebaseSetup.setupAllData();
      _setStatus('تم تهيئة جميع البيانات بنجاح');
      _addLog('تم تهيئة جميع البيانات بنجاح');
    } catch (e) {
      _setStatus('خطأ في تهيئة جميع البيانات: $e');
      _addLog('خطأ في تهيئة جميع البيانات: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    setState(() {
      _isLoading = loading;
    });
  }

  void _setStatus(String status) {
    setState(() {
      _status = status;
    });
  }

  void _addLog(String log) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} - $log');
    });
  }
}
