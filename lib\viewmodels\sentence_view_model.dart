import 'package:flutter/material.dart';
import 'package:test05/viewmodels/auth_view_model.dart';
import '../models/sentence_model.dart';
import '../services/firestore_service.dart';
import '../services/file_cache_service.dart';
import '../services/sentence_service.dart';
import '../services/native_notification_service.dart';
import '../services/local_sentence_storage_service.dart';
import '../services/hive_sentence_service.dart';
import '../services/sync_manager.dart';
import '../services/daily_sentence_service.dart';
import '../services/daily_sentences_manager.dart';
import '../adapters/sentence_view_model_adapter.dart';
import '../adapters/daily_sentences_manager_adapter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class SentenceViewModel extends ChangeNotifier {
  final FirestoreService _firestoreService;
  final FileCacheService _cacheService;
  final SentenceService _sentenceService;
  final AuthViewModel _authViewModel;
  late final LocalSentenceStorageService _localStorageService;
  SentenceViewModelAdapter? _adapter;
  DailySentencesManagerAdapter? _dailyAdapter;

  /// الحصول على المحول
  SentenceViewModelAdapter? get adapter => _adapter;

  /// الحصول على محول مدير الجمل اليومية
  DailySentencesManagerAdapter? get dailyAdapter => _dailyAdapter;

  List<SentenceModel> _sentences = [];
  List<SentenceModel> _favoriteSentences = [];
  List<SentenceModel> _dailyRandomSentences = [];
  List<String> _categories = [];
  String _selectedCategory = '';
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  String _error = '';
  final bool _isSyncing = false;
  bool _needsManualRefresh =
      false; // Flag to track if user needs to manually refresh

  SentenceViewModel(this._firestoreService, this._cacheService,
      this._sentenceService, this._authViewModel,
      {LocalSentenceStorageService? localStorageService,
      HiveSentenceService? hiveSentenceService,
      SyncManager? syncManager,
      DailySentenceService? dailySentenceService,
      DailySentencesManager? dailySentencesManager}) {
    _localStorageService = localStorageService ?? LocalSentenceStorageService();

    // إذا تم توفير HiveSentenceService و SyncManager و DailySentenceService، قم بتهيئة المحول
    if (hiveSentenceService != null &&
        syncManager != null &&
        dailySentenceService != null) {
      _adapter = SentenceViewModelAdapter(
          hiveSentenceService, syncManager, dailySentenceService);
      debugPrint('تم تهيئة SentenceViewModelAdapter');
    }

    // إذا تم توفير DailySentencesManager، قم بتهيئة محول مدير الجمل اليومية
    if (dailySentencesManager != null) {
      _dailyAdapter = DailySentencesManagerAdapter(dailySentencesManager);
      debugPrint('تم تهيئة DailySentencesManagerAdapter');
    }

    _loadInitialData();
  }

  // Getters
  List<SentenceModel> get sentences => _sentences;
  List<SentenceModel> get favoriteSentences => _favoriteSentences;
  List<SentenceModel> get dailyRandomSentences => _dailyRandomSentences;
  List<String> get categories => _categories;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String get error => _error;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  bool get isSyncing => _isSyncing;
  bool get needsManualRefresh => _needsManualRefresh;
  bool get isOffline =>
      false; // Always return false as we're removing offline functionality

  // إضافة خصائص جديدة لتتبع عدد الجمل المقروءة والمعروضة اليوم
  int _todayReadCount = 0;
  int _todayShownCount = 0;

  int get todayReadCount => _todayReadCount;
  int get todayShownCount => _todayShownCount;

  // تحديث عدد الجمل المقروءة اليوم
  Future<void> updateTodayReadCount() async {
    try {
      // الحصول على العدد من التخزين المحلي
      final localReadCount = await _localStorageService.getTodayReadCount();
      _todayReadCount = localReadCount;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating today read count: $e');
    }
  }

  // تحديث عدد الجمل المعروضة اليوم
  Future<void> updateTodayShownCount() async {
    try {
      // الحصول على العدد من التخزين المحلي
      final localShownCount = await _localStorageService.getTodayShownCount();
      _todayShownCount = localShownCount;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating today shown count: $e');
    }
  }

  // الحصول على الجمل المقروءة في تاريخ محدد
  Future<List<SentenceModel>> getReadSentencesForDate(DateTime date) async {
    try {
      // التحقق مما إذا كان المستخدم مصادقًا عليه
      if (!_authViewModel.isAuthenticated) {
        return [];
      }

      // تحويل التاريخ إلى بداية اليوم ونهايته
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      // استخدام Firestore مباشرة
      final userId = _authViewModel.user!.uid;
      final snapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .where('readAt', isGreaterThanOrEqualTo: startOfDay)
          .where('readAt', isLessThanOrEqualTo: endOfDay)
          .get();

      // الحصول على معرفات الجمل المقروءة
      final sentenceIds = snapshot.docs.map((doc) => doc.id).toList();

      // الحصول على تفاصيل الجمل
      final List<SentenceModel> readSentences = [];
      for (final sentenceId in sentenceIds) {
        final sentenceDoc = await FirebaseFirestore.instance
            .collection('sentences')
            .doc(sentenceId)
            .get();

        if (sentenceDoc.exists) {
          final sentenceData = sentenceDoc.data()!;
          readSentences.add(SentenceModel.fromMap(
            sentenceData,
            sentenceId,
            isReadByCurrentUser: true,
          ));
        }
      }

      return readSentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل المقروءة للتاريخ المحدد: $e');
      return [];
    }
  }

  // تحميل عدد الجمل المقروءة والمعروضة اليوم من التخزين المحلي
  Future<void> loadTodayCountsFromStorage() async {
    try {
      if (_adapter != null) {
        // استخدام المحول للحصول على الإحصائيات من Hive
        _todayReadCount = _adapter!.getTodayReadCount();
        _todayShownCount = _adapter!.getTodayShownCount();

        // طباعة معلومات تصحيح
        debugPrint(
            'تم تحميل الإحصائيات من Hive: قراءة=$_todayReadCount, عرض=$_todayShownCount');

        try {
          // محاولة الحصول على عدد الجمل المقروءة والمفضلة
          final readSentences = _adapter!.getReadSentences();
          final favoriteSentences = _adapter!.getFavoriteSentences();

          debugPrint(
              'إجمالي المقروءة=${readSentences.length}, إجمالي المفضلة=${favoriteSentences.length}');
        } catch (e) {
          debugPrint('خطأ في الحصول على عدد الجمل المقروءة والمفضلة: $e');
        }
      } else {
        // استخدام التخزين المحلي القديم
        final localReadCount = await _localStorageService.getTodayReadCount();
        _todayReadCount = localReadCount;

        final localShownCount = await _localStorageService.getTodayShownCount();
        _todayShownCount = localShownCount;

        debugPrint(
            'تم تحميل الإحصائيات من التخزين المحلي: قراءة=$_todayReadCount, عرض=$_todayShownCount');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات من التخزين المحلي: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _hasError = true;
    notifyListeners();
  }

  Future<void> _loadInitialData() async {
    _setLoading(true);
    try {
      debugPrint('Loading initial data');
      await Future.wait([
        _loadSentences(),
        _loadCategories(),
      ]);

      // تهيئة المفضلة
      initializeFavorites();

      // تحميل الجمل اليومية
      if (_authViewModel.isAuthenticated) {
        debugPrint('User authenticated, loading daily sentences');
        // استخدام تأخير صغير للتأكد من تهيئة كل شيء
        await Future.delayed(const Duration(milliseconds: 500));
        await loadRandomDailySentences();
      } else {
        debugPrint('User not authenticated, daily sentences not loaded');
      }
    } catch (e) {
      _setError(e.toString());
      debugPrint('Error loading initial data: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadSentences() async {
    try {
      final sentences = await _firestoreService.getSentences();
      _sentences = sentences;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sentences: $e');
    }
  }

  // Método público para cargar oraciones
  Future<void> loadSentences({bool forceRefresh = false}) async {
    try {
      _setLoading(true);
      final sentences = await _firestoreService.getSentences();
      _sentences = sentences;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sentences: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _firestoreService.getCategories();
      _categories = categories;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load categories: $e');
    }
  }

  // Método público para cargar categorías
  Future<void> loadCategories() async {
    try {
      _setLoading(true);
      final categories = await _firestoreService.getCategories();
      _categories = categories;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> markSentenceAsRead(String sentenceId, String userId) async {
    try {
      await _firestoreService.markSentenceAsRead(sentenceId, userId);
      _sentences = _sentences.map((sentence) {
        if (sentence.id == sentenceId) {
          final updatedReadBy = Map<String, dynamic>.from(sentence.readBy)
            ..[userId] = DateTime.now();
          return sentence.copyWith(readBy: updatedReadBy);
        }
        return sentence;
      }).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to mark sentence as read: $e');
    }
  }

  // هذه الطريقة لم تعد تستخدم مباشرة، نستخدم toggleFavorite بدلاً منها
  // تم الاحتفاظ بها للتوافق مع الكود القديم
  Future<bool> toggleSentenceFavorite(String sentenceId) async {
    try {
      // استخدام خدمة الجمل لتحديث مجموعة المفضلة للمستخدم فقط
      await _sentenceService.toggleFavorite(
          sentenceId, _authViewModel.user!.uid);

      // تحقق مما إذا كانت الجملة في المفضلة حاليًا
      final isFavorite = await _checkIsFavorite(sentenceId);

      // تحديث حالة الجملة في جميع القوائم
      _updateSentenceFavoriteStatus(sentenceId, isFavorite);

      // إشعار المستمعين بالتغيير
      notifyListeners();

      return isFavorite;
    } catch (e) {
      _setError('Failed to toggle favorite: $e');
      return false;
    }
  }

  Future<bool> _checkIsFavorite(String sentenceId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('favorites')
          .doc(sentenceId)
          .get();

      return doc.exists;
    } catch (e) {
      debugPrint('Error checking favorite status: $e');
      return false;
    }
  }

  void _updateSentenceFavoriteStatus(String sentenceId, bool isFavorite) {
    // تحديث في قائمة الجمل العادية
    final sentenceIndex = _sentences.indexWhere((s) => s.id == sentenceId);
    if (sentenceIndex != -1) {
      _sentences[sentenceIndex].isFavoriteByCurrentUser = isFavorite;
    }

    // تحديث في قائمة الجمل اليومية
    final dailyIndex =
        _dailyRandomSentences.indexWhere((s) => s.id == sentenceId);
    if (dailyIndex != -1) {
      _dailyRandomSentences[dailyIndex].isFavoriteByCurrentUser = isFavorite;
    }

    // تحديث في قائمة الجمل المقروءة (لا نحتاج لتحديثها هنا لأنها تُحدث عند الحصول عليها)

    // تحديث قائمة المفضلة
    if (isFavorite) {
      // إذا لم تكن الجملة موجودة بالفعل في المفضلة، أضفها
      if (!_favoriteSentences.any((s) => s.id == sentenceId)) {
        // ابحث عن الجملة في القوائم الأخرى
        SentenceModel? sentence;
        if (sentenceIndex != -1) {
          sentence = _sentences[sentenceIndex];
        } else if (dailyIndex != -1) {
          sentence = _dailyRandomSentences[dailyIndex];
        }

        if (sentence != null) {
          _favoriteSentences
              .add(sentence.copyWith(isFavoriteByCurrentUser: true));
        }
      }
    } else {
      // إذا كانت الجملة موجودة في المفضلة، أزلها
      _favoriteSentences.removeWhere((s) => s.id == sentenceId);
    }
  }

  Future<List<SentenceModel>> searchSentences(String query) async {
    if (query.isEmpty) return [];
    try {
      final sentences = await _firestoreService.searchSentences(query);
      return sentences;
    } catch (e) {
      _setError(e.toString());
      return [];
    }
  }

  Future<void> loadSentencesByCategory(String category) async {
    try {
      _setLoading(true);
      final sentences =
          await _firestoreService.getSentencesByCategory(category);
      _sentences = sentences;
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sentences by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  void initializeFavorites() {
    if (_authViewModel.isAuthenticated) {
      _sentenceService
          .getFavoriteSentences(_authViewModel.user!.uid)
          .listen((sentences) {
        _favoriteSentences = sentences;
        notifyListeners();
      });
    }
  }

  // دالة للتحقق من اليوم الجديد وتحديث الجمل تلقائيًا إذا لزم الأمر
  Future<void> checkForNewDayAndUpdate() async {
    try {
      debugPrint('Checking for new day...');

      // التحقق مما إذا كان المستخدم مصادقًا عليه
      if (!_authViewModel.isAuthenticated) {
        debugPrint('User not authenticated, skipping new day check');
        return;
      }

      // التحقق مما إذا كان اليوم جديدًا باستخدام التخزين المحلي
      final isNewDay = await _localStorageService.isNewDayLocally();

      // التحقق مما إذا كانت قائمة الجمل فارغة تمامًا (كل الجمل مقروءة)
      // استخدام التخزين المحلي بدلاً من الاعتماد على _dailyRandomSentences
      final isEmptyList = await _localStorageService.isDailySentencesEmpty();

      debugPrint('Is new day: $isNewDay, Is empty list: $isEmptyList');

      // حتى لو كان اليوم جديدًا، لا نقوم بتحديث الجمل تلقائيًا
      // بدلاً من ذلك، نعين علامة التحديث اليدوي لإجبار المستخدم على الضغط على زر "10 Again"
      if (isNewDay) {
        debugPrint('New day detected, setting manual refresh flag');
        _needsManualRefresh = true; // تعيين علامة التحديث اليدوي

        // جدولة إشعار تحفيزي ليوم جديد
        final notificationService = NativeNotificationService();
        await notificationService.showNewDayMotivationNotification();
      } else if (isNewDay && !isEmptyList) {
        // إذا كان اليوم جديدًا ولكن القائمة غير فارغة، جدولة إشعار تذكير صباحي
        final notificationService = NativeNotificationService();
        await notificationService.showMorningReminderNotification();

        // تحديث تاريخ آخر تحديث في التخزين المحلي
        await _localStorageService.updateLastUpdateDate();
      } else if (!isEmptyList) {
        // إذا كانت القائمة غير فارغة، جدولة إشعار تذكير بالجمل غير المكتملة
        final notificationService = NativeNotificationService();
        await notificationService.showUnfinishedSentencesNotification();
      } else {
        debugPrint('No auto-refresh needed');
      }

      // تحديث _dailyRandomSentences من التخزين المحلي
      final localSentences =
          await _localStorageService.getLocalDailySentences();
      if (localSentences.isNotEmpty) {
        _dailyRandomSentences =
            localSentences.where((s) => !s.isReadByCurrentUser).toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error checking for new day: $e');
    }
  }

  /// تعيين الجمل اليومية العشوائية
  void setDailyRandomSentences(List<dynamic> sentences) {
    if (sentences.isEmpty) return;

    if (_adapter != null) {
      // تحويل جميع الجمل بدون تصفية
      _dailyRandomSentences =
          sentences.map((s) => _adapter!.toSentenceModel(s)).toList();

      // طباعة معلومات تصحيح
      debugPrint(
          'تم تعيين ${_dailyRandomSentences.length} جملة في _dailyRandomSentences');
      for (var sentence in _dailyRandomSentences) {
        debugPrint(
            'الجملة ${sentence.id}: مقروءة=${sentence.isReadByCurrentUser}');
      }
    } else {
      // إذا لم يكن المحول متاحًا، نستخدم قائمة فارغة
      _dailyRandomSentences = [];
    }

    notifyListeners();
  }

  Future<void> loadRandomDailySentences() async {
    try {
      debugPrint('بدء تحميل الجمل اليومية');
      _isLoading = true;
      notifyListeners();

      // التحقق مما إذا كان المستخدم مصادقًا عليه
      if (!_authViewModel.isAuthenticated) {
        debugPrint('المستخدم غير مصادق عليه');
        return;
      }

      // استخدام محول مدير الجمل اليومية إذا كان متاحًا
      if (_dailyAdapter != null) {
        debugPrint('استخدام محول مدير الجمل اليومية لتحميل الجمل اليومية');

        // الحصول على الجمل اليومية غير المقروءة
        final unreadSentences = _dailyAdapter!.getUnreadDailySentences();

        // التحقق مما إذا كان المستخدم بحاجة إلى تحديث يدوي
        if (_needsManualRefresh || unreadSentences.isEmpty) {
          debugPrint('المستخدم بحاجة إلى تحديث يدوي أو جميع الجمل مقروءة');

          // التحقق مما إذا كان اليوم جديدًا
          final isNewDay = _dailyAdapter!.isNewDay();

          if (isNewDay) {
            debugPrint('تم اكتشاف يوم جديد، جلب جمل جديدة');
            // إذا كان يوم جديد، نحصل على جمل جديدة
            final newSentences = await _dailyAdapter!
                .getNewDailySentences(10, userId: _authViewModel.user?.uid);
            _dailyRandomSentences = newSentences;
            _needsManualRefresh = false; // إعادة تعيين علامة التحديث اليدوي
          } else {
            debugPrint(
                'ليس يومًا جديدًا، يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على المزيد');
            // إذا لم يكن يوم جديد، نعرض الجمل المحلية غير المقروءة (إن وجدت)
            _dailyRandomSentences = unreadSentences;
            _needsManualRefresh = true; // تعيين علامة التحديث اليدوي
          }
        } else {
          // إذا كانت هناك جمل محلية غير مقروءة، نستخدمها
          debugPrint(
              'استخدام ${unreadSentences.length} جملة غير مقروءة من التخزين المحلي');
          _dailyRandomSentences = unreadSentences;
        }

        // تحديث عدد الجمل المقروءة والمعروضة
        _todayReadCount = _dailyAdapter!.getTodayReadCount();
        _todayShownCount = _dailyAdapter!.getTodayShownCount();
      }
      // استخدام المحول القديم إذا كان متاحًا
      else if (_adapter != null) {
        // استخدام المحول للحصول على جميع الجمل اليومية (بما في ذلك المقروءة)
        final hiveSentences = _adapter!.getDailySentences();

        // التحقق مما إذا كان المستخدم بحاجة إلى تحديث يدوي
        if (_needsManualRefresh || hiveSentences.isEmpty) {
          debugPrint('المستخدم بحاجة إلى تحديث يدوي أو جميع الجمل مقروءة');

          // لا نحتاج إلى التحقق مما إذا كان اليوم جديدًا لأننا نريد دائمًا تحديثًا يدويًا

          // حتى لو كان اليوم جديدًا، يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على جمل جديدة
          debugPrint(
              'يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على جمل جديدة');
          // نعرض الجمل المحلية غير المقروءة (إن وجدت)
          _dailyRandomSentences = hiveSentences;
          _needsManualRefresh = true; // تعيين علامة التحديث اليدوي

          // تحديث عدد الجمل المقروءة والمعروضة
          _todayReadCount = _adapter!.getTodayReadCount();
          _todayShownCount = _adapter!.getTodayShownCount();
        } else {
          // إذا كانت هناك جمل محلية غير مقروءة، نستخدمها
          debugPrint(
              'استخدام ${hiveSentences.length} جملة غير مقروءة من التخزين المحلي');
          _dailyRandomSentences = hiveSentences;

          // تحديث عدد الجمل المقروءة والمعروضة
          _todayReadCount = _adapter!.getTodayReadCount();
          _todayShownCount = _adapter!.getTodayShownCount();
        }
      } else {
        // الطريقة القديمة
        // أولاً، نحاول تحميل الجمل من التخزين المحلي
        final localSentences =
            await _localStorageService.getLocalDailySentences();

        // التحقق مما إذا كان المستخدم بحاجة إلى تحديث يدوي
        final hasUnreadLocalSentences =
            localSentences.any((s) => !s.isReadByCurrentUser);

        // إذا كان المستخدم بحاجة إلى تحديث يدوي أو كانت جميع الجمل المحلية مقروءة
        if (_needsManualRefresh || !hasUnreadLocalSentences) {
          debugPrint('المستخدم بحاجة إلى تحديث يدوي أو جميع الجمل مقروءة');

          // تحقق مما إذا كان اليوم جديدًا
          final isNewDay = await _localStorageService.isNewDayLocally();

          if (isNewDay) {
            debugPrint('تم اكتشاف يوم جديد، فرض التحديث');
            // إذا كان يوم جديد، نحصل على جمل جديدة
            final newSentences = await _sentenceService.getUnreadDailySentences(
                _authViewModel.user!.uid, 10,
                forceRefresh: true);
            _dailyRandomSentences = newSentences;
            _needsManualRefresh = false; // إعادة تعيين علامة التحديث اليدوي
          } else {
            debugPrint(
                'ليس يومًا جديدًا، يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على المزيد');
            // إذا لم يكن يوم جديد، نعرض الجمل المحلية غير المقروءة (إن وجدت)
            _dailyRandomSentences =
                localSentences.where((s) => !s.isReadByCurrentUser).toList();
            _needsManualRefresh = true; // تعيين علامة التحديث اليدوي
          }

          _isLoading = false;
          notifyListeners();
          return;
        }

        // إذا كانت هناك جمل محلية غير مقروءة، نستخدمها
        if (hasUnreadLocalSentences) {
          final unreadLocalSentences =
              localSentences.where((s) => !s.isReadByCurrentUser).toList();
          debugPrint(
              'استخدام ${unreadLocalSentences.length} جملة غير مقروءة من التخزين المحلي');
          _dailyRandomSentences = unreadLocalSentences;
          _isLoading = false;
          notifyListeners();
          return;
        }

        // إذا وصلنا إلى هنا، فهذا يعني أنه لا توجد جمل محلية غير مقروءة
        // ولا يحتاج المستخدم إلى تحديث يدوي، لذا نحاول الحصول على جمل من Firestore
        final sentences = await _sentenceService.getUnreadDailySentences(
            _authViewModel.user!.uid, 10,
            forceRefresh: false);

        debugPrint('تم تحميل الجمل من الخدمة: ${sentences.length}');

        if (sentences.isEmpty) {
          // إذا لم تكن هناك جمل من Firestore، نعين علامة التحديث اليدوي
          debugPrint(
              'لا توجد جمل من Firestore، يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على المزيد');
          _needsManualRefresh = true;
        } else {
          // إذا كانت هناك جمل من Firestore، نستخدمها
          _dailyRandomSentences = sentences;
        }
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      debugPrint('خطأ في تحميل الجمل اليومية: $e');

      // في حالة الخطأ، نحاول استخدام الجمل المحلية
      try {
        if (_dailyAdapter != null) {
          // استخدام محول مدير الجمل اليومية للحصول على الجمل المحلية
          final sentences = _dailyAdapter!.getUnreadDailySentences();
          if (sentences.isNotEmpty) {
            _dailyRandomSentences = sentences;
            _errorMessage = ''; // مسح رسالة الخطأ إذا وجدنا جمل محلية
            _hasError = false;
            debugPrint(
                'تم الاسترداد باستخدام ${_dailyRandomSentences.length} جملة محلية من محول مدير الجمل اليومية');
          }
        } else if (_adapter != null) {
          // استخدام المحول القديم للحصول على الجمل المحلية
          final hiveSentences = _adapter!.getUnreadDailySentences();
          if (hiveSentences.isNotEmpty) {
            _dailyRandomSentences = hiveSentences;
            _errorMessage = ''; // مسح رسالة الخطأ إذا وجدنا جمل محلية
            _hasError = false;
            debugPrint(
                'تم الاسترداد باستخدام ${_dailyRandomSentences.length} جملة محلية من المحول القديم');
          }
        } else {
          // الطريقة القديمة
          final localSentences =
              await _localStorageService.getLocalDailySentences();
          if (localSentences.isNotEmpty) {
            _dailyRandomSentences =
                localSentences.where((s) => !s.isReadByCurrentUser).toList();
            if (_dailyRandomSentences.isNotEmpty) {
              _errorMessage = ''; // مسح رسالة الخطأ إذا وجدنا جمل محلية
              _hasError = false;
              debugPrint(
                  'تم الاسترداد باستخدام ${_dailyRandomSentences.length} جملة محلية');
            }
          }
        }
      } catch (localError) {
        debugPrint('خطأ في تحميل الجمل المحلية: $localError');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshRandomSentences() async {
    try {
      // استدعاء forceRefreshRandomSentences مع معالجة الأخطاء
      await forceRefreshRandomSentences();
    } catch (e) {
      // في حالة فشل التحديث، نسجل الخطأ ولكن لا نغير حالة الواجهة
      debugPrint('Error in refreshRandomSentences: $e');
      // لا نقوم بتعيين _hasError أو _errorMessage هنا لتجنب إظهار رسائل خطأ للمستخدم
      // عند فشل التحديث التلقائي
    }
  }

  // طريقة لفرض تحديث الجمل اليومية
  Future<bool> forceRefreshRandomSentences() async {
    bool hasError = false;

    _isLoading = true;
    _errorMessage = ''; // مسح أي رسالة خطأ سابقة
    _needsManualRefresh = false; // Reset the manual refresh flag
    notifyListeners();

    try {
      debugPrint('Forcing update of daily sentences');

      // التحقق مما إذا كان المستخدم مصادقًا عليه
      if (!_authViewModel.isAuthenticated) {
        debugPrint('User not authenticated');
        _errorMessage = 'يجب تسجيل الدخول لعرض الجمل اليومية';
        hasError = true;
        return hasError;
      }

      // استخدام محول مدير الجمل اليومية إذا كان متاحًا
      if (_dailyAdapter != null) {
        debugPrint('Using DailySentencesManagerAdapter to get new sentences');

        // التحقق مما إذا كانت جميع الجمل اليومية مقروءة
        final allRead = _dailyAdapter!.areAllDailySentencesRead();

        if (!allRead && !_needsManualRefresh) {
          debugPrint(
              'There are still unread sentences, please read them first');
          _errorMessage = 'يرجى إكمال قراءة الجمل الحالية أولاً';

          // تحديث _dailyRandomSentences من المحول
          _dailyRandomSentences = _dailyAdapter!.getUnreadDailySentences();

          hasError = true;
          return hasError;
        }

        // الحصول على جمل جديدة
        final newSentences = await _dailyAdapter!
            .getNewDailySentences(10, userId: _authViewModel.user?.uid);

        if (newSentences.isEmpty) {
          debugPrint(
              'No new sentences were obtained from DailySentencesManager');
          _errorMessage =
              'لا توجد جمل جديدة للقراءة. لقد قرأت جميع الجمل المتاحة.';
          _needsManualRefresh =
              false; // لا حاجة للتحديث اليدوي لأنه لا توجد جمل متاحة
        } else {
          // تحديث قائمة الجمل اليومية
          _dailyRandomSentences = newSentences;

          // تحديث عدد الجمل المقروءة والمعروضة
          _todayReadCount = _dailyAdapter!.getTodayReadCount();
          _todayShownCount = _dailyAdapter!.getTodayShownCount();

          debugPrint(
              'Successfully obtained ${newSentences.length} new sentences');
          _errorMessage = '';
        }

        return _errorMessage.isNotEmpty;
      }

      // استخدام المحول القديم إذا كان متاحًا
      else if (_adapter != null) {
        debugPrint('Using old adapter to get new sentences');

        // ... (resto del código existente)
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      debugPrint('Error getting sentences: $e');
      hasError = true;
    } finally {
      _isLoading = false;
      notifyListeners();
    }

    return hasError;
  }

  /// طريقة لفرض تحديث الجمل اليومية بجمل جديدة تمامًا
  Future<bool> forceRefreshWithNewSentences() async {
    bool hasError = false;

    _isLoading = true;
    _errorMessage = ''; // مسح أي رسالة خطأ سابقة
    notifyListeners();

    try {
      debugPrint('Forcing update with completely new sentences');

      // التحقق مما إذا كان المستخدم مصادقًا عليه
      if (!_authViewModel.isAuthenticated) {
        debugPrint('User not authenticated');
        _errorMessage = 'يجب تسجيل الدخول لعرض الجمل اليومية';
        hasError = true;
        return hasError;
      }

      // استخدام محول مدير الجمل اليومية إذا كان متاحًا
      if (_dailyAdapter != null) {
        debugPrint(
            'Using DailySentencesManagerAdapter to force get new sentences');

        // الحصول على جمل جديدة بشكل قسري
        final newSentences = await _dailyAdapter!
            .forceGetNewDailySentences(10, userId: _authViewModel.user?.uid);

        if (newSentences.isEmpty) {
          debugPrint(
              'No new sentences were obtained from forceGetNewDailySentences');
          _errorMessage =
              'لا توجد جمل جديدة للقراءة. يرجى المحاولة مرة أخرى لاحقًا.';
          hasError = true;
        } else {
          // تحديث قائمة الجمل اليومية
          _dailyRandomSentences = newSentences;

          // تحديث عدد الجمل المقروءة والمعروضة
          _todayReadCount = _dailyAdapter!.getTodayReadCount();
          _todayShownCount = _dailyAdapter!.getTodayShownCount();

          // إعادة تعيين حالة التحديث اليدوي
          _needsManualRefresh = false;

          debugPrint(
              'Successfully obtained ${newSentences.length} completely new sentences');
          _errorMessage = '';
        }
      }
      // استخدام المحول القديم إذا كان متاحًا
      else if (_adapter != null) {
        debugPrint('Using old adapter to get new sentences');

        // التحقق مما إذا كانت جميع الجمل اليومية مقروءة
        final allRead = _adapter!.areAllDailySentencesRead();

        if (!allRead && !_needsManualRefresh) {
          debugPrint(
              'There are still unread sentences, please read them first');
          _errorMessage = 'يرجى إكمال قراءة الجمل الحالية أولاً';

          // تحديث _dailyRandomSentences من المحول
          _dailyRandomSentences = _adapter!.getUnreadDailySentences();

          hasError = true;
          return hasError;
        }

        // الحصول على جمل جديدة
        final newSentences =
            await _adapter!.getMoreSentences(_authViewModel.user!.uid);

        if (newSentences.isEmpty) {
          debugPrint('No new sentences were obtained from old adapter');
          _errorMessage =
              'لا توجد جمل جديدة للقراءة. لقد قرأت جميع الجمل المتاحة.';
          _needsManualRefresh =
              false; // لا حاجة للتحديث اليدوي لأنه لا توجد جمل متاحة
        } else {
          // تحديث قائمة الجمل اليومية
          _dailyRandomSentences = newSentences;

          // تحديث عدد الجمل المقروءة والمعروضة
          _todayReadCount = _adapter!.getTodayReadCount();
          _todayShownCount = _adapter!.getTodayShownCount();

          debugPrint(
              'Successfully obtained ${newSentences.length} new sentences');
          _errorMessage = '';
        }
      }
      // استخدام الطريقة القديمة
      else {
        // التحقق مما إذا كانت هناك جمل غير مقروءة في التخزين المحلي
        final hasUnreadSentences =
            await _localStorageService.hasUnreadSentencesLocally();

        if (hasUnreadSentences && !_needsManualRefresh) {
          debugPrint('There are still unread sentences in local storage');
          _errorMessage = 'يرجى إكمال قراءة الجمل الحالية أولاً';

          // تحديث _dailyRandomSentences من التخزين المحلي
          final localSentences =
              await _localStorageService.getLocalDailySentences();
          _dailyRandomSentences =
              localSentences.where((s) => !s.isReadByCurrentUser).toList();

          hasError = true;
          return hasError;
        }

        // التحقق من حالة الاتصال بالإنترنت
        final isConnected = await _sentenceService.isOnline();

        if (!isConnected) {
          debugPrint('No internet connection, trying to use local storage');

          // في حالة عدم وجود اتصال بالإنترنت، نحاول استخدام التخزين المحلي
          // حتى لو كانت جميع الجمل مقروءة، نعيد استخدامها
          final localSentences =
              await _localStorageService.getLocalDailySentences();

          if (localSentences.isNotEmpty) {
            // إعادة تعيين حالة القراءة لجميع الجمل المحلية
            for (var sentence in localSentences) {
              sentence.isReadByCurrentUser = false;
            }

            // تحديث التخزين المحلي بالجمل المعاد تعيينها
            await _localStorageService.storeLocalDailySentences(localSentences);

            // تحديث قائمة الجمل اليومية
            _dailyRandomSentences = localSentences;

            // إعادة تعيين علامة التحديث اليدوي
            _needsManualRefresh = false;

            // تحديث تاريخ آخر تحديث في التخزين المحلي
            await _localStorageService.updateLastUpdateDate();

            // زيادة عداد الجمل المعروضة اليوم (10 جمل فقط)
            await _localStorageService.incrementTodayShownCount(10);

            // تحديث عدد الجمل المعروضة والمقروءة
            await updateTodayShownCount();
            await updateTodayReadCount();

            debugPrint(
                'Reset read status for ${localSentences.length} local sentences');
            _errorMessage = '';
            return false;
          } else {
            _errorMessage =
                'لا توجد جمل محلية متاحة. سيتم تحميل جمل جديدة عند عودة الاتصال بالإنترنت.';
            hasError = true;
            return hasError;
          }
        }

        // حذف جميع الجمل الموجودة في التخزين المحلي
        await _localStorageService.clearDailySentences();
        debugPrint('Cleared local daily sentences');

        // الحصول على جمل جديدة
        final sentences = await _sentenceService.getUnreadDailySentences(
            _authViewModel.user!.uid, 10,
            forceRefresh: true);

        debugPrint('Sentences obtained from service: ${sentences.length}');

        // تحديث قائمة الجمل اليومية
        _dailyRandomSentences = sentences;

        // إذا لم تكن هناك جمل جديدة، تعيين رسالة الخطأ
        if (_dailyRandomSentences.isEmpty) {
          debugPrint('No new sentences were obtained');
          _errorMessage =
              'لا توجد جمل جديدة للقراءة. لقد قرأت جميع الجمل المتاحة.';
          _needsManualRefresh =
              false; // لا حاجة للتحديث اليدوي لأنه لا توجد جمل متاحة
        } else {
          // إذا تم الحصول على جمل جديدة، مسح رسالة الخطأ
          _errorMessage = '';
          debugPrint(
              'Sentences successfully obtained: ${_dailyRandomSentences.length}');
          for (var sentence in _dailyRandomSentences) {
            debugPrint(
                'Sentence ID: ${sentence.id}, Read: ${sentence.isReadByCurrentUser}');
          }

          // تحديث عدد الجمل المعروضة والمقروءة
          await updateTodayShownCount();
          await updateTodayReadCount();

          debugPrint('Updated today shown count: $_todayShownCount');

          // تحديث تاريخ آخر تحديث في التخزين المحلي
          await _localStorageService.updateLastUpdateDate();
          debugPrint('Last update date updated in local storage');
        }
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      debugPrint('Error getting sentences: $e');

      // في حالة الخطأ، نحاول استخدام الجمل المحلية
      if (_dailyAdapter != null) {
        try {
          // محاولة الحصول على الجمل الحالية
          final currentSentences = _dailyAdapter!.getDailySentences();
          if (currentSentences.isNotEmpty) {
            _dailyRandomSentences = currentSentences;
            _errorMessage = ''; // مسح رسالة الخطأ إذا وجدنا جمل محلية
            _hasError = false;
          }
        } catch (localError) {
          debugPrint(
              'Error getting current sentences from adapter: $localError');
        }
      } else {
        try {
          final localSentences =
              await _localStorageService.getLocalDailySentences();
          if (localSentences.isNotEmpty) {
            // إعادة تعيين حالة القراءة لجميع الجمل المحلية
            for (var sentence in localSentences) {
              sentence.isReadByCurrentUser = false;
            }

            // تحديث التخزين المحلي بالجمل المعاد تعيينها
            await _localStorageService.storeLocalDailySentences(localSentences);

            // تحديث قائمة الجمل اليومية
            _dailyRandomSentences = localSentences;

            // تحديث عدد الجمل المعروضة والمقروءة
            await updateTodayShownCount();
            await updateTodayReadCount();

            _errorMessage = ''; // مسح رسالة الخطأ إذا وجدنا جمل محلية
            _hasError = false;
          }
        } catch (localError) {
          debugPrint('Error getting local sentences: $localError');
        }
      }

      hasError = _errorMessage.isNotEmpty;
    } finally {
      _isLoading = false;
      notifyListeners();
    }

    return hasError;
  }

  Future<void> markAsRead(SentenceModel sentence) async {
    if (!_authViewModel.isAuthenticated) return;

    debugPrint('تعليم الجملة كمقروءة: ${sentence.id}');

    try {
      // تحديث حالة الجملة كمقروءة محليًا أولاً
      sentence.isReadByCurrentUser = true;

      // تحقق مما إذا كانت هذه هي الجملة الأخيرة
      // نحتاج إلى استبعاد الجملة الحالية من العد لأنها تم تعليمها كمقروءة بالفعل
      final unreadSentences = _dailyRandomSentences
          .where((s) => !s.isReadByCurrentUser && s.id != sentence.id)
          .toList();
      final isLastSentence = unreadSentences.isEmpty;

      debugPrint('الجمل غير المقروءة المتبقية: ${unreadSentences.length}');

      // إشعار المستمعين بالتغييرات المحلية (قبل إزالة الجملة)
      notifyListeners();

      // استخدام محول مدير الجمل اليومية إذا كان متاحًا، ثم المحول القديم، وإلا استخدام الطريقة القديمة
      if (_dailyAdapter != null) {
        // استخدام محول مدير الجمل اليومية لتعليم الجملة كمقروءة
        final result = await _dailyAdapter!.markSentenceAsRead(sentence);
        debugPrint(
            'تم تعليم الجملة كمقروءة باستخدام محول مدير الجمل اليومية: ${sentence.id}, النتيجة: $result');

        // تحديث عدد الجمل المقروءة اليوم
        _todayReadCount = _dailyAdapter!.getTodayReadCount();
      } else if (_adapter != null) {
        // استخدام المحول القديم لتعليم الجملة كمقروءة (يستخدم HiveSentenceService)
        await _adapter!.markSentenceAsRead(sentence);
        debugPrint(
            'تم تعليم الجملة كمقروءة باستخدام المحول القديم: ${sentence.id}');

        // تحديث عدد الجمل المقروءة اليوم
        _todayReadCount = _adapter!.getTodayReadCount();
      } else {
        // الطريقة القديمة
        final isConnected = await _sentenceService.isOnline();

        if (isConnected) {
          // إذا كان هناك اتصال بالإنترنت، تحديث البيانات في Firestore
          await _sentenceService.markSentenceAsRead(
              _authViewModel.user!.uid, sentence.id);
          debugPrint('تم تعليم الجملة كمقروءة في Firestore: ${sentence.id}');
        } else {
          // إذا لم يكن هناك اتصال بالإنترنت، تحديث البيانات محليًا فقط
          await _localStorageService.markSentenceAsReadLocally(sentence.id);
          debugPrint('تم تعليم الجملة كمقروءة محليًا فقط: ${sentence.id}');
        }

        // تحديث عدد الجمل المقروءة اليوم
        await _localStorageService.incrementTodayReadCount();
        await updateTodayReadCount();
      }

      // إذا كانت الجملة الأخيرة، نعين علامة التحديث اليدوي
      if (isLastSentence) {
        debugPrint(
            'هذه هي الجملة الأخيرة. يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على جمل جديدة');
        _needsManualRefresh = true; // نحتاج إلى تحديث يدوي
      }

      // إزالة الجملة من قائمة الجمل اليومية المعروضة
      _dailyRandomSentences.removeWhere((s) => s.id == sentence.id);

      notifyListeners();

      debugPrint('تم تعليم الجملة كمقروءة بنجاح: ${sentence.id}');
    } catch (e) {
      // في حالة فشل الاتصال، نحاول تحديث الحالة محليًا فقط
      debugPrint('خطأ في تعليم الجملة كمقروءة: $e');

      try {
        // تحديث الجملة في التخزين المحلي
        if (_dailyAdapter != null) {
          await _dailyAdapter!.markSentenceAsRead(sentence);
        } else if (_adapter != null) {
          await _adapter!.markSentenceAsRead(sentence);
        } else {
          await _localStorageService.markSentenceAsReadLocally(sentence.id);
        }

        // تحقق مما إذا كانت هذه هي الجملة الأخيرة
        final unreadSentences = _dailyRandomSentences
            .where((s) => !s.isReadByCurrentUser && s.id != sentence.id)
            .toList();
        final isLastSentence = unreadSentences.isEmpty;

        // إزالة الجملة من قائمة الجمل اليومية المعروضة
        _dailyRandomSentences.removeWhere((s) => s.id == sentence.id);

        // تحديث عدد الجمل المقروءة اليوم
        if (_dailyAdapter != null) {
          _todayReadCount = _dailyAdapter!.getTodayReadCount();
        } else if (_adapter != null) {
          _todayReadCount = _adapter!.getTodayReadCount();
        } else {
          await _localStorageService.incrementTodayReadCount();
          await updateTodayReadCount();
        }

        // إذا كانت الجملة الأخيرة، نعين علامة التحديث اليدوي
        if (isLastSentence) {
          debugPrint(
              'هذه هي الجملة الأخيرة. يجب على المستخدم الضغط على "10 مرة أخرى" للحصول على جمل جديدة');
          _needsManualRefresh = true; // نحتاج إلى تحديث يدوي
        }

        notifyListeners();

        debugPrint('تم تعليم الجملة كمقروءة محليًا بعد الخطأ: ${sentence.id}');
      } catch (localError) {
        debugPrint('خطأ في تحديث التخزين المحلي: $localError');
      }
    }
  }

  Future<Map<String, dynamic>> getLocalStats() async {
    try {
      return await _sentenceService.getLocalStats();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
      return {};
    }
  }

  // الحصول على جميع الجمل
  Future<List<SentenceModel>> getAllSentences() async {
    try {
      return await _sentenceService.getAllSentences();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
      return [];
    }
  }

  // إضافة دالة للوصول إلى SentenceService
  SentenceService getSentenceService() {
    return _sentenceService;
  }

  Future<void> addSentence(
      String englishText, String arabicText, String category,
      {String? difficulty}) async {
    try {
      final sentence = SentenceModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        englishText: englishText,
        arabicText: arabicText,
        category: category,
        createdAt: DateTime.now(),
        readBy: {},
        isFavorite: false,
        difficulty:
            difficulty ?? 'medium', // إضافة حقل مستوى الصعوبة مع قيمة افتراضية
      );

      await _firestoreService.addSentence(sentence);
      _sentences.add(sentence);
      notifyListeners();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> updateSentence(SentenceModel sentence) async {
    try {
      await _firestoreService.updateSentence(sentence.id, sentence);
      final index = _sentences.indexWhere((s) => s.id == sentence.id);
      if (index != -1) {
        _sentences[index] = sentence;
        notifyListeners();
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<void> deleteSentence(String id) async {
    try {
      await _firestoreService.deleteSentence(id);
      _sentences.removeWhere((s) => s.id == id);
      notifyListeners();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  Future<String> calculateCacheSize() async {
    try {
      final totalSize = await _cacheService.getCacheSize();
      return _cacheService.formatFileSize(totalSize);
    } catch (e) {
      return '0 B';
    }
  }

  Future<void> addCategory(String text) async {
    try {
      // Add the category to Firestore
      await FirebaseFirestore.instance.collection('categories').add({
        'name': text,
        'createdAt': FieldValue.serverTimestamp(),
      });
      await loadCategories();
    } catch (e) {
      _hasError = true;
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  void toggleFavorite(SentenceModel sentence) async {
    if (!_authViewModel.isAuthenticated) return;

    // تحديد الحالة الجديدة للمفضلة (عكس الحالة الحالية)
    final newFavoriteStatus = !sentence.isFavoriteByCurrentUser;

    // تحديث حالة الجملة محليًا أولاً لتحسين تجربة المستخدم
    sentence.isFavoriteByCurrentUser = newFavoriteStatus;

    // تحديث حالة الجملة في جميع القوائم محليًا
    _updateSentenceFavoriteStatusInLists(sentence.id, newFavoriteStatus);

    // إشعار المستمعين بالتغييرات المحلية
    notifyListeners();

    try {
      // استخدام المحول إذا كان متاحًا، وإلا استخدام الطريقة القديمة
      if (_adapter != null) {
        // استخدام المحول لتبديل حالة المفضلة (يستخدم HiveSentenceService)
        await _adapter!.toggleFavorite(sentence);
        debugPrint(
            'تم تبديل حالة المفضلة باستخدام المحول: ${sentence.id}, isFavorite: $newFavoriteStatus');
      } else {
        // الطريقة القديمة - محاولة تحديث البيانات في Firestore
        await _sentenceService.toggleFavorite(
            sentence.id, _authViewModel.user!.uid);
        debugPrint(
            'تم تحديث حالة المفضلة في Firestore: ${sentence.id}, isFavorite: $newFavoriteStatus');
      }
    } catch (e) {
      // في حالة فشل الاتصال، نسجل الخطأ ولكن لا نعيد الحالة السابقة
      _hasError = true;
      _errorMessage =
          'فشل في تحديث حالة المفضلة. يرجى التحقق من اتصالك بالإنترنت.';
      debugPrint('خطأ في تحديث حالة المفضلة: $e');

      // إشعار المستمعين بحدوث خطأ، ولكن بدون تغيير حالة الواجهة
      notifyListeners();
    }
  }

  // Este método ya está implementado arriba, lo eliminamos para evitar duplicados

  Future<void> clearLocalData([String? selectedLanguage]) async {
    try {
      _setLoading(true);
      await _sentenceService.clearLocalData(selectedLanguage ?? 'ar');
      _sentences = [];
      _favoriteSentences = [];
      _dailyRandomSentences = [];
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> syncWithServer() async {
    try {
      _setLoading(true);

      // مزامنة الجمل المعلقة مع قاعدة البيانات
      if (_authViewModel.isAuthenticated) {
        await _sentenceService.syncPendingSentences(_authViewModel.user!.uid);
      }

      await loadSentences(forceRefresh: true);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// جلب جميع الجمل المتاحة من Firebase وتخزينها محليًا
  /// يعيد عدد الجمل التي تم تخزينها
  Future<int> fetchAllAvailableSentences() async {
    try {
      _isLoading = true;
      _errorMessage = '';
      notifyListeners();

      if (!_authViewModel.isAuthenticated) {
        _errorMessage = 'يجب تسجيل الدخول لتحميل جميع الجمل';
        return 0;
      }

      // استخدام محول مدير الجمل اليومية إذا كان متاحًا
      if (_dailyAdapter != null) {
        debugPrint('جاري تحميل جميع الجمل المتاحة...');
        final count = await _dailyAdapter!.fetchAllAvailableSentences(
          userId: _authViewModel.user!.uid,
        );

        if (count > 0) {
          debugPrint('تم تحميل $count جملة جديدة بنجاح');
          _errorMessage = 'تم تحميل $count جملة جديدة بنجاح';
          return count;
        } else {
          _errorMessage = 'لم يتم العثور على جمل جديدة للتحميل';
          return 0;
        }
      } else {
        _errorMessage = 'لا يمكن تحميل جميع الجمل في هذا الإصدار';
        return 0;
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'حدث خطأ أثناء تحميل الجمل: ${e.toString()}';
      debugPrint('خطأ في تحميل جميع الجمل: $e');
      return 0;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Este método ya está implementado arriba, lo eliminamos para evitar duplicados

  Stream<List<SentenceModel>> getReadSentences({
    int limit = 10,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    bool descending = true,
  }) {
    try {
      if (!_authViewModel.isAuthenticated) {
        return Stream.value([]);
      }

      // Get the user's read sentences from the readSentences collection
      Query<Map<String, dynamic>> query = FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('readSentences');

      // Apply date filters if provided
      if (startDate != null) {
        query = query.where('readAt',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        // Add one day to include the end date fully
        final nextDay = endDate.add(const Duration(days: 1));
        query = query.where('readAt', isLessThan: Timestamp.fromDate(nextDay));
      }

      // Order by read date
      query = query.orderBy('readAt', descending: descending);

      // Apply limit
      query = query.limit(limit);

      // Get the read sentences and then fetch the actual sentence data
      return query.snapshots().asyncMap((snapshot) async {
        if (snapshot.docs.isEmpty) {
          return [];
        }

        // Get all sentence IDs
        final sentenceIds = snapshot.docs.map((doc) => doc.id).toList();

        // Fetch all sentences in one batch
        final sentencesQuery = await FirebaseFirestore.instance
            .collection('sentences')
            .where(FieldPath.documentId, whereIn: sentenceIds)
            .get();

        // Create a map of sentence ID to read timestamp
        final readTimestamps = <String, Timestamp>{};
        for (var doc in snapshot.docs) {
          readTimestamps[doc.id] = doc.data()['readAt'] as Timestamp;
        }

        // Create sentence models with read timestamps
        final sentences = sentencesQuery.docs.map((doc) {
          final data = doc.data();
          final sentence =
              SentenceModel.fromMap(data, doc.id, isReadByCurrentUser: true);
          return sentence;
        }).toList();

        // Filter by category if provided
        if (category != null && category.isNotEmpty) {
          sentences.removeWhere((s) => s.category != category);
        }

        // Sort by read timestamp (already done in the query, but we need to maintain the order)
        sentences.sort((a, b) {
          final aTimestamp = readTimestamps[a.id];
          final bTimestamp = readTimestamps[b.id];
          if (aTimestamp == null || bTimestamp == null) return 0;
          return descending
              ? bTimestamp.compareTo(aTimestamp)
              : aTimestamp.compareTo(bTimestamp);
        });

        return sentences;
      });
    } catch (e) {
      debugPrint('Error getting read sentences: $e');
      _setError(e.toString());
      return Stream.value([]);
    }
  }

  // Método para obtener las frases leídas como una lista (no como un stream)
  Future<List<SentenceModel>> getReadSentencesAsList({
    int limit = 10,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? difficulty, // إضافة حقل مستوى الصعوبة
    bool descending = true,
    Timestamp? lastReadAt,
    List<String>? excludeIds,
  }) async {
    try {
      if (!_authViewModel.isAuthenticated) {
        return [];
      }

      // Get the user's read sentences from the readSentences collection
      Query<Map<String, dynamic>> query = FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('readSentences');

      // Apply date filters if provided
      if (startDate != null) {
        query = query.where('readAt',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        // Add one day to include the end date fully
        final nextDay = endDate.add(const Duration(days: 1));
        query = query.where('readAt', isLessThan: Timestamp.fromDate(nextDay));
      }

      // Apply pagination using lastReadAt if provided
      if (lastReadAt != null) {
        if (descending) {
          // If descending, get items with readAt less than lastReadAt
          query = query.where('readAt', isLessThan: lastReadAt);
        } else {
          // If ascending, get items with readAt greater than lastReadAt
          query = query.where('readAt', isGreaterThan: lastReadAt);
        }
      }

      // Order by read date
      query = query.orderBy('readAt', descending: descending);

      // Apply limit
      query = query.limit(limit);

      // Execute the query
      final snapshot = await query.get();

      if (snapshot.docs.isEmpty) {
        return [];
      }

      // Get all sentence IDs
      final sentenceIds = snapshot.docs.map((doc) => doc.id).toList();

      // Exclude IDs if provided
      if (excludeIds != null && excludeIds.isNotEmpty) {
        sentenceIds.removeWhere((id) => excludeIds.contains(id));
      }

      if (sentenceIds.isEmpty) {
        return [];
      }

      // Fetch all sentences in one batch
      final sentencesQuery = await FirebaseFirestore.instance
          .collection('sentences')
          .where(FieldPath.documentId, whereIn: sentenceIds)
          .get();

      // Create a map of sentence ID to read timestamp
      final readTimestamps = <String, Timestamp>{};
      for (var doc in snapshot.docs) {
        if (sentenceIds.contains(doc.id)) {
          // Only include non-excluded IDs
          readTimestamps[doc.id] = doc.data()['readAt'] as Timestamp;
        }
      }

      // Create sentence models with read timestamps
      final sentences = sentencesQuery.docs.map((doc) {
        final data = doc.data();
        final sentence =
            SentenceModel.fromMap(data, doc.id, isReadByCurrentUser: true);
        return sentence;
      }).toList();

      // Filter by category if provided
      if (category != null && category.isNotEmpty) {
        sentences.removeWhere((s) => s.category != category);
      }

      // Filter by difficulty if provided
      if (difficulty != null && difficulty.isNotEmpty) {
        sentences.removeWhere((s) => s.difficulty != difficulty);
      }

      // Sort by read timestamp
      sentences.sort((a, b) {
        final aTimestamp = readTimestamps[a.id];
        final bTimestamp = readTimestamps[b.id];
        if (aTimestamp == null || bTimestamp == null) return 0;
        return descending
            ? bTimestamp.compareTo(aTimestamp)
            : aTimestamp.compareTo(bTimestamp);
      });

      return sentences;
    } catch (e) {
      debugPrint('Error getting read sentences as list: $e');
      _setError(e.toString());
      return [];
    }
  }

  // Método para obtener el timestamp de lectura de una frase
  Future<Timestamp?> getReadTimestamp(String sentenceId) async {
    try {
      if (!_authViewModel.isAuthenticated) {
        return null;
      }

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('readSentences')
          .doc(sentenceId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return doc.data()?['readAt'] as Timestamp?;
    } catch (e) {
      debugPrint('Error getting read timestamp: $e');
      return null;
    }
  }

  void setSelectedCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  // Este método ya está implementado arriba, lo eliminamos para evitar duplicados

  Stream<List<SentenceModel>> getSentencesByCategory() {
    if (_selectedCategory.isEmpty) {
      return Stream.value([]);
    }

    return FirebaseFirestore.instance
        .collection('sentences')
        .where('category', isEqualTo: _selectedCategory)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SentenceModel.fromMap(doc.data(), doc.id))
            .toList());
  }

  // تحديث حالة المفضلة للجملة في جميع القوائم المحلية
  void _updateSentenceFavoriteStatusInLists(
      String sentenceId, bool isFavorite) {
    try {
      // تحديث في قائمة الجمل اليومية
      for (var sentence in _dailyRandomSentences) {
        if (sentence.id == sentenceId) {
          sentence.isFavoriteByCurrentUser = isFavorite;
        }
      }

      // تحديث في قائمة الجمل المفضلة
      if (isFavorite) {
        // إذا كانت الجملة موجودة في قائمة الجمل، أضفها إلى المفضلة
        SentenceModel? sentenceToAdd;

        try {
          sentenceToAdd = _dailyRandomSentences.firstWhere(
            (s) => s.id == sentenceId,
          );
        } catch (e) {
          try {
            sentenceToAdd = _sentences.firstWhere(
              (s) => s.id == sentenceId,
            );
          } catch (e) {
            // إذا لم يتم العثور على الجملة، إنشاء نموذج جديد
            sentenceToAdd = SentenceModel(
              id: sentenceId,
              arabicText: '',
              englishText: '',
              category: '',
              createdAt: DateTime.now(),
              readBy: {},
              isFavorite: false,
              isFavoriteByCurrentUser: true,
            );
          }
        }

        // تحقق مما إذا كانت الجملة موجودة بالفعل في المفضلة
        final existingIndex =
            _favoriteSentences.indexWhere((s) => s.id == sentenceId);
        if (existingIndex == -1) {
          _favoriteSentences.add(sentenceToAdd);
        } else {
          _favoriteSentences[existingIndex].isFavoriteByCurrentUser = true;
        }
      } else {
        // إذا كانت الجملة غير مفضلة، أزلها من قائمة المفضلة
        _favoriteSentences.removeWhere((s) => s.id == sentenceId);
      }

      // تحديث في قائمة جميع الجمل
      for (var sentence in _sentences) {
        if (sentence.id == sentenceId) {
          sentence.isFavoriteByCurrentUser = isFavorite;
        }
      }
    } catch (e) {
      debugPrint('Error in _updateSentenceFavoriteStatusInLists: $e');
    }
  }

  Future<List<SentenceModel>> getSentencesByCategoryAsList(
      String category) async {
    if (category.isEmpty) {
      return [];
    }

    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('sentences')
          .where('category', isEqualTo: category)
          .get();

      // الحصول على الجمل المقروءة والمفضلة للمستخدم الحالي
      final readSentenceIds = <String>{};
      final favoriteSentenceIds = <String>{};

      if (_authViewModel.isAuthenticated) {
        final readSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(_authViewModel.user!.uid)
            .collection('readSentences')
            .get();

        readSentenceIds.addAll(readSnapshot.docs.map((doc) => doc.id));

        final favoriteSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(_authViewModel.user!.uid)
            .collection('favorites')
            .get();

        favoriteSentenceIds.addAll(favoriteSnapshot.docs.map((doc) => doc.id));
      }

      return snapshot.docs.map((doc) {
        final isRead = readSentenceIds.contains(doc.id);
        final isFavorite = favoriteSentenceIds.contains(doc.id);
        return SentenceModel.fromMap(
          doc.data(),
          doc.id,
          isReadByCurrentUser: isRead,
          isFavoriteByCurrentUser: isFavorite,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error getting sentences by category: $e');
      return [];
    }
  }

  Future<Map<String, int>> getCategorySentenceCounts() async {
    try {
      final result = <String, int>{};

      // الحصول على عدد الجمل لكل فئة
      for (final category in _categories) {
        final snapshot = await FirebaseFirestore.instance
            .collection('sentences')
            .where('category', isEqualTo: category)
            .get();

        result[category] = snapshot.size;
      }

      return result;
    } catch (e) {
      debugPrint('Error getting category sentence counts: $e');
      return {};
    }
  }

  Future<int> getTotalSentencesCount() async {
    try {
      final snapshot =
          await FirebaseFirestore.instance.collection('sentences').get();

      return snapshot.size;
    } catch (e) {
      debugPrint('Error getting total sentences count: $e');
      return 0;
    }
  }

  // متغيرات لتخزين آخر مستند وآخر مؤشر تم تحميله
  DocumentSnapshot? _lastLoadedDocument;
  int? _lastLoadedIndex;

  // دالة لإعادة تعيين آخر مستند وآخر مؤشر تم تحميله
  void resetLastLoadedDocument() {
    _lastLoadedDocument = null;
    _lastLoadedIndex = null;
  }

  Future<List<SentenceModel>> getAllSentencesAsList({
    int limit = 20,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? difficulty, // إضافة حقل مستوى الصعوبة
    bool descending = true,
    DocumentSnapshot? lastDocument,
    List<String>? excludeIds,
    bool resetPagination = false,
  }) async {
    try {
      // إعادة تعيين آخر مستند إذا تم طلب ذلك
      if (resetPagination) {
        _lastLoadedDocument = null;
      }

      // استخدام التخزين المحلي (Hive) إذا كان المحول متاحًا
      if (_dailyAdapter != null) {
        debugPrint('استخدام التخزين المحلي (Hive) للحصول على جميع الجمل');

        // الحصول على جميع الجمل من التخزين المحلي
        List<SentenceModel> allSentences = [];

        // استخدام محول مدير الجمل اليومية للحصول على جميع الجمل
        // هذا يستخدم نفس التخزين المحلي الذي تستخدمه الجمل اليومية
        allSentences = _dailyAdapter!.getAllSentences();

        debugPrint(
            'تم العثور على ${allSentences.length} جملة في التخزين المحلي');

        // إذا لم تكن هناك جمل في التخزين المحلي، نحاول تحميل جميع الجمل من Firebase
        if (allSentences.isEmpty && _authViewModel.isAuthenticated) {
          debugPrint('لا توجد جمل في التخزين المحلي، جلب الجمل من Firebase');

          // تحميل جميع الجمل من Firebase وتخزينها محليًا
          final count = await fetchAllAvailableSentences();

          if (count > 0) {
            // إعادة محاولة الحصول على الجمل من التخزين المحلي بعد التحميل
            allSentences = _dailyAdapter!.getAllSentences();
            debugPrint('تم تحميل ${allSentences.length} جملة من Firebase');
          }
        }

        // تطبيق الفلاتر على الجمل
        List<SentenceModel> filteredSentences = allSentences;

        // تطبيق فلتر الفئة
        if (category != null && category.isNotEmpty) {
          filteredSentences =
              filteredSentences.where((s) => s.category == category).toList();
        }

        // تطبيق فلتر مستوى الصعوبة
        if (difficulty != null && difficulty.isNotEmpty) {
          filteredSentences = filteredSentences
              .where((s) => s.difficulty == difficulty)
              .toList();
        }

        // تطبيق فلاتر التاريخ
        if (startDate != null) {
          filteredSentences = filteredSentences
              .where((s) =>
                  s.createdAt.isAfter(startDate) ||
                  s.createdAt.isAtSameMomentAs(startDate))
              .toList();
        }

        if (endDate != null) {
          final nextDay = endDate.add(const Duration(days: 1));
          filteredSentences = filteredSentences
              .where((s) => s.createdAt.isBefore(nextDay))
              .toList();
        }

        // ترتيب حسب تاريخ الإنشاء
        filteredSentences.sort((a, b) => descending
            ? b.createdAt.compareTo(a.createdAt)
            : a.createdAt.compareTo(b.createdAt));

        // استبعاد المعرفات إذا تم توفيرها
        if (excludeIds != null && excludeIds.isNotEmpty) {
          filteredSentences.removeWhere((s) => excludeIds.contains(s.id));
        }

        // تطبيق الحد والتصفح
        int startIndex = 0;

        // إذا كان هناك تصفح، نحدد نقطة البداية
        if (!resetPagination && _lastLoadedIndex != null) {
          startIndex = _lastLoadedIndex! + 1;
        }

        // التأكد من أن نقطة البداية ضمن النطاق
        if (startIndex >= filteredSentences.length) {
          return [];
        }

        // تحديد نقطة النهاية
        int endIndex = startIndex + limit;
        if (endIndex > filteredSentences.length) {
          endIndex = filteredSentences.length;
        }

        // تخزين آخر مؤشر للاستخدام في الصفحة التالية
        _lastLoadedIndex = endIndex - 1;

        // إرجاع الجمل المفلترة والمحدودة
        return filteredSentences.sublist(startIndex, endIndex);
      }

      // إذا لم يكن المحول متاحًا، نستخدم Firebase مباشرة (الطريقة القديمة)
      // تعليق: الطريقة القديمة التي تستخدم Firebase مباشرة
      debugPrint('استخدام Firebase مباشرة للحصول على جميع الجمل');

      // الحصول على جميع الجمل من مجموعة sentences
      Query<Map<String, dynamic>> query =
          FirebaseFirestore.instance.collection('sentences');

      // تطبيق فلتر الفئة إذا تم توفيره
      if (category != null && category.isNotEmpty) {
        query = query.where('category', isEqualTo: category);
      }

      // تطبيق فلتر مستوى الصعوبة إذا تم توفيره
      if (difficulty != null && difficulty.isNotEmpty) {
        query = query.where('difficulty', isEqualTo: difficulty);
      }

      // تطبيق فلاتر التاريخ إذا تم توفيرها
      if (startDate != null) {
        query = query.where('createdAt',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        // إضافة يوم واحد لتضمين تاريخ النهاية بالكامل
        final nextDay = endDate.add(const Duration(days: 1));
        query =
            query.where('createdAt', isLessThan: Timestamp.fromDate(nextDay));
      }

      // ترتيب حسب تاريخ الإنشاء
      query = query.orderBy('createdAt', descending: descending);

      // تطبيق التصفح باستخدام آخر مستند
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      } else if (_lastLoadedDocument != null) {
        query = query.startAfterDocument(_lastLoadedDocument!);
      }

      // تطبيق الحد
      query = query.limit(limit);

      // تنفيذ الاستعلام
      final snapshot = await query.get();

      if (snapshot.docs.isEmpty) {
        return [];
      }

      // تخزين آخر مستند للاستخدام في الصفحة التالية
      if (snapshot.docs.isNotEmpty) {
        _lastLoadedDocument = snapshot.docs.last;
      }

      // الحصول على الجمل المقروءة والمفضلة للمستخدم الحالي
      final readSentenceIds = <String>{};
      final favoriteSentenceIds = <String>{};

      if (_authViewModel.isAuthenticated) {
        final readSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(_authViewModel.user!.uid)
            .collection('readSentences')
            .get();

        readSentenceIds.addAll(readSnapshot.docs.map((doc) => doc.id));

        final favoriteSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(_authViewModel.user!.uid)
            .collection('favorites')
            .get();

        favoriteSentenceIds.addAll(favoriteSnapshot.docs.map((doc) => doc.id));
      }

      // إنشاء نماذج الجمل
      final sentences = snapshot.docs.map((doc) {
        final isRead = readSentenceIds.contains(doc.id);
        final isFavorite = favoriteSentenceIds.contains(doc.id);
        return SentenceModel.fromMap(
          doc.data(),
          doc.id,
          isReadByCurrentUser: isRead,
          isFavoriteByCurrentUser: isFavorite,
        );
      }).toList();

      // استبعاد المعرفات إذا تم توفيرها
      if (excludeIds != null && excludeIds.isNotEmpty) {
        sentences.removeWhere((s) => excludeIds.contains(s.id));
      }

      return sentences;
    } catch (e) {
      debugPrint('Error getting all sentences as list: $e');
      return [];
    }
  }

  bool isFavorite(String id) {
    return _favoriteSentences.any((s) => s.id == id);
  }

  // Método para obtener las frases favoritas como una lista (no como un stream)
  Future<List<SentenceModel>> getFavoriteSentencesAsList({
    int limit = 10,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? difficulty, // إضافة حقل مستوى الصعوبة
    bool descending = true,
    Timestamp? lastTimestamp,
    List<String>? excludeIds,
  }) async {
    try {
      if (!_authViewModel.isAuthenticated) {
        return [];
      }

      // Get the user's favorite sentences from the favorites collection
      Query<Map<String, dynamic>> query = FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('favorites');

      // Apply date filters if provided
      if (startDate != null) {
        query = query.where('timestamp',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        // Add one day to include the end date fully
        final nextDay = endDate.add(const Duration(days: 1));
        query =
            query.where('timestamp', isLessThan: Timestamp.fromDate(nextDay));
      }

      // Apply pagination using lastTimestamp if provided
      if (lastTimestamp != null) {
        if (descending) {
          // If descending, get items with timestamp less than lastTimestamp
          query = query.where('timestamp', isLessThan: lastTimestamp);
        } else {
          // If ascending, get items with timestamp greater than lastTimestamp
          query = query.where('timestamp', isGreaterThan: lastTimestamp);
        }
      }

      // Order by timestamp
      query = query.orderBy('timestamp', descending: descending);

      // Apply limit
      query = query.limit(limit);

      // Execute the query
      final snapshot = await query.get();

      if (snapshot.docs.isEmpty) {
        return [];
      }

      // Get all sentence IDs
      final sentenceIds = snapshot.docs.map((doc) => doc.id).toList();

      // Exclude IDs if provided
      if (excludeIds != null && excludeIds.isNotEmpty) {
        sentenceIds.removeWhere((id) => excludeIds.contains(id));
      }

      if (sentenceIds.isEmpty) {
        return [];
      }

      // Fetch all sentences in one batch
      final sentencesQuery = await FirebaseFirestore.instance
          .collection('sentences')
          .where(FieldPath.documentId, whereIn: sentenceIds)
          .get();

      // Create a map of sentence ID to favorite timestamp
      final favoriteTimestamps = <String, Timestamp>{};
      for (var doc in snapshot.docs) {
        if (sentenceIds.contains(doc.id)) {
          // Only include non-excluded IDs
          favoriteTimestamps[doc.id] = doc.data()['timestamp'] as Timestamp;
        }
      }

      // Create sentence models with favorite timestamps
      final sentences = sentencesQuery.docs.map((doc) {
        final data = doc.data();
        final sentence =
            SentenceModel.fromMap(data, doc.id, isFavoriteByCurrentUser: true);
        return sentence;
      }).toList();

      // Filter by category if provided
      if (category != null && category.isNotEmpty) {
        sentences.removeWhere((s) => s.category != category);
      }

      // Filter by difficulty if provided
      if (difficulty != null && difficulty.isNotEmpty) {
        sentences.removeWhere((s) => s.difficulty != difficulty);
      }

      // Sort by favorite timestamp
      sentences.sort((a, b) {
        final aTimestamp = favoriteTimestamps[a.id];
        final bTimestamp = favoriteTimestamps[b.id];
        if (aTimestamp == null || bTimestamp == null) return 0;
        return descending
            ? bTimestamp.compareTo(aTimestamp)
            : aTimestamp.compareTo(bTimestamp);
      });

      return sentences;
    } catch (e) {
      debugPrint('Error getting favorite sentences as list: $e');
      // Manejar el error sin usar _setError
      _hasError = true;
      _errorMessage = e.toString();
      return [];
    }
  }

  // Método para obtener el timestamp de favorito de una frase
  Future<Timestamp?> getFavoriteTimestamp(String sentenceId) async {
    try {
      if (!_authViewModel.isAuthenticated) {
        return null;
      }

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_authViewModel.user!.uid)
          .collection('favorites')
          .doc(sentenceId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return doc.data()?['timestamp'] as Timestamp?;
    } catch (e) {
      debugPrint('Error getting favorite timestamp: $e');
      return null;
    }
  }
}
