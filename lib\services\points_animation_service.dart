import 'package:flutter/material.dart';
import '../models/points.dart';
import '../widgets/points_animation.dart';

/// خدمة لعرض تأثيرات النقاط في أي مكان في التطبيق
class PointsAnimationService {
  static final PointsAnimationService _instance = PointsAnimationService._internal();
  
  factory PointsAnimationService() => _instance;
  
  PointsAnimationService._internal();
  
  OverlayEntry? _overlayEntry;
  
  /// عرض تأثير النقاط
  void showPointsAnimation(
    BuildContext context,
    int points,
    PointType type,
  ) {
    // إزالة أي تأثير سابق
    _removeOverlay();
    
    // إنشاء تأثير جديد
    _overlayEntry = OverlayEntry(
      builder: (context) => PointsAnimation(
        points: points,
        type: type,
        onComplete: () {
          // إزالة التأثير بعد الانتهاء
          Future.delayed(const Duration(milliseconds: 500), () {
            _removeOverlay();
          });
        },
      ),
    );
    
    // إضافة التأثير إلى الشاشة
    Overlay.of(context).insert(_overlayEntry!);
  }
  
  /// إزالة التأثير
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
