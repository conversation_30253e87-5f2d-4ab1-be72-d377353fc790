import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:encrypt/encrypt.dart';
import 'sentence_service.dart';

class DataExportService {
  static const String _exportFileName = 'sentences_backup';
  final SentenceService _sentenceService;
  final Key _encryptionKey = Key.fromLength(32);
  final IV _iv = IV.fromLength(16);

  DataExportService(this._sentenceService);

  Future<String> exportData(String userId) async {
    try {
      // جمع البيانات المحلية
      final data = _sentenceService.exportLocalData(userId, {});

      // تشفير البيانات
      final encrypter = Encrypter(AES(_encryptionKey));
      final jsonData = json.encode(data);
      final encrypted = encrypter.encrypt(jsonData, iv: _iv);

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/${_exportFileName}_$timestamp.enc';

      final file = File(filePath);
      await file.writeAsBytes(encrypted.bytes);

      return filePath;
    } catch (e) {
      throw Exception('فشل في تصدير البيانات: $e');
    }
  }

  Future<void> importData(String userId, String filePath) async {
    try {
      // قراءة الملف المشفر
      final file = File(filePath);
      final encrypted = Encrypted(await file.readAsBytes());

      // فك التشفير
      final encrypter = Encrypter(AES(_encryptionKey));
      final decrypted = encrypter.decrypt(encrypted, iv: _iv);

      // تحويل البيانات
      final data = json.decode(decrypted) as Map<String, dynamic>;

      // استيراد البيانات
      _sentenceService.exportLocalData(userId, data);
    } catch (e) {
      throw Exception('فشل في استيراد البيانات: $e');
    }
  }

  Future<List<String>> getAvailableBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory
          .listSync()
          .where((file) =>
              file.path.contains(_exportFileName) && file.path.endsWith('.enc'))
          .map((file) => file.path)
          .toList();
      return files;
    } catch (e) {
      return [];
    }
  }

  Future<DateTime?> getBackupTimestamp(String filePath) async {
    try {
      final fileName = filePath.split('/').last;
      final timestamp = int.parse(
        fileName.replaceAll('${_exportFileName}_', '').replaceAll('.enc', ''),
      );
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      return null;
    }
  }

  Future<bool> deleteBackup(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<Map<String, dynamic>> previewBackup(String filePath) async {
    try {
      // قراءة الملف المشفر
      final file = File(filePath);
      final encrypted = Encrypted(await file.readAsBytes());

      // فك التشفير
      final encrypter = Encrypter(AES(_encryptionKey));
      final decrypted = encrypter.decrypt(encrypted, iv: _iv);

      // تحويل البيانات
      final data = json.decode(decrypted) as Map<String, dynamic>;

      return {
        'sentences': (data['sentences'] as List).length,
        'readCount': data['readCount'],
        'favoriteCount': data['favoriteCount'],
        'exportDate': data['exportDate'],
      };
    } catch (e) {
      throw Exception('فشل في قراءة النسخة الاحتياطية: $e');
    }
  }
}
