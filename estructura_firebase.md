# هيكل قاعدة بيانات Firebase للتطبيق

هذا الملف يوضح هيكل قاعدة بيانات Firebase المطلوب للتطبيق، بما في ذلك المجموعات والوثائق والحقول.

## المستويات (levels)

المستويات هي المجموعة الرئيسية التي تحتوي على معلومات المستويات الثلاثة.

```
/levels/{levelId}
```

حيث `levelId` هو معرف المستوى (1, 2, 3).

### حقول المستوى:

- `title`: عنوان المستوى (المبتدئ، المتوسط، المتقدم)
- `requiredSentences`: عدد الجمل المطلوبة لإكمال المستوى
- `totalEducationalPoints`: إجمالي النقاط التعليمية للمستوى
- `difficulty`: مستوى الصعوبة (easy, medium, hard)

### الدورات (cycles):

```
/levels/{levelId}/cycles/{cycleId}
```

حيث `cycleId` هو معرف الدورة (1, 2, 3, ...).

#### حقول الدورة:

- `title`: عنوان الدورة (الدورة الأولى، الدورة الثانية، ...)
- `isLocked`: هل الدورة مغلقة (true/false)
- `isCompleted`: هل تم إكمال الدورة (true/false)
- `totalSentences`: إجمالي عدد الجمل في الدورة
- `completedSentences`: عدد الجمل المكتملة
- `accuracy`: دقة النطق (0.0 - 1.0)

### مجموعات الدروس (lessonGroups):

```
/levels/{levelId}/cycles/{cycleId}/lessonGroups/{groupId}
```

حيث `groupId` هو معرف المجموعة (1, 2, 3, 4).

#### حقول مجموعة الدروس:

- `id`: معرف المجموعة
- `cycleId`: معرف الدورة
- `globalId`: معرف عالمي فريد
- `title`: عنوان المجموعة (دفعة 1، محادثة 1، دفعة 2، مراجعة)
- `type`: نوع المجموعة (sentenceBatch, conversation, review)
- `totalSentences`: إجمالي عدد الجمل
- `completedSentences`: عدد الجمل المكتملة
- `accuracy`: دقة النطق (0.0 - 1.0)
- `isCompleted`: هل تم إكمال المجموعة (true/false)
- `isLocked`: هل المجموعة مغلقة (true/false)
- `routePath`: مسار التوجيه (/daily-sentences, /conversation, /review)

## المستخدمين (users)

```
/users/{userId}
```

حيث `userId` هو معرف المستخدم من Firebase Authentication.

### حقول المستخدم:

- `email`: البريد الإلكتروني
- `name`: اسم المستخدم
- `typeuser`: نوع المستخدم (adm للمسؤول، usr للمستخدم العادي)
- `createdAt`: تاريخ الإنشاء

### تقدم المستخدم (progress):

```
/users/{userId}/progress/levels
```

#### حقول تقدم المستخدم:

- `currentLevel`: المستوى الحالي (1, 2, 3)
- `level_1_unlocked`: هل المستوى الأول مفتوح (true/false)
- `level_2_unlocked`: هل المستوى الثاني مفتوح (true/false)
- `level_3_unlocked`: هل المستوى الثالث مفتوح (true/false)
- `level_1_completedSentences`: عدد الجمل المكتملة في المستوى الأول
- `level_1_points`: النقاط المكتسبة في المستوى الأول
- `level_2_points`: النقاط المكتسبة في المستوى الثاني
- `level_3_points`: النقاط المكتسبة في المستوى الثالث

### نقاط المستخدم (points):

```
/users/{userId}/points/summary
```

#### حقول ملخص النقاط:

- `educational`: النقاط التعليمية
- `reward`: نقاط المكافآت
- `total`: إجمالي النقاط
- `lastUpdated`: آخر تحديث

### سجل النقاط (history):

```
/users/{userId}/points/history
```

#### حقول سجل النقاط:

- `lastUpdated`: آخر تحديث

### سجلات النقاط (records):

```
/users/{userId}/points/history/records/{recordId}
```

حيث `recordId` هو معرف فريد للسجل (يمكن استخدام الطابع الزمني).

#### حقول سجل النقاط:

- `amount`: مقدار النقاط
- `type`: نوع النقاط (educational, reward)
- `activity`: النشاط (read_sentence, complete_batch, daily_login, ...)
- `timestamp`: الطابع الزمني
- `description`: وصف النشاط

## مجموعات المستخدمين (userGroups)

```
/userGroups/{userId}
```

حيث `userId` هو معرف المستخدم.

### حقول مجموعة المستخدم:

- `userId`: معرف المستخدم
- `levelId`: معرف المستوى الحالي
- `createdAt`: تاريخ الإنشاء
- `updatedAt`: تاريخ التحديث

## مجموعات المستويات (levelGroups)

```
/levelGroups/{levelId}/users/{userId}
```

حيث `levelId` هو معرف المستوى و `userId` هو معرف المستخدم.

### حقول مجموعة المستوى:

- `userId`: معرف المستخدم
- `addedAt`: تاريخ الإضافة

## إعدادات النقاط (settings)

```
/settings/points
```

### حقول إعدادات النقاط:

- `read_sentence`: نقاط قراءة جملة جديدة
- `memory_test`: نقاط اختبار الحفظ
- `complete_batch`: نقاط إكمال دفعة
- `complete_conversation`: نقاط إكمال محادثة
- `review_sentence`: نقاط مراجعة جملة
- `daily_login`: نقاط تسجيل الدخول اليومي
- `streak_3_days`: نقاط تسلسل 3 أيام
- `streak_7_days`: نقاط تسلسل 7 أيام
- `streak_14_days`: نقاط تسلسل 14 يوم
- `watch_ad`: نقاط مشاهدة إعلان
- `share_link`: نقاط مشاركة رابط
- `successful_referral`: نقاط إحالة ناجحة
