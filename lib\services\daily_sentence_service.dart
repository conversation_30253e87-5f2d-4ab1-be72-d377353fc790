import 'package:flutter/foundation.dart';
import 'dart:math';
import 'hive_sentence_service.dart';
import 'hive_date_service.dart';
import 'sync_service.dart';
import '../models/hive/hive_sentence_model.dart';

/// خدمة إدارة الجمل اليومية
class DailySentenceService {
  final HiveSentenceService _hiveSentenceService;
  final HiveDateService _hiveDateService;
  final SyncService _syncService;

  DailySentenceService(
    this._hiveSentenceService,
    this._hiveDateService,
    this._syncService,
  );

  /// تحميل جميع الجمل من Firebase عند بدء التطبيق لأول مرة
  Future<List<HiveSentenceModel>> loadAllSentencesFirstTime(
      String userId) async {
    try {
      // التحقق مما إذا كان صندوق الجمل فارغًا (أول مرة)
      final isFirstLoad = _hiveSentenceService.getSentencesBox().isEmpty;

      if (isFirstLoad) {
        debugPrint('تحميل جميع الجمل من Firebase للمرة الأولى');
        // استدعاء طريقة جلب جميع الجمل من SyncService
        final allSentences =
            await _syncService.fetchAllSentencesFirstTime(userId);

        if (allSentences.isNotEmpty) {
          debugPrint('تم تحميل ${allSentences.length} جملة من Firebase');
          return allSentences;
        }
      } else {
        debugPrint(
            'الجمل موجودة بالفعل في التخزين المحلي، تم تخطي التحميل الأولي');
      }

      return [];
    } catch (e) {
      debugPrint('خطأ في تحميل جميع الجمل للمرة الأولى: $e');
      return [];
    }
  }

  /// الحصول على الجمل اليومية
  Future<List<HiveSentenceModel>> getDailySentences(String userId,
      {bool forceRefresh = false}) async {
    try {
      debugPrint('الحصول على الجمل اليومية، تحديث إجباري: $forceRefresh');

      // التحقق مما إذا كان هذا هو التحميل الأول
      final isFirstLoad = _hiveSentenceService.getSentencesBox().isEmpty;

      if (isFirstLoad) {
        // إذا كان هذا هو التحميل الأول، قم بتحميل جميع الجمل من Firebase
        debugPrint('التحميل الأول، جاري تحميل جميع الجمل من Firebase');
        await loadAllSentencesFirstTime(userId);
      }

      // التحقق مما إذا كان اليوم جديدًا
      final isNewDay = await _hiveDateService.isNewDay();
      debugPrint('هل اليوم جديد؟ $isNewDay');

      // إذا كان اليوم جديدًا، مسح الجمل اليومية القديمة
      if (isNewDay) {
        await _hiveSentenceService.replaceDailySentences([]);
        debugPrint('تم مسح الجمل اليومية القديمة لأن اليوم جديد');
      }

      // الحصول على الجمل اليومية الحالية
      final currentDailySentences = _hiveSentenceService.getDailySentences();
      debugPrint('عدد الجمل اليومية الحالية: ${currentDailySentences.length}');

      // الحصول على الجمل اليومية غير المقروءة
      final currentUnreadSentences =
          _hiveSentenceService.getUnreadDailySentences();
      debugPrint(
          'عدد الجمل اليومية غير المقروءة: ${currentUnreadSentences.length}');

      // التحقق مما إذا كانت جميع الجمل قد تمت قراءتها
      final allRead = currentDailySentences.isEmpty ||
          currentDailySentences.every((s) => s.isReadByCurrentUser);
      debugPrint('هل تمت قراءة جميع الجمل؟ $allRead');

      // إذا كان اليوم جديدًا أو تم طلب تحديث إجباري أو لا توجد جمل يومية
      if (isNewDay || forceRefresh || currentDailySentences.isEmpty) {
        debugPrint(
            'يوم جديد أو تحديث إجباري أو لا توجد جمل يومية، جاري جلب جمل جديدة');

        // Ensure we have the most up-to-date read status before getting new sentences
        await _hiveSentenceService.updateReadStatusFromSyncLogs();

        // Clear temporary read sentences list to start fresh
        await _hiveSentenceService.clearTempReadSentences();
        debugPrint(
            'Cleared temporary read sentences list for new day or force refresh');

        // Clear displayed sentences list when needed
        await _hiveSentenceService.clearDisplayedSentences();
        debugPrint(
            'Cleared displayed sentences list for new day or force refresh');

        // CRITICAL FIX: Reset the read status of all sentences in the database
        await _hiveSentenceService.resetAllSentencesReadStatus();
        debugPrint('Reset read status of all sentences in the database');

        // Get all read sentence IDs from sync logs and read sentences box
        final allReadSentenceIds =
            _hiveSentenceService.getAllReadSentenceIdsFromSyncLogs();
        debugPrint('All sentences ever read: ${allReadSentenceIds.length}');

        // Mark all sentences in the read sentences box as read in the model
        for (final sentenceId in allReadSentenceIds) {
          final sentence = _hiveSentenceService.getSentenceById(sentenceId);
          if (sentence != null && !sentence.isReadByCurrentUser) {
            sentence.isReadByCurrentUser = true;
            await sentence.save();
            debugPrint('Marked sentence $sentenceId as read in the model');
          }
        }

        // جلب جمل جديدة
        return await _getNewDailySentences(userId);
      }

      // إذا كانت هناك جمل يومية غير مقروءة، إرجاعها
      if (currentUnreadSentences.isNotEmpty) {
        debugPrint(
            'هناك ${currentUnreadSentences.length} جملة غير مقروءة، سيتم إرجاعها');
        return currentUnreadSentences;
      }

      // إذا كانت هناك جمل يومية ولكنها كلها مقروءة
      if (currentDailySentences.isNotEmpty && allRead) {
        debugPrint('جميع الجمل اليومية مقروءة، سيتم إرجاع قائمة فارغة');
        // إرجاع قائمة فارغة لإظهار رسالة "تمت قراءة جميع الجمل"
        return [];
      }

      // إذا وصلنا إلى هنا، فهذا يعني أنه لا توجد جمل يومية أو أنها كلها مقروءة
      // في هذه الحالة، نجلب جمل جديدة
      debugPrint('لا توجد جمل يومية غير مقروءة، جاري جلب جمل جديدة');
      return await _getNewDailySentences(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية: $e');
      return [];
    }
  }

  /// الحصول على جمل يومية جديدة
  Future<List<HiveSentenceModel>> _getNewDailySentences(String userId) async {
    try {
      debugPrint('Getting completely new daily sentences from local storage');

      // Clear temporary read sentences list to start fresh
      await _hiveSentenceService.clearTempReadSentences();
      debugPrint('Cleared temporary read sentences list');

      // Clear displayed sentences list when needed
      await _hiveSentenceService.clearDisplayedSentences();
      debugPrint('Cleared displayed sentences list');

      // CRITICAL FIX: Reset the read status of all sentences in the database
      await _hiveSentenceService.resetAllSentencesReadStatus();
      debugPrint('Reset read status of all sentences in the database');

      // Get all read sentence IDs from sync logs and read sentences box
      final allReadSentenceIds =
          _hiveSentenceService.getAllReadSentenceIdsFromSyncLogs();
      debugPrint('All sentences ever read: ${allReadSentenceIds.length}');

      // Mark all sentences in the read sentences box as read in the model
      for (final sentenceId in allReadSentenceIds) {
        final sentence = _hiveSentenceService.getSentenceById(sentenceId);
        if (sentence != null && !sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = true;
          await sentence.save();
          debugPrint('Marked sentence $sentenceId as read in the model');
        }
      }

      // Get all unread sentences from local storage
      final allUnreadSentences = _hiveSentenceService.getAllUnreadSentences();
      debugPrint(
          'Found ${allUnreadSentences.length} unread sentences in local storage');

      if (allUnreadSentences.isNotEmpty) {
        // If we have unread sentences in local storage, use them
        debugPrint('Using unread sentences from local storage');

        // Shuffle and select up to 10 sentences
        allUnreadSentences.shuffle(Random());
        final selectedSentences = allUnreadSentences.take(10).toList();

        // Make sure all sentences are marked as unread
        for (var sentence in selectedSentences) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();

          // Remove from read sentences box if it's there for some reason
          if (_hiveSentenceService.isInReadSentencesBox(sentence.id)) {
            await _hiveSentenceService.removeFromReadSentencesBox(sentence.id);
            debugPrint(
                'Removed sentence ${sentence.id} from read sentences box');
          }

          debugPrint('Set sentence ${sentence.id} as unread');
        }

        // Replace daily sentences
        await _hiveSentenceService.replaceDailySentences(selectedSentences);

        // Increment shown count
        await _hiveSentenceService
            .incrementTodayShownCount(selectedSentences.length);

        debugPrint(
            'Selected ${selectedSentences.length} new daily sentences from local storage');
        return selectedSentences;
      } else {
        // If no unread sentences in local storage, try to get from Firebase
        debugPrint(
            'No unread sentences in local storage, trying to get from Firebase');

        // Get random sentences from Firebase that have not been read
        final newSentences = await _syncService.fetchRandomSentencesNotInList(
            userId, allReadSentenceIds.toList(), 10);

        if (newSentences.isEmpty) {
          debugPrint(
              'No new unread sentences available from Firebase, returning empty list');
          return [];
        }

        debugPrint(
            'Retrieved ${newSentences.length} new sentences from Firebase');

        // Make sure all sentences are marked as unread
        for (var sentence in newSentences) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();

          // Remove from read sentences box if it's there for some reason
          if (_hiveSentenceService.isInReadSentencesBox(sentence.id)) {
            await _hiveSentenceService.removeFromReadSentencesBox(sentence.id);
            debugPrint(
                'Removed sentence ${sentence.id} from read sentences box');
          }

          debugPrint('Set sentence ${sentence.id} as unread');
        }

        // Replace daily sentences
        await _hiveSentenceService.replaceDailySentences(newSentences);

        // Increment shown count
        await _hiveSentenceService
            .incrementTodayShownCount(newSentences.length);

        debugPrint(
            'Selected ${newSentences.length} new daily sentences from Firebase');
        return newSentences;
      }
    } catch (e) {
      debugPrint('Error getting new daily sentences: $e');

      // Fallback to local sentences if Firebase fetch fails
      try {
        debugPrint('Falling back to local sentences');

        // Get all sentences from Hive
        final allSentences = _hiveSentenceService.getAllSentences();

        // Get all read sentence IDs
        final allReadSentenceIds =
            _hiveSentenceService.getAllReadSentenceIdsFromSyncLogs();

        // Filter out read sentences
        final availableSentences = allSentences
            .where((s) =>
                !allReadSentenceIds.contains(s.id) && !s.isReadByCurrentUser)
            .toList();

        debugPrint('Available local sentences: ${availableSentences.length}');

        if (availableSentences.isEmpty) {
          debugPrint(
              'No unread sentences available locally, returning empty list');
          return [];
        }

        // Shuffle and select up to 10 sentences
        availableSentences.shuffle(Random());
        final selectedSentences = availableSentences.take(10).toList();

        // Make sure all sentences are marked as unread
        for (var sentence in selectedSentences) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();

          if (_hiveSentenceService.isInReadSentencesBox(sentence.id)) {
            await _hiveSentenceService.removeFromReadSentencesBox(sentence.id);
          }
        }

        // Replace daily sentences
        await _hiveSentenceService.replaceDailySentences(selectedSentences);

        // Increment shown count
        await _hiveSentenceService
            .incrementTodayShownCount(selectedSentences.length);

        return selectedSentences;
      } catch (fallbackError) {
        debugPrint('Error in fallback: $fallbackError');
        return [];
      }
    }
  }

  /// الحصول على 10 جمل جديدة (زر "10 مرة أخرى")
  Future<List<HiveSentenceModel>> getMoreSentences(String userId) async {
    try {
      debugPrint('طلب المزيد من الجمل (زر "10 مرة أخرى")');

      // التحقق مما إذا كان هذا هو التحميل الأول
      final isFirstLoad = _hiveSentenceService.getSentencesBox().isEmpty;

      if (isFirstLoad) {
        // إذا كان هذا هو التحميل الأول، قم بتحميل جميع الجمل من Firebase
        debugPrint('التحميل الأول، جاري تحميل جميع الجمل من Firebase');
        await loadAllSentencesFirstTime(userId);
      }

      // الحصول على الجمل اليومية الحالية
      final currentDailySentences = _hiveSentenceService.getDailySentences();
      debugPrint('عدد الجمل اليومية الحالية: ${currentDailySentences.length}');

      // الحصول على الجمل اليومية غير المقروءة
      final currentUnreadSentences =
          _hiveSentenceService.getUnreadDailySentences();
      debugPrint(
          'عدد الجمل اليومية غير المقروءة: ${currentUnreadSentences.length}');

      // التحقق مما إذا كانت جميع الجمل الحالية قد تمت قراءتها
      final allRead = currentDailySentences.isEmpty ||
          currentDailySentences.every((s) => s.isReadByCurrentUser);
      debugPrint('هل تمت قراءة جميع الجمل؟ $allRead');

      // إذا كانت هناك جمل غير مقروءة، لا يمكن الحصول على جمل جديدة
      if (!allRead && currentUnreadSentences.isNotEmpty) {
        debugPrint(
            'لا يمكن الحصول على جمل جديدة حتى تتم قراءة جميع الجمل الحالية');
        return currentUnreadSentences;
      }

      // Get the current daily sentences before clearing them
      final allCurrentDailySentences = _hiveSentenceService.getDailySentences();

      // Mark all current daily sentences as read to ensure they won't be selected again
      for (var sentence in allCurrentDailySentences) {
        if (!sentence.isReadByCurrentUser) {
          await _hiveSentenceService.markSentenceAsRead(sentence.id);
          debugPrint(
              'Marked sentence ${sentence.id} as read to prevent reselection');
        }
      }

      // Clear the daily sentences list
      await _hiveSentenceService.replaceDailySentences([]);
      debugPrint('Cleared current daily sentences before getting new ones');

      // Ensure we have the most up-to-date read status before getting new sentences
      await _hiveSentenceService.updateReadStatusFromSyncLogs();

      // Clear any cached data that might be causing issues
      await _hiveSentenceService.clearDailySentencesCache();

      // Clear temporary read sentences list to start fresh
      await _hiveSentenceService.clearTempReadSentences();
      debugPrint('Cleared temporary read sentences list');

      // Clear displayed sentences list when needed
      await _hiveSentenceService.clearDisplayedSentences();
      debugPrint('Cleared displayed sentences list');

      // CRITICAL FIX: Reset the read status of all sentences in the database
      await _hiveSentenceService.resetAllSentencesReadStatus();
      debugPrint('Reset read status of all sentences in the database');

      // Get all read sentence IDs from sync logs and read sentences box
      final allReadSentenceIds =
          _hiveSentenceService.getAllReadSentenceIdsFromSyncLogs();
      debugPrint('All sentences ever read: ${allReadSentenceIds.length}');

      // Mark all sentences in the read sentences box as read in the model
      for (final sentenceId in allReadSentenceIds) {
        final sentence = _hiveSentenceService.getSentenceById(sentenceId);
        if (sentence != null && !sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = true;
          await sentence.save();
          debugPrint('Marked sentence $sentenceId as read in the model');
        }
      }

      // Get new sentences from local storage first
      debugPrint('Getting new sentences from local storage first');

      // Get all unread sentences from local storage
      final allUnreadSentences = _hiveSentenceService.getAllUnreadSentences();
      debugPrint(
          'Found ${allUnreadSentences.length} unread sentences in local storage');

      if (allUnreadSentences.isNotEmpty) {
        // If we have unread sentences in local storage, use them
        debugPrint('Using unread sentences from local storage');

        // Shuffle and select up to 10 sentences
        allUnreadSentences.shuffle(Random());
        final selectedSentences = allUnreadSentences.take(10).toList();

        // Make sure all sentences are marked as unread
        for (var sentence in selectedSentences) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();

          // Remove from read sentences box if it's there for some reason
          if (_hiveSentenceService.isInReadSentencesBox(sentence.id)) {
            await _hiveSentenceService.removeFromReadSentencesBox(sentence.id);
            debugPrint(
                'Removed sentence ${sentence.id} from read sentences box');
          }

          debugPrint('Set sentence ${sentence.id} as unread');
        }

        // Replace daily sentences
        await _hiveSentenceService.replaceDailySentences(selectedSentences);

        // Increment shown count
        await _hiveSentenceService
            .incrementTodayShownCount(selectedSentences.length);

        debugPrint(
            'Selected ${selectedSentences.length} new daily sentences from local storage');
        return selectedSentences;
      }

      // If no unread sentences in local storage, try to get from Firebase
      debugPrint(
          'No unread sentences in local storage, trying to get from Firebase');

      // We already have allReadSentenceIds from above
      debugPrint('Excluding ${allReadSentenceIds.length} read sentences');

      // Get random sentences from Firebase that have not been read
      final newSentences = await _syncService.fetchRandomSentencesNotInList(
          userId, allReadSentenceIds.toList(), 10);

      // If no new sentences are available, return an empty list
      if (newSentences.isEmpty) {
        debugPrint('No new sentences available, returning empty list');
        return [];
      }

      // Print diagnostic information about new sentences
      debugPrint('Retrieved ${newSentences.length} new sentences:');
      for (var i = 0; i < newSentences.length; i++) {
        // Asegurarse de que las oraciones nuevas no estén marcadas como leídas
        if (newSentences[i].isReadByCurrentUser) {
          newSentences[i].isReadByCurrentUser = false;
          await newSentences[i].save();
          debugPrint(
              'Corrected read status for sentence ${newSentences[i].id} to false');
        }

        // Verificar si la oración está en el cuadro de oraciones leídas y eliminarla si es necesario
        if (_hiveSentenceService.isInReadSentencesBox(newSentences[i].id)) {
          await _hiveSentenceService
              .removeFromReadSentencesBox(newSentences[i].id);
          debugPrint(
              'Removed sentence ${newSentences[i].id} from read sentences box');
        }

        debugPrint(
            'Sentence ${i + 1}: ${newSentences[i].id} - read=${newSentences[i].isReadByCurrentUser}');
      }

      // Replace daily sentences
      await _hiveSentenceService.replaceDailySentences(newSentences);

      // Increment shown count
      await _hiveSentenceService.incrementTodayShownCount(newSentences.length);

      debugPrint(
          'Selected ${newSentences.length} new daily sentences from Firebase');
      return newSentences;
    } catch (e) {
      debugPrint('Error getting more sentences: $e');
      return [];
    }
  }
}
