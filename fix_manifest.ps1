$manifestPath = "C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_mlkit_smart_reply-0.9.0\android\src\main\AndroidManifest.xml"

# Check if the file exists
if (Test-Path $manifestPath) {
    # Read the content of the file
    $content = Get-Content $manifestPath -Raw

    # Replace the package attribute in the manifest tag
    $newContent = $content -replace 'package="com.google_mlkit_smart_reply"', ''

    # Write the modified content back to the file
    Set-Content -Path $manifestPath -Value $newContent

    Write-Host "Successfully removed package attribute from AndroidManifest.xml"
} else {
    Write-Host "AndroidManifest.xml file not found at: $manifestPath"
}
