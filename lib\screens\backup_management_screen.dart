import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/data_export_service.dart';
import '../services/sentence_service.dart';
import '../viewmodels/auth_view_model.dart';
import '../viewmodels/sentence_view_model.dart';
import 'package:intl/intl.dart' as intl;

class BackupManagementScreen extends StatefulWidget {
  const BackupManagementScreen({super.key});

  @override
  State<BackupManagementScreen> createState() => _BackupManagementScreenState();
}

class _BackupManagementScreenState extends State<BackupManagementScreen> {
  late final DataExportService _dataExportService;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _dataExportService = DataExportService(SentenceService());
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
    }

    try {
      await _dataExportService.getAvailableBackups();
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _createBackup() async {
    final userId = Provider.of<AuthViewModel>(context, listen: false).user?.uid;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final filePath = await _dataExportService.exportData(userId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إنشاء النسخة الاحتياطية في: $filePath'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل إنشاء النسخة الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _loadBackups();
      }
    }
  }

  Future<void> _restoreBackup(String filePath) async {
    final userId = Provider.of<AuthViewModel>(context, listen: false).user?.uid;
    if (userId == null) return;

    try {
      // عرض محتويات النسخة الاحتياطية قبل الاستعادة
      final preview = await _dataExportService.previewBackup(filePath);
      if (!mounted) return;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد استعادة النسخة الاحتياطية'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('تاريخ النسخة: ${preview['exportDate']}'),
              Text('عدد الجمل: ${preview['sentences']}'),
              Text('الجمل المقروءة: ${preview['readCount']}'),
              Text('المفضلة: ${preview['favoriteCount']}'),
              const SizedBox(height: 16),
              const Text(
                'سيتم استبدال البيانات الحالية بمحتويات هذه النسخة. هل تريد المتابعة؟',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('استعادة'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _dataExportService.importData(userId, filePath);

      // تحديث البيانات في التطبيق
      if (mounted) {
        final viewModel =
            Provider.of<SentenceViewModel>(context, listen: false);
        await viewModel.loadSentences(forceRefresh: true);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم استعادة النسخة الاحتياطية بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل استعادة النسخة الاحتياطية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطية'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Card(
                  margin: const EdgeInsets.all(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Text(
                          'النسخ الاحتياطي',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'قم بإنشاء نسخة احتياطية من بياناتك المحلية لحمايتها من الفقدان.',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _createBackup,
                          icon: const Icon(Icons.backup),
                          label: const Text('إنشاء نسخة احتياطية'),
                        ),
                      ],
                    ),
                  ),
                ),
                if (_error != null)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      _error!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                Expanded(
                  child: FutureBuilder<List<String>>(
                    future: _dataExportService.getAvailableBackups(),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }

                      final backups = snapshot.data!;
                      if (backups.isEmpty) {
                        return const Center(
                          child: Text('لا توجد نسخ احتياطية'),
                        );
                      }

                      return ListView.builder(
                        itemCount: backups.length,
                        padding: const EdgeInsets.all(16),
                        itemBuilder: (context, index) {
                          final backup = backups[index];
                          return FutureBuilder<DateTime?>(
                            future:
                                _dataExportService.getBackupTimestamp(backup),
                            builder: (context, snapshot) {
                              final date = snapshot.data;
                              return Card(
                                child: ListTile(
                                  leading: const Icon(Icons.restore),
                                  title: Text(
                                    date != null
                                        ? intl.DateFormat('yyyy/MM/dd HH:mm')
                                            .format(date)
                                        : 'نسخة احتياطية',
                                  ),
                                  subtitle: Text(backup),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.delete_outline),
                                    onPressed: () async {
                                      final confirmed = await showDialog<bool>(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: const Text(
                                              'حذف النسخة الاحتياطية'),
                                          content: const Text(
                                            'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟',
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context, false),
                                              child: const Text('إلغاء'),
                                            ),
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context, true),
                                              style: TextButton.styleFrom(
                                                foregroundColor: Colors.red,
                                              ),
                                              child: const Text('حذف'),
                                            ),
                                          ],
                                        ),
                                      );

                                      if (confirmed == true) {
                                        await _dataExportService
                                            .deleteBackup(backup);
                                        if (mounted) {
                                          setState(() {});
                                        }
                                      }
                                    },
                                  ),
                                  onTap: () => _restoreBackup(backup),
                                ),
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}
