{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/ANDROIDSDK01/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/ANDROIDSDK01/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/ANDROIDSDK01/cmake/3.22.1/bin/ctest.exe", "root": "F:/ANDROIDSDK01/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-57d7d98300a891984138.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e335494a69b621079320.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-76a71b10da3737a88845.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e335494a69b621079320.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-76a71b10da3737a88845.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-57d7d98300a891984138.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}