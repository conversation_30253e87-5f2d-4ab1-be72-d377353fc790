import 'package:hive/hive.dart';

part 'sync_log_model.g.dart';

/// نوع عملية المزامنة
@HiveType(typeId: 6)
enum SyncOperationType {
  @HiveField(0)
  read,

  @HiveField(1)
  favorite,

  @HiveField(2)
  unfavorite,

  @HiveField(3)
  add,

  @HiveField(4)
  delete,
}

/// نموذج سجل المزامنة
@HiveType(typeId: 7)
class SyncLogModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String sentenceId;

  @HiveField(2)
  final SyncOperationType operationType;

  @HiveField(3)
  final DateTime timestamp;

  @HiveField(4)
  bool isSynced;

  SyncLogModel({
    required this.id,
    required this.sentenceId,
    required this.operationType,
    required this.timestamp,
    this.isSynced = false,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sentenceId': sentenceId,
      'operationType': operationType.toString(),
      'timestamp': timestamp.toIso8601String(),
      'isSynced': isSynced,
    };
  }

  // تعيين حالة المزامنة
  void markAsSynced() {
    isSynced = true;
    save();
  }
}
