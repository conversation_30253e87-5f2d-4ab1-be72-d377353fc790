{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\no\\test05_01\\android\\app\\.cxx\\Debug\\2a603j10\\x86", "clean"]], "buildTargetsCommandComponents": ["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\no\\test05_01\\android\\app\\.cxx\\Debug\\2a603j10\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}