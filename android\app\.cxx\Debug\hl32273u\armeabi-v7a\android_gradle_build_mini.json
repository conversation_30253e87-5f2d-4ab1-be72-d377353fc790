{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\no\\test05_01\\android\\app\\.cxx\\Debug\\hl32273u\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["F:\\ANDROIDSDK01\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\app\\no\\test05_01\\android\\app\\.cxx\\Debug\\hl32273u\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}