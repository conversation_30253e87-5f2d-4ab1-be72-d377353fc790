// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_log_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SyncLogModelAdapter extends TypeAdapter<SyncLogModel> {
  @override
  final int typeId = 7;

  @override
  SyncLogModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SyncLogModel(
      id: fields[0] as String,
      sentenceId: fields[1] as String,
      operationType: fields[2] as SyncOperationType,
      timestamp: fields[3] as DateTime,
      isSynced: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, SyncLogModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.sentenceId)
      ..writeByte(2)
      ..write(obj.operationType)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.isSynced);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncLogModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SyncOperationTypeAdapter extends TypeAdapter<SyncOperationType> {
  @override
  final int typeId = 6;

  @override
  SyncOperationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return SyncOperationType.read;
      case 1:
        return SyncOperationType.favorite;
      case 2:
        return SyncOperationType.unfavorite;
      case 3:
        return SyncOperationType.add;
      case 4:
        return SyncOperationType.delete;
      default:
        return SyncOperationType.read;
    }
  }

  @override
  void write(BinaryWriter writer, SyncOperationType obj) {
    switch (obj) {
      case SyncOperationType.read:
        writer.writeByte(0);
        break;
      case SyncOperationType.favorite:
        writer.writeByte(1);
        break;
      case SyncOperationType.unfavorite:
        writer.writeByte(2);
        break;
      case SyncOperationType.add:
        writer.writeByte(3);
        break;
      case SyncOperationType.delete:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncOperationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
