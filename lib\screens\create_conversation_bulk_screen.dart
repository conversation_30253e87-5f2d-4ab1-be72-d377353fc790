import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/message_model.dart';
import '../services/conversation_service.dart';

class CreateConversationBulkScreen extends StatefulWidget {
  const CreateConversationBulkScreen({Key? key}) : super(key: key);

  @override
  State<CreateConversationBulkScreen> createState() =>
      _CreateConversationBulkScreenState();
}

class _CreateConversationBulkScreenState
    extends State<CreateConversationBulkScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _bulkTextController = TextEditingController();
  String _selectedCategory = 'greetings';
  String _selectedDifficulty = 'medium';
  String _selectedLevel = '1'; // المستوى الافتراضي هو 1
  final List<MessageModel> _messages = [];
  bool _isLoading = false;
  bool _isLoadingCategories = true;
  List<Map<String, dynamic>> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoadingCategories = true;
    });

    try {
      // هنا يمكن استبدال هذه القائمة بجلب الفئات من قاعدة البيانات
      _categories = [
        {'id': 'greetings', 'name': 'التحيات'},
        {'id': 'travel', 'name': 'السفر'},
        {'id': 'food', 'name': 'الطعام'},
        {'id': 'shopping', 'name': 'التسوق'},
        {'id': 'education', 'name': 'التعليم'},
        {'id': 'work', 'name': 'العمل'},
        {'id': 'health', 'name': 'الصحة'},
        {'id': 'family', 'name': 'العائلة'},
        {'id': 'hobbies', 'name': 'الهوايات'},
        {'id': 'weather', 'name': 'الطقس'},
        {'id': 'other', 'name': 'أخرى'},
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل الفئات: $e');
    } finally {
      setState(() {
        _isLoadingCategories = false;
      });
    }
  }

  void _processMessages() {
    if (_bulkTextController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال نص المحادثة')),
      );
      return;
    }

    setState(() {
      _messages.clear();

      // تقسيم النص إلى أسطر
      List<String> lines = _bulkTextController.text.split('\n');

      // تجاهل الأسطر الفارغة
      lines = lines.where((line) => line.trim().isNotEmpty).toList();

      bool isPersonA = true; // البدء بالشخص الأول

      // معالجة كل سطرين كرسالة واحدة (عربي وإنجليزي)
      for (int i = 0; i < lines.length; i += 2) {
        if (i + 1 < lines.length) {
          String arabicText = lines[i].trim();
          String englishText = lines[i + 1].trim();

          // التحقق من أن النص العربي يحتوي على أحرف عربية
          bool isArabicFirst = _containsArabic(arabicText);

          // إذا كان السطر الأول لا يحتوي على أحرف عربية، نبدل الترتيب
          if (!isArabicFirst) {
            String temp = arabicText;
            arabicText = englishText;
            englishText = temp;
          }

          _messages.add(
            MessageModel(
              id: const Uuid().v4(),
              arabicText: arabicText,
              englishText: englishText,
              isPersonA: isPersonA,
              createdAt: DateTime.now(),
              readBy: {},
              testResults: {},
            ),
          );

          // تبديل الشخص للرسالة التالية
          isPersonA = !isPersonA;
        }
      }
    });

    // عرض عدد الرسائل التي تم إنشاؤها
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم إنشاء ${_messages.length} رسالة')),
    );
  }

  // دالة للتحقق مما إذا كان النص يحتوي على أحرف عربية
  bool _containsArabic(String text) {
    // نطاق الأحرف العربية في Unicode
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  Future<void> _saveConversation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_messages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة رسائل للمحادثة أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);

      final conversationId = await conversationService.createConversation(
        _titleController.text,
        _selectedCategory,
        _selectedDifficulty,
        _messages,
        _selectedLevel,
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ المحادثة بنجاح')),
        );
        Navigator.pop(context, conversationId);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء محادثة جديدة (نسخ ولصق)'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات المحادثة
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان المحادثة',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال عنوان للمحادثة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // اختيار الفئة
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          labelText: 'الفئة',
                          border: OutlineInputBorder(),
                        ),
                        items: _isLoadingCategories
                            ? [
                                const DropdownMenuItem<String>(
                                  value: 'greetings',
                                  child: Text('جاري التحميل...'),
                                )
                              ]
                            : _categories
                                .map(
                                  (category) => DropdownMenuItem<String>(
                                    value: category['id'] as String,
                                    child: Text(category['name'] as String),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // اختيار مستوى الصعوبة
                      DropdownButtonFormField<String>(
                        value: _selectedDifficulty,
                        decoration: const InputDecoration(
                          labelText: 'مستوى الصعوبة',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'easy',
                            child: Text('سهل'),
                          ),
                          DropdownMenuItem(
                            value: 'medium',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'hard',
                            child: Text('صعب'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedDifficulty = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // اختيار المستوى
                      DropdownButtonFormField<String>(
                        value: _selectedLevel,
                        decoration: const InputDecoration(
                          labelText: 'المستوى',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: '1',
                            child: Text('المستوى 1'),
                          ),
                          DropdownMenuItem(
                            value: '2',
                            child: Text('المستوى 2'),
                          ),
                          DropdownMenuItem(
                            value: '3',
                            child: Text('المستوى 3'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedLevel = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // حقل النص المجمع
                      const Text(
                        'نص المحادثة (كل سطر يمثل جملة، يتم تبديل الشخص تلقائياً):',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _bulkTextController,
                        decoration: const InputDecoration(
                          hintText:
                              'الجملة بالعربي الشخص 1\nالجملة بالإنجليزي الشخص 1\nالجملة بالعربي الشخص 2\nالجملة بالإنجليزي الشخص 2',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 10,
                        textDirection: TextDirection.rtl,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال نص المحادثة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // زر معالجة النص
                      ElevatedButton.icon(
                        onPressed: _processMessages,
                        icon: const Icon(Icons.format_list_bulleted),
                        label: const Text('معالجة النص'),
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 50),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // عرض الرسائل المعالجة
                      if (_messages.isNotEmpty) ...[
                        const Text(
                          'الرسائل المعالجة:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 200,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: _messages.length,
                            itemBuilder: (context, index) {
                              final message = _messages[index];
                              return Card(
                                margin: const EdgeInsets.all(4),
                                child: ListTile(
                                  leading: Icon(
                                    message.isPersonA
                                        ? Icons.person
                                        : Icons.person_outline,
                                    color: message.isPersonA
                                        ? Colors.blue
                                        : Colors.green,
                                  ),
                                  title: Text(
                                    message.arabicText,
                                    textDirection: TextDirection.rtl,
                                  ),
                                  subtitle: Text(
                                    message.englishText,
                                    textDirection: TextDirection.ltr,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 16),

                        // زر حفظ المحادثة
                        ElevatedButton.icon(
                          onPressed: _saveConversation,
                          icon: const Icon(Icons.save),
                          label: const Text('حفظ المحادثة'),
                          style: ElevatedButton.styleFrom(
                            minimumSize: const Size(double.infinity, 50),
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
