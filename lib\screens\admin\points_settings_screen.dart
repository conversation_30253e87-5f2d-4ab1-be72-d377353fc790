import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../../providers/points_provider.dart';
import '../../viewmodels/auth_view_model.dart';

/// شاشة إعدادات النقاط للمسؤولين
class PointsSettingsScreen extends StatefulWidget {
  const PointsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<PointsSettingsScreen> createState() => _PointsSettingsScreenState();
}

class _PointsSettingsScreenState extends State<PointsSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _error;
  Map<String, int> _pointsSettings = {};
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _loadPointsSettings();
  }

  @override
  void dispose() {
    // التخلص من وحدات التحكم في النص
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // تحميل إعدادات النقاط
  Future<void> _loadPointsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // جلب إعدادات النقاط من Firebase
      final settingsDoc = await FirebaseFirestore.instance
          .collection('settings')
          .doc('points')
          .get();

      if (settingsDoc.exists) {
        final data = settingsDoc.data() as Map<String, dynamic>;
        final settings = <String, int>{};
        
        // تحويل القيم إلى أعداد صحيحة
        data.forEach((key, value) {
          if (value is num) {
            settings[key] = value.toInt();
            // إنشاء وحدة تحكم لكل إعداد
            _controllers[key] = TextEditingController(text: value.toString());
          }
        });

        setState(() {
          _pointsSettings = settings;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء تحميل إعدادات النقاط: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // حفظ إعدادات النقاط
  Future<void> _savePointsSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحديث القيم من وحدات التحكم
      final updatedSettings = <String, int>{};
      _controllers.forEach((key, controller) {
        updatedSettings[key] = int.parse(controller.text);
      });

      // حفظ الإعدادات في Firebase
      await FirebaseFirestore.instance
          .collection('settings')
          .doc('points')
          .set(updatedSettings);

      // تحديث مزود النقاط
      final pointsProvider = Provider.of<PointsProvider>(context, listen: false);
      await pointsProvider.fetchPoints();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حفظ إعدادات النقاط بنجاح')),
      );
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء حفظ إعدادات النقاط: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authViewModel = Provider.of<AuthViewModel>(context);
    
    // التحقق من أن المستخدم مسؤول
    if (!authViewModel.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('إعدادات النقاط')),
        body: const Center(
          child: Text('غير مصرح لك بالوصول إلى هذه الصفحة'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات النقاط'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'إعدادات النقاط التعليمية',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPointSettingField('read_sentence', 'قراءة جملة جديدة'),
                        _buildPointSettingField('memory_test', 'اختبار الحفظ'),
                        _buildPointSettingField('complete_batch', 'إكمال دفعة'),
                        _buildPointSettingField('complete_conversation', 'إكمال محادثة'),
                        _buildPointSettingField('review_sentence', 'مراجعة جملة'),
                        
                        const SizedBox(height: 24),
                        const Text(
                          'إعدادات نقاط المكافأة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPointSettingField('daily_login', 'تسجيل الدخول اليومي'),
                        _buildPointSettingField('streak_3_days', 'تسلسل 3 أيام'),
                        _buildPointSettingField('streak_7_days', 'تسلسل 7 أيام'),
                        _buildPointSettingField('streak_14_days', 'تسلسل 14 يوم'),
                        _buildPointSettingField('watch_ad', 'مشاهدة إعلان'),
                        _buildPointSettingField('share_link', 'مشاركة رابط'),
                        _buildPointSettingField('successful_referral', 'إحالة ناجحة'),
                        
                        const SizedBox(height: 24),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _savePointsSettings,
                            child: _isLoading
                                ? const CircularProgressIndicator()
                                : const Text('حفظ الإعدادات'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }

  // بناء حقل إعداد النقاط
  Widget _buildPointSettingField(String key, String label) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController(
          text: _pointsSettings[key]?.toString() ?? '0');
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: _controllers[key],
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال قيمة';
          }
          if (int.tryParse(value) == null) {
            return 'يرجى إدخال رقم صحيح';
          }
          return null;
        },
      ),
    );
  }
}
