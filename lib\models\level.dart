import 'lesson_group.dart';
import 'cycle.dart';

/// نموذج المستوى في التطبيق
class Level {
  final int id;
  final String title;
  final bool isLocked;
  final bool isCurrent;
  final int requiredSentences; // عدد الجمل المطلوبة للترقية للمستوى التالي
  final List<Cycle> cycles; // دورات المستوى
  final int totalEducationalPoints; // إجمالي النقاط التعليمية للمستوى
  final int earnedEducationalPoints; // النقاط التعليمية المكتسبة
  final String difficulty; // مستوى صعوبة الجمل (easy, medium, hard)

  Level({
    required this.id,
    required this.title,
    required this.isLocked,
    required this.isCurrent,
    required this.requiredSentences,
    required this.cycles,
    required this.totalEducationalPoints,
    required this.earnedEducationalPoints,
    this.difficulty = 'medium', // القيمة الافتراضية هي متوسط
  });

  /// الحصول على جميع مجموعات الدروس في جميع الدورات
  List<LessonGroup> get allLessonGroups {
    List<LessonGroup> groups = [];
    for (var cycle in cycles) {
      groups.addAll(cycle.lessonGroups);
    }
    return groups;
  }

  /// البحث عن مجموعة دروس بواسطة المعرف العام
  LessonGroup? findLessonGroupByGlobalId(int globalId) {
    for (var cycle in cycles) {
      for (var group in cycle.lessonGroups) {
        if (group.id == globalId) {
          return group;
        }
      }
    }
    return null;
  }

  /// البحث عن دورة ومجموعة دروس بواسطة معرفات الدورة والمجموعة
  Map<String, dynamic>? findCycleAndGroup(int cycleId, int groupId) {
    for (var cycle in cycles) {
      if (cycle.id == cycleId) {
        for (var group in cycle.lessonGroups) {
          if (group.id == groupId) {
            return {
              'cycle': cycle,
              'group': group,
            };
          }
        }
      }
    }
    return null;
  }

  /// إنشاء نسخة من المستوى من بيانات JSON
  factory Level.fromJson(Map<String, dynamic> json) {
    return Level(
      id: json['id'] as int,
      title: json['title'] as String,
      isLocked: json['isLocked'] as bool,
      isCurrent: json['isCurrent'] as bool,
      requiredSentences: json['requiredSentences'] as int,
      cycles: (json['cycles'] as List<dynamic>)
          .map((e) => Cycle.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalEducationalPoints: json['totalEducationalPoints'] as int,
      earnedEducationalPoints: json['earnedEducationalPoints'] as int,
      difficulty: json['difficulty'] as String? ?? 'medium',
    );
  }

  /// تحويل المستوى إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isLocked': isLocked,
      'isCurrent': isCurrent,
      'requiredSentences': requiredSentences,
      'cycles': cycles.map((e) => e.toJson()).toList(),
      'totalEducationalPoints': totalEducationalPoints,
      'earnedEducationalPoints': earnedEducationalPoints,
      'difficulty': difficulty,
    };
  }

  /// إنشاء نسخة جديدة من المستوى مع تحديث بعض الخصائص
  Level copyWith({
    int? id,
    String? title,
    bool? isLocked,
    bool? isCurrent,
    int? requiredSentences,
    List<Cycle>? cycles,
    int? totalEducationalPoints,
    int? earnedEducationalPoints,
    String? difficulty,
  }) {
    return Level(
      id: id ?? this.id,
      title: title ?? this.title,
      isLocked: isLocked ?? this.isLocked,
      isCurrent: isCurrent ?? this.isCurrent,
      requiredSentences: requiredSentences ?? this.requiredSentences,
      cycles: cycles ?? this.cycles,
      totalEducationalPoints:
          totalEducationalPoints ?? this.totalEducationalPoints,
      earnedEducationalPoints:
          earnedEducationalPoints ?? this.earnedEducationalPoints,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  @override
  String toString() {
    return 'Level{id: $id, title: $title, isLocked: $isLocked, isCurrent: $isCurrent, requiredSentences: $requiredSentences, cycles: $cycles, totalEducationalPoints: $totalEducationalPoints, earnedEducationalPoints: $earnedEducationalPoints, difficulty: $difficulty}';
  }
}
