import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hive/hive_sentence_model.dart';
import '../viewmodels/hive_sentence_view_model.dart';
import '../viewmodels/auth_view_model.dart';
import '../theme/app_theme.dart';
import '../widgets/quiz_dialog.dart';
import '../models/sentence_model.dart';

/// بطاقة الجملة المتحركة التي تستخدم HiveSentenceModel
class HiveAnimatedSentenceCard extends StatefulWidget {
  final HiveSentenceModel sentence;
  final int index;
  final PageController pageController;

  const HiveAnimatedSentenceCard({
    super.key,
    required this.sentence,
    required this.index,
    required this.pageController,
  });

  @override
  State<HiveAnimatedSentenceCard> createState() =>
      _HiveAnimatedSentenceCardState();
}

class _HiveAnimatedSentenceCardState extends State<HiveAnimatedSentenceCard>
    with SingleTickerProviderStateMixin {
  bool _showTranslation = false;
  bool _isMarkedAsRead = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  // دالة مساعدة لعرض نص مستوى الصعوبة
  String _getDifficultyText(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'غير محدد';
    }
  }

  // دالة مساعدة لعرض لون مستوى الصعوبة
  Color _getDifficultyColor(String? difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // بدء التحريك بالحجم الكامل
    _animationController.value = 0.0;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRead = widget.sentence.isReadByCurrentUser || _isMarkedAsRead;
    final authVM = Provider.of<AuthViewModel>(context, listen: false);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Card(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(
                  color: isRead ? Colors.green : Colors.transparent,
                  width: isRead ? 2 : 0,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // رأس البطاقة مع مستوى الصعوبة
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // مستوى الصعوبة
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color:
                                _getDifficultyColor(widget.sentence.difficulty)
                                    .withAlpha(51), // 0.2 * 255 = 51
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.signal_cellular_alt,
                                size: 14,
                                color: _getDifficultyColor(
                                    widget.sentence.difficulty),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _getDifficultyText(widget.sentence.difficulty),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getDifficultyColor(
                                      widget.sentence.difficulty),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // زر المفضلة
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // زر الاختبار
                            IconButton(
                              icon: const Icon(
                                Icons.school,
                                color: AppTheme.primaryColor,
                                size: 20,
                              ),
                              onPressed: () {
                                // تحويل HiveSentenceModel إلى SentenceModel للاستخدام في نافذة الاختبار
                                final sentence = SentenceModel(
                                  id: widget.sentence.id,
                                  arabicText: widget.sentence.arabicText,
                                  englishText: widget.sentence.englishText,
                                  category: widget.sentence.category,
                                  createdAt: widget.sentence.createdAt,
                                  readBy: {},
                                  isFavorite:
                                      widget.sentence.isFavoriteByCurrentUser,
                                  difficulty: widget.sentence.difficulty,
                                  isReadByCurrentUser:
                                      widget.sentence.isReadByCurrentUser,
                                  isFavoriteByCurrentUser:
                                      widget.sentence.isFavoriteByCurrentUser,
                                );

                                // فتح نافذة الاختبار
                                showDialog(
                                  context: context,
                                  builder: (context) => QuizDialog(
                                    sentence: sentence,
                                  ),
                                ).then((result) {
                                  // التحقق من نتيجة الاختبار
                                  if (result != null &&
                                      result is Map<String, dynamic>) {
                                    // تحديث حالة الاختبار في نموذج الجملة
                                    if (result
                                        .containsKey('isPronunciationTested')) {
                                      sentence.isPronunciationTested =
                                          result['isPronunciationTested'];

                                      // تحديث واجهة المستخدم
                                      setState(() {});
                                    }
                                  }
                                });
                              },
                              tooltip: 'اختبار الفهم',
                            ),
                            // زر المفضلة
                            IconButton(
                              icon: Icon(
                                widget.sentence.isFavoriteByCurrentUser
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: widget.sentence.isFavoriteByCurrentUser
                                    ? Colors.red
                                    : null,
                                size: 20,
                              ),
                              onPressed: () {
                                if (authVM.isAuthenticated) {
                                  final viewModel =
                                      Provider.of<HiveSentenceViewModel>(
                                          context,
                                          listen: false);
                                  viewModel.toggleFavorite(widget.sentence.id);
                                }
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // نص الجملة الإنجليزي
                    Text(
                      widget.sentence.englishText,
                      style: const TextStyle(
                        fontSize: 18,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    const SizedBox(height: 16),
                    // زر إظهار/إخفاء الترجمة
                    OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _showTranslation = !_showTranslation;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: theme.colorScheme.primary
                              .withAlpha(128), // 0.5 * 255 = 128
                        ),
                      ),
                      child: Text(
                        _showTranslation ? 'إخفاء الترجمة' : 'إظهار الترجمة',
                      ),
                    ),
                    // الترجمة العربية (تظهر/تختفي)
                    if (_showTranslation) ...[
                      const SizedBox(height: 16),
                      Text(
                        widget.sentence.arabicText,
                        style: TextStyle(
                          fontSize: 18,
                          height: 1.5,
                          color: theme.colorScheme.onSurface
                              .withAlpha(204), // 0.8 * 255 = 204
                        ),
                        textAlign: TextAlign.right,
                        textDirection: TextDirection.rtl,
                      ),
                    ],
                    const Spacer(),
                    // زر تعليم كمقروءة
                    Consumer<AuthViewModel>(
                      builder: (context, authVM, _) {
                        return ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isRead
                                ? Colors.green
                                : theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            isRead ? 'تمت القراءة' : 'تعليم كمقروءة',
                          ),
                          onPressed: () {
                            if (!isRead && authVM.isAuthenticated) {
                              final viewModel =
                                  Provider.of<HiveSentenceViewModel>(context,
                                      listen: false);

                              // تعليم الجملة كمقروءة
                              viewModel.markSentenceAsRead(widget.sentence.id);

                              // تغيير حالة البطاقة وإخفاؤها بعد فترة
                              setState(() {
                                _isMarkedAsRead = true;
                              });

                              // الحصول على عدد البطاقات المتبقية قبل الإخفاء
                              final hiveSentenceViewModel =
                                  Provider.of<HiveSentenceViewModel>(context,
                                      listen: false);

                              // نحسب عدد البطاقات المتبقية بعد تعليم البطاقة الحالية كمقروءة
                              final totalUnreadCards = hiveSentenceViewModel
                                  .dailySentences
                                  .where((s) =>
                                      !s.isReadByCurrentUser &&
                                      s.id != widget.sentence.id)
                                  .length;

                              debugPrint(
                                  'Remaining unread cards: $totalUnreadCards');

                              // إخفاء البطاقة بعد فترة قصيرة
                              Future.delayed(const Duration(milliseconds: 500),
                                  () {
                                // تحريك البطاقة للخارج
                                _animationController.forward().then((_) {
                                  // حساب الصفحة التالية
                                  int nextPage;

                                  if (totalUnreadCards == 0) {
                                    // إذا كانت هذه هي البطاقة الأخيرة، نقوم بتحديث واجهة المستخدم فقط
                                    debugPrint(
                                        'Last card marked as read, updating UI');

                                    // تأكد من تحديث واجهة المستخدم قبل إخفاء البطاقة
                                    hiveSentenceViewModel.loadDailySentences(
                                        userId: authVM.user?.uid);

                                    // لا نقوم بأي تحريك، فقط نخفي البطاقة الحالية
                                    _animationController.reverse();
                                    return;
                                  } else {
                                    // تحديد ما إذا كانت البطاقة الحالية هي الأولى في القائمة
                                    final isFirstCard = widget.index == 0;

                                    // تحديد ما إذا كانت البطاقة الحالية هي الأخيرة في القائمة
                                    final isLastCard = widget.index ==
                                        hiveSentenceViewModel
                                                .dailySentences.length -
                                            1;

                                    debugPrint(
                                        'Is first card: $isFirstCard, Is last card: $isLastCard');

                                    // تحديد الصفحة التالية
                                    if (isLastCard) {
                                      // إذا كانت البطاقة الأخيرة، انتقل إلى البطاقة السابقة
                                      nextPage = widget.index - 1;
                                    } else {
                                      // في الحالات الأخرى، انتقل إلى البطاقة التالية
                                      nextPage = widget.index;
                                    }

                                    // تحريك البطاقات - تحقق أولاً من أن PageController لا يزال صالحًا
                                    if (widget.pageController.hasClients) {
                                      try {
                                        widget.pageController
                                            .jumpToPage(nextPage);

                                        // تحديث واجهة المستخدم فورًا بعد تعليم الجملة كمقروءة
                                        setState(() {
                                          // تحديث حالة البطاقة المحلية
                                        });

                                        // إضافة تأخير قصير قبل إعادة تحميل الجمل
                                        Future.delayed(
                                            const Duration(milliseconds: 300),
                                            () {
                                          try {
                                            // إعادة تحميل الجمل بعد تحريك البطاقات
                                            hiveSentenceViewModel
                                                .loadDailySentences(
                                                    userId: authVM.user?.uid);
                                          } catch (e) {
                                            debugPrint(
                                                'Error refreshing sentences: $e');
                                          }
                                        });
                                      } catch (e) {
                                        debugPrint('Error jumping to page: $e');
                                      }
                                    } else {
                                      debugPrint(
                                          'PageController no longer has clients');
                                    }
                                  }

                                  _animationController.reverse();
                                });
                              });
                            }
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
