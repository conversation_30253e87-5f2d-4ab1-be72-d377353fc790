import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../constants/hive_constants.dart';
import '../models/conversation_model.dart';
import '../models/message_model.dart';

class ConversationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final Connectivity _connectivity = Connectivity();

  // Claves para Hive
  static const String _userConversationsKey = 'user_conversations';
  static const String _lastSyncKey = 'last_sync_conversations';

  // Colección de conversaciones
  CollectionReference get _conversationsCollection =>
      _firestore.collection('conversations');

  // Obtener el ID del usuario actual
  String? get currentUserId => _auth.currentUser?.uid;

  // Verificar si hay conexión a Internet
  Future<bool> get isConnected async {
    var connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Obtener la caja de Hive para conversaciones
  Future<Box> _getConversationsBox() async {
    if (!Hive.isBoxOpen(HiveConstants.conversationsBox)) {
      return await Hive.openBox(HiveConstants.conversationsBox);
    }
    return Hive.box(HiveConstants.conversationsBox);
  }

  // Guardar una conversación en el almacenamiento local
  Future<void> _saveConversationLocally(ConversationModel conversation) async {
    if (currentUserId == null) return;

    final box = await _getConversationsBox();

    // Obtener las conversaciones actuales del usuario
    Map<String, dynamic> userConversations = Map<String, dynamic>.from(
        box.get('${_userConversationsKey}_$currentUserId') ?? {});

    // Convertir la conversación a un mapa
    Map<String, dynamic> conversationMap = {
      'id': conversation.id,
      'title': conversation.title,
      'category': conversation.category,
      'createdAt': conversation.createdAt.millisecondsSinceEpoch,
      'createdBy': conversation.createdBy,
      'messages': conversation.messages.map((msg) => msg.toMap()).toList(),
      'readBy': conversation.readBy,
      'difficulty': conversation.difficulty,
      'level': conversation.level,
    };

    // Guardar la conversación
    userConversations[conversation.id] = conversationMap;

    // Actualizar el almacenamiento
    await box.put('${_userConversationsKey}_$currentUserId', userConversations);
  }

  // Obtener una conversación del almacenamiento local
  Future<ConversationModel?> _getConversationLocally(
      String conversationId) async {
    if (currentUserId == null) return null;

    final box = await _getConversationsBox();

    // Obtener las conversaciones del usuario
    dynamic rawData = box.get('${_userConversationsKey}_$currentUserId') ?? {};
    Map<dynamic, dynamic> rawMap = rawData is Map ? rawData : {};
    Map<String, dynamic> userConversations = {};

    // Convertir las claves dinámicas a String
    rawMap.forEach((key, value) {
      if (key != null) {
        userConversations[key.toString()] = value;
      }
    });

    // Verificar si la conversación existe
    if (!userConversations.containsKey(conversationId)) return null;

    try {
      // Obtener la conversación y asegurarse de que sea un Map<String, dynamic>
      dynamic conversationData = userConversations[conversationId];
      if (conversationData is! Map) return null;

      Map<String, dynamic> conversationMap = Map<String, dynamic>.from(
          conversationData
              .map((key, value) => MapEntry(key.toString(), value)));

      // Convertir los mensajes
      List<MessageModel> messages = [];
      if (conversationMap['messages'] != null) {
        try {
          messages = List<MessageModel>.from(
            (conversationMap['messages'] as List).map(
              (message) {
                if (message is Map) {
                  // Convertir explícitamente cada clave y valor a String y dynamic
                  Map<String, dynamic> stringMap = {};
                  message.forEach((key, value) {
                    stringMap[key.toString()] = value;
                  });
                  return MessageModel.fromMap(stringMap);
                }
                return MessageModel(
                  id: 'error',
                  englishText: 'Error loading message',
                  arabicText: 'خطأ في تحميل الرسالة',
                  isPersonA: true,
                  createdAt: DateTime.now(),
                  readBy: {},
                  testResults: {},
                );
              },
            ),
          );
        } catch (e) {
          debugPrint('Error al convertir mensajes: $e');
        }
      }

      // Convertir readBy a Map<String, dynamic>
      Map<String, dynamic> readBy = {};
      if (conversationMap['readBy'] != null &&
          conversationMap['readBy'] is Map) {
        (conversationMap['readBy'] as Map).forEach((key, value) {
          if (key != null && value is Map) {
            readBy[key.toString()] = Map<String, dynamic>.from(
              value.map((k, v) => MapEntry(k.toString(), v)),
            );
          }
        });
      }

      // Crear y devolver el modelo de conversación
      return ConversationModel(
        id: conversationMap['id']?.toString() ?? '',
        title: conversationMap['title']?.toString() ?? '',
        category: conversationMap['category']?.toString() ?? '',
        createdAt: conversationMap['createdAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(
                conversationMap['createdAt'] as int)
            : DateTime.now(),
        createdBy: conversationMap['createdBy']?.toString() ?? '',
        messages: messages,
        readBy: readBy,
        difficulty: conversationMap['difficulty']?.toString() ?? 'medium',
        level: conversationMap['level']?.toString() ?? '1',
      );
    } catch (e) {
      debugPrint('Error al obtener conversación local: $e');
      return null;
    }
  }

  // Obtener todas las conversaciones del almacenamiento local
  Future<List<ConversationModel>> _getAllConversationsLocally() async {
    if (currentUserId == null) return [];

    final box = await _getConversationsBox();

    // Obtener las conversaciones del usuario
    dynamic rawData = box.get('${_userConversationsKey}_$currentUserId') ?? {};
    Map<dynamic, dynamic> rawMap = rawData is Map ? rawData : {};
    Map<String, dynamic> userConversations = {};

    // Convertir las claves dinámicas a String
    rawMap.forEach((key, value) {
      if (key != null) {
        userConversations[key.toString()] = value;
      }
    });

    // Convertir a lista de modelos
    List<ConversationModel> conversations = [];
    for (var conversationData in userConversations.values) {
      try {
        // Asegurarse de que conversationMap sea un Map<String, dynamic>
        Map<String, dynamic> conversationMap;
        if (conversationData is Map) {
          conversationMap = Map<String, dynamic>.from(conversationData.map(
            (key, value) => MapEntry(key.toString(), value),
          ));
        } else {
          continue; // Saltar si no es un mapa
        }

        // Convertir los mensajes
        List<MessageModel> messages = [];
        if (conversationMap['messages'] != null) {
          try {
            messages = List<MessageModel>.from(
              (conversationMap['messages'] as List).map(
                (message) {
                  if (message is Map) {
                    // Convertir explícitamente cada clave y valor a String y dynamic
                    Map<String, dynamic> stringMap = {};
                    message.forEach((key, value) {
                      stringMap[key.toString()] = value;
                    });
                    return MessageModel.fromMap(stringMap);
                  }
                  return MessageModel(
                    id: 'error',
                    englishText: 'Error loading message',
                    arabicText: 'خطأ في تحميل الرسالة',
                    isPersonA: true,
                    createdAt: DateTime.now(),
                    readBy: {},
                    testResults: {},
                  );
                },
              ),
            );
          } catch (e) {
            debugPrint('Error al convertir mensajes: $e');
          }
        }

        // Convertir readBy a Map<String, dynamic>
        Map<String, dynamic> readBy = {};
        if (conversationMap['readBy'] != null &&
            conversationMap['readBy'] is Map) {
          (conversationMap['readBy'] as Map).forEach((key, value) {
            if (key != null && value is Map) {
              readBy[key.toString()] = Map<String, dynamic>.from(
                value.map((k, v) => MapEntry(k.toString(), v)),
              );
            }
          });
        }

        // Crear el modelo de conversación
        conversations.add(ConversationModel(
          id: conversationMap['id']?.toString() ?? '',
          title: conversationMap['title']?.toString() ?? '',
          category: conversationMap['category']?.toString() ?? '',
          createdAt: conversationMap['createdAt'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                  conversationMap['createdAt'] as int)
              : DateTime.now(),
          createdBy: conversationMap['createdBy']?.toString() ?? '',
          messages: messages,
          readBy: readBy,
          difficulty: conversationMap['difficulty']?.toString() ?? 'medium',
          level: conversationMap['level']?.toString() ?? '1',
        ));
      } catch (e) {
        debugPrint('Error al convertir conversación: $e');
      }
    }

    // Ordenar por fecha de creación (más reciente primero)
    conversations.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return conversations;
  }

  // Obtener todas las conversaciones
  Stream<List<ConversationModel>> getConversations() async* {
    // Primero intentar obtener las conversaciones localmente
    List<ConversationModel> localConversations =
        await _getAllConversationsLocally();
    if (localConversations.isNotEmpty) {
      yield localConversations;
    }

    // Luego intentar obtener las conversaciones de Firebase si hay conexión
    if (await isConnected) {
      try {
        await for (var snapshot in _conversationsCollection
            .orderBy('createdAt', descending: true)
            .snapshots()) {
          List<ConversationModel> firebaseConversations = snapshot.docs
              .map((doc) => ConversationModel.fromFirestore(doc))
              .toList();

          // Guardar las conversaciones localmente
          for (var conversation in firebaseConversations) {
            await _saveConversationLocally(conversation);
          }

          yield firebaseConversations;
        }
      } catch (e) {
        // Si hay un error, devolver las conversaciones locales
        if (localConversations.isEmpty) {
          yield [];
        }
      }
    }
  }

  // Obtener conversaciones por categoría
  Stream<List<ConversationModel>> getConversationsByCategory(
      String category) async* {
    // Primero intentar obtener las conversaciones localmente
    List<ConversationModel> localConversations =
        await _getAllConversationsLocally();
    List<ConversationModel> filteredLocalConversations = localConversations
        .where((conversation) => conversation.category == category)
        .toList();

    if (filteredLocalConversations.isNotEmpty) {
      yield filteredLocalConversations;
    }

    // Luego intentar obtener las conversaciones de Firebase si hay conexión
    if (await isConnected) {
      try {
        await for (var snapshot in _conversationsCollection
            .where('category', isEqualTo: category)
            .orderBy('createdAt', descending: true)
            .snapshots()) {
          List<ConversationModel> firebaseConversations = snapshot.docs
              .map((doc) => ConversationModel.fromFirestore(doc))
              .toList();

          // Guardar las conversaciones localmente
          for (var conversation in firebaseConversations) {
            await _saveConversationLocally(conversation);
          }

          yield firebaseConversations;
        }
      } catch (e) {
        // Si hay un error, devolver las conversaciones locales
        if (filteredLocalConversations.isEmpty) {
          yield [];
        }
      }
    }
  }

  // Obtener conversaciones por dificultad
  Stream<List<ConversationModel>> getConversationsByDifficulty(
      String difficulty) async* {
    // Primero intentar obtener las conversaciones localmente
    List<ConversationModel> localConversations =
        await _getAllConversationsLocally();
    List<ConversationModel> filteredLocalConversations = localConversations
        .where((conversation) => conversation.difficulty == difficulty)
        .toList();

    if (filteredLocalConversations.isNotEmpty) {
      yield filteredLocalConversations;
    }

    // Luego intentar obtener las conversaciones de Firebase si hay conexión
    if (await isConnected) {
      try {
        await for (var snapshot in _conversationsCollection
            .where('difficulty', isEqualTo: difficulty)
            .orderBy('createdAt', descending: true)
            .snapshots()) {
          List<ConversationModel> firebaseConversations = snapshot.docs
              .map((doc) => ConversationModel.fromFirestore(doc))
              .toList();

          // Guardar las conversaciones localmente
          for (var conversation in firebaseConversations) {
            await _saveConversationLocally(conversation);
          }

          yield firebaseConversations;
        }
      } catch (e) {
        // Si hay un error, devolver las conversaciones locales
        if (filteredLocalConversations.isEmpty) {
          yield [];
        }
      }
    }
  }

  // Obtener conversaciones por nivel
  Stream<List<ConversationModel>> getConversationsByLevel(String level) async* {
    // Primero intentar obtener las conversaciones localmente
    List<ConversationModel> localConversations =
        await _getAllConversationsLocally();
    List<ConversationModel> filteredLocalConversations = localConversations
        .where((conversation) => conversation.level == level)
        .toList();

    if (filteredLocalConversations.isNotEmpty) {
      yield filteredLocalConversations;
    }

    // Luego intentar obtener las conversaciones de Firebase si hay conexión
    if (await isConnected) {
      try {
        await for (var snapshot in _conversationsCollection
            .where('level', isEqualTo: level)
            .orderBy('createdAt', descending: true)
            .snapshots()) {
          List<ConversationModel> firebaseConversations = snapshot.docs
              .map((doc) => ConversationModel.fromFirestore(doc))
              .toList();

          // Guardar las conversaciones localmente
          for (var conversation in firebaseConversations) {
            await _saveConversationLocally(conversation);
          }

          yield firebaseConversations;
        }
      } catch (e) {
        // Si hay un error, devolver las conversaciones locales
        if (filteredLocalConversations.isEmpty) {
          yield [];
        }
      }
    }
  }

  // Obtener conversaciones leídas por el usuario actual
  Stream<List<ConversationModel>> getReadConversations() async* {
    if (currentUserId == null) {
      yield [];
      return;
    }

    // Primero intentar obtener las conversaciones localmente
    List<ConversationModel> localConversations =
        await _getAllConversationsLocally();
    List<ConversationModel> filteredLocalConversations = localConversations
        .where((conversation) =>
            conversation.readBy.containsKey(currentUserId) &&
            (conversation.readBy[currentUserId]
                    as Map<String, dynamic>)['readCount'] >
                0)
        .toList();

    // Ordenar por fecha de última lectura (más reciente primero)
    filteredLocalConversations.sort((a, b) {
      var aReadBy = a.readBy[currentUserId] as Map<String, dynamic>;
      var bReadBy = b.readBy[currentUserId] as Map<String, dynamic>;

      if (aReadBy.containsKey('lastReadAt') &&
          bReadBy.containsKey('lastReadAt')) {
        var aLastRead = aReadBy['lastReadAt'];
        var bLastRead = bReadBy['lastReadAt'];

        if (aLastRead is Timestamp && bLastRead is Timestamp) {
          return bLastRead.compareTo(aLastRead);
        }
      }

      return 0;
    });

    if (filteredLocalConversations.isNotEmpty) {
      yield filteredLocalConversations;
    }

    // Luego intentar obtener las conversaciones de Firebase si hay conexión
    if (await isConnected) {
      try {
        await for (var snapshot in _conversationsCollection
            .where('readBy.$currentUserId', isNull: false)
            .orderBy('readBy.$currentUserId.lastReadAt', descending: true)
            .snapshots()) {
          List<ConversationModel> firebaseConversations = snapshot.docs
              .map((doc) => ConversationModel.fromFirestore(doc))
              .toList();

          // Guardar las conversaciones localmente
          for (var conversation in firebaseConversations) {
            await _saveConversationLocally(conversation);
          }

          yield firebaseConversations;
        }
      } catch (e) {
        // Si hay un error, devolver las conversaciones locales
        if (filteredLocalConversations.isEmpty) {
          yield [];
        }
      }
    }
  }

  // Obtener una conversación específica
  Future<ConversationModel?> getConversation(String conversationId) async {
    // Primero intentar obtener la conversación localmente
    ConversationModel? localConversation =
        await _getConversationLocally(conversationId);

    // Si hay conexión a Internet, intentar obtener la conversación de Firebase
    if (await isConnected) {
      try {
        DocumentSnapshot doc =
            await _conversationsCollection.doc(conversationId).get();
        if (doc.exists) {
          ConversationModel firebaseConversation =
              ConversationModel.fromFirestore(doc);

          // Guardar la conversación localmente
          await _saveConversationLocally(firebaseConversation);

          return firebaseConversation;
        }
      } catch (e) {
        // Si hay un error, devolver la conversación local si existe
        if (localConversation != null) {
          return localConversation;
        }
        return null;
      }
    }

    // Si no hay conexión o no se encontró en Firebase, devolver la conversación local
    return localConversation;
  }

  // Crear una nueva conversación
  Future<String> createConversation(String title, String category,
      String difficulty, List<MessageModel> messages,
      [String? level]) async {
    // Usar el valor predeterminado si no se proporciona
    level = level ?? '1';
    if (currentUserId == null) throw Exception('Usuario no autenticado');

    String conversationId = '';

    // Intentar guardar en Firebase primero
    if (await isConnected) {
      try {
        // Crear el documento de la conversación
        DocumentReference docRef = await _conversationsCollection.add({
          'title': title,
          'category': category,
          'createdAt': Timestamp.now(),
          'createdBy': currentUserId,
          'messages': messages.map((msg) => msg.toMap()).toList(),
          'readBy': {},
          'difficulty': difficulty,
          'level': level,
        });

        conversationId = docRef.id;

        // Crear el modelo de conversación con el ID de Firebase
        ConversationModel conversation = ConversationModel(
          id: conversationId,
          title: title,
          category: category,
          createdAt: DateTime.now(),
          createdBy: currentUserId!,
          messages: messages,
          readBy: {},
          difficulty: difficulty,
          level: level,
        );

        // Guardar localmente después de guardar en Firebase
        await _saveConversationLocally(conversation);

        return conversationId;
      } catch (e) {
        // Si hay un error al guardar en Firebase, intentar guardar localmente
        debugPrint('Error al guardar en Firebase: $e');
      }
    }

    // Si no hay conexión o hubo un error, guardar localmente con ID temporal
    String localId = 'local_${DateTime.now().millisecondsSinceEpoch}';

    // Crear el modelo de conversación con ID temporal
    ConversationModel conversation = ConversationModel(
      id: localId,
      title: title,
      category: category,
      createdAt: DateTime.now(),
      createdBy: currentUserId!,
      messages: messages,
      readBy: {},
      difficulty: difficulty,
      level: level,
    );

    // Guardar localmente
    await _saveConversationLocally(conversation);

    return localId;
  }

  // Actualizar el progreso de lectura de una conversación
  Future<void> updateReadProgress(
      String conversationId, int messageIndex, double pronunciationScore,
      {int? levelId, int? cycleId, int? groupId}) async {
    if (currentUserId == null) throw Exception('Usuario no autenticado');

    // Obtener la conversación actual
    ConversationModel? conversation = await getConversation(conversationId);
    if (conversation == null) throw Exception('Conversación no encontrada');

    // Actualizar el progreso
    ConversationModel updatedConversation = conversation.updateReadProgress(
      currentUserId!,
      messageIndex,
      pronunciationScore,
    );

    // Actualizar localmente primero
    await _saveConversationLocally(updatedConversation);

    // Actualizar en Firestore si hay conexión
    if (await isConnected) {
      try {
        // Actualizar el progreso de lectura en la colección de conversaciones
        await _conversationsCollection.doc(conversationId).update({
          'readBy': updatedConversation.readBy,
        });

        // Guardar también en la colección readSentences para que aparezca en la revisión
        if (levelId != null && cycleId != null && groupId != null) {
          debugPrint(
              'Guardando conversación en readSentences: $conversationId');
          debugPrint('levelId: $levelId, cycleId: $cycleId, groupId: $groupId');

          await _firestore
              .collection('users')
              .doc(currentUserId)
              .collection('readSentences')
              .doc(conversationId)
              .set({
            'readAt': FieldValue.serverTimestamp(),
            'levelId': levelId,
            'cycleId': cycleId,
            'groupId': groupId,
            'isConversation': true, // Marcar como conversación para diferenciar
          });

          debugPrint('Conversación guardada en readSentences correctamente');
        } else {
          debugPrint(
              'No se guardó en readSentences porque faltan parámetros de nivel/ciclo/grupo');
        }
      } catch (e) {
        debugPrint('Error al actualizar progreso de lectura: $e');
        // Si hay un error, al menos ya se guardó localmente
      }
    }
  }

  // Actualizar los resultados de la prueba de un mensaje
  Future<void> updateMessageTestResults(
    String conversationId,
    String messageId,
    double score,
  ) async {
    if (currentUserId == null) throw Exception('Usuario no autenticado');

    // Obtener la conversación actual
    ConversationModel? conversation = await getConversation(conversationId);
    if (conversation == null) throw Exception('Conversación no encontrada');

    // Encontrar el índice del mensaje
    int messageIndex =
        conversation.messages.indexWhere((msg) => msg.id == messageId);
    if (messageIndex == -1) throw Exception('Mensaje no encontrado');

    // Actualizar el mensaje
    MessageModel message = conversation.messages[messageIndex];
    MessageModel updatedMessage =
        message.updateTestResults(currentUserId!, score);

    // Actualizar la lista de mensajes
    List<MessageModel> updatedMessages = List.from(conversation.messages);
    updatedMessages[messageIndex] = updatedMessage;

    // Crear una conversación actualizada
    ConversationModel updatedConversation = conversation.copyWith(
      messages: updatedMessages,
    );

    // Actualizar también la puntuación de pronunciación global
    updatedConversation =
        updatedConversation.updatePronunciationScore(currentUserId!);

    // Guardar localmente primero
    await _saveConversationLocally(updatedConversation);

    // Actualizar en Firestore si hay conexión
    if (await isConnected) {
      try {
        await _conversationsCollection.doc(conversationId).update({
          'messages': updatedMessages.map((msg) => msg.toMap()).toList(),
        });

        // También actualizar el progreso de lectura en Firestore
        await _conversationsCollection.doc(conversationId).update({
          'readBy': updatedConversation.readBy,
        });
      } catch (e) {
        // Si hay un error, al menos ya se guardó localmente
      }
    }
  }

  // Sincronizar todas las conversaciones desde Firebase
  Future<List<ConversationModel>> syncAllConversations() async {
    if (!await isConnected) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    try {
      // Obtener todas las conversaciones de Firebase
      QuerySnapshot snapshot = await _conversationsCollection
          .orderBy('createdAt', descending: true)
          .get();

      List<ConversationModel> firebaseConversations = snapshot.docs
          .map((doc) => ConversationModel.fromFirestore(doc))
          .toList();

      // Guardar las conversaciones localmente
      for (var conversation in firebaseConversations) {
        await _saveConversationLocally(conversation);
      }

      return firebaseConversations;
    } catch (e) {
      debugPrint('Error al sincronizar conversaciones: $e');
      rethrow;
    }
  }
}
