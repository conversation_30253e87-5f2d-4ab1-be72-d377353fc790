import 'package:flutter/material.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:provider/provider.dart';
import '../models/level.dart';
import '../models/lesson_group.dart';
import '../models/cycle.dart';
import '../providers/level_provider_temp.dart';
import '../providers/points_provider.dart';
import '../widgets/lesson_group_card.dart';
import '../utils/app_colors.dart';
import 'daily_sentences_screen.dart';
import 'conversation_screen.dart';
import 'review_screen.dart';

class TimelineScreen extends StatefulWidget {
  static const routeName = '/timeline';

  const TimelineScreen({Key? key}) : super(key: key);

  @override
  State<TimelineScreen> createState() => _TimelineScreenState();
}

class _TimelineScreenState extends State<TimelineScreen> {
  bool _isLoading = true;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // تحميل بيانات المستويات والنقاط
    final levelProvider = Provider.of<LevelProvider>(context, listen: false);
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);

    await Future.wait([
      levelProvider.fetchLevels(),
      pointsProvider.fetchPoints(),
    ]);

    setState(() {
      _isLoading = false;
    });

    // التمرير إلى المستوى الحالي بعد تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentLevel(levelProvider.levels);
    });
  }

  void _scrollToCurrentLevel(List<Level> levels) {
    final currentLevelIndex = levels.indexWhere((level) => level.isCurrent);
    if (currentLevelIndex != -1) {
      final scrollPosition = currentLevelIndex * 200.0; // تقدير تقريبي للموضع
      _scrollController.animateTo(
        scrollPosition,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مسار التعلم'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<LevelProvider>(
              builder: (ctx, levelProvider, _) {
                final levels = levelProvider.levels;
                if (levels.isEmpty) {
                  return const Center(
                    child: Text('لا توجد مستويات متاحة حاليًا'),
                  );
                }

                return RefreshIndicator(
                  onRefresh: _loadData,
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(
                        vertical: 20, horizontal: 16),
                    itemCount: levels.length,
                    itemBuilder: (ctx, index) {
                      final level = levels[index];
                      return _buildLevelTimelineTile(
                          level, index == 0, index == levels.length - 1);
                    },
                  ),
                );
              },
            ),
    );
  }

  Widget _buildLevelTimelineTile(Level level, bool isFirst, bool isLast) {
    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.2,
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        width: 40,
        height: 40,
        indicator: _buildLevelIndicator(level),
        drawGap: true,
      ),
      beforeLineStyle: LineStyle(
        color: level.isLocked
            ? Colors.grey.shade300
            : AppColors.getLevelColor(level.id),
        thickness: 3,
      ),
      afterLineStyle: LineStyle(
        color: level.isLocked
            ? Colors.grey.shade300
            : AppColors.getLevelColor(level.id),
        thickness: 3,
      ),
      endChild: _buildLevelContent(level),
    );
  }

  Widget _buildLevelIndicator(Level level) {
    return Container(
      decoration: BoxDecoration(
        color: level.isLocked
            ? Colors.grey.shade300
            : level.isCurrent
                ? AppColors.getLevelColor(level.id)
                : Colors.green,
        shape: BoxShape.circle,
        border: Border.all(
          color: level.isCurrent ? AppColors.accentColor : Colors.transparent,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: level.isLocked
                ? Colors.grey.withAlpha(76) // 0.3 * 255 = 76
                : AppColors.getLevelColor(level.id)
                    .withAlpha(128), // 0.5 * 255 = 128
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Center(
        child: level.isLocked
            ? const Icon(Icons.lock, color: Colors.white, size: 20)
            : Text(
                '${level.id}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
      ),
    );
  }

  Widget _buildLevelContent(Level level) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 8, top: 8, bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المستوى وشريط التقدم
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: level.isLocked
                  ? Colors.grey.shade100
                  : AppColors.getLevelColor(level.id).withAlpha(30),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'المستوى ${level.id}: ${level.title}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: level.isLocked ? Colors.grey : Colors.black,
                        ),
                      ),
                    ),
                    if (!level.isLocked)
                      Consumer<PointsProvider>(
                        builder: (ctx, pointsProvider, _) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppColors.getLevelColor(level.id)
                                  .withAlpha(50),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  color: AppColors.getLevelColor(level.id),
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${level.earnedEducationalPoints}/${level.totalEducationalPoints}',
                                  style: TextStyle(
                                    color: AppColors.getLevelColor(level.id),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
                if (!level.isLocked) ...[
                  const SizedBox(height: 8),
                  // شريط التقدم
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: level.earnedEducationalPoints /
                          level.totalEducationalPoints,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        level.isCurrent
                            ? AppColors.getLevelColor(level.id)
                            : Colors.green,
                      ),
                      minHeight: 8,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!level.isLocked) ...[
            // عرض الدورات
            ...level.cycles.map((cycle) => _buildCycleWidget(level, cycle)),
          ] else ...[
            // رسالة المستوى المغلق
            _buildLockedLevelMessage(level),
          ],
        ],
      ),
    );
  }

  // بناء واجهة الدورة
  Widget _buildCycleWidget(Level level, Cycle cycle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: cycle.isLocked
            ? Colors.grey.shade100
            : AppColors.getLevelColor(level.id).withAlpha(15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: cycle.isLocked
              ? Colors.grey.shade300
              : AppColors.getLevelColor(level.id).withAlpha(50),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الدورة وشريط التقدم
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: cycle.isLocked
                  ? Colors.grey.shade200
                  : AppColors.getLevelColor(level.id).withAlpha(30),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: cycle.isLocked
                            ? Colors.grey.shade400
                            : AppColors.getLevelColor(level.id),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: cycle.isLocked
                            ? const Icon(Icons.lock,
                                color: Colors.white, size: 16)
                            : Text(
                                '${cycle.id}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        cycle.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: cycle.isLocked ? Colors.grey : Colors.black,
                        ),
                      ),
                    ),
                    if (!cycle.isLocked)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color:
                              AppColors.getLevelColor(level.id).withAlpha(40),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: AppColors.getLevelColor(level.id),
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${cycle.completedSentences}/${cycle.totalSentences}',
                              style: TextStyle(
                                color: AppColors.getLevelColor(level.id),
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                if (!cycle.isLocked) ...[
                  const SizedBox(height: 8),
                  // شريط التقدم
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: cycle.totalSentences > 0
                          ? cycle.completedSentences / cycle.totalSentences
                          : 0.0,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        cycle.isCompleted
                            ? Colors.green
                            : AppColors.getLevelColor(level.id),
                      ),
                      minHeight: 6,
                    ),
                  ),
                ],
              ],
            ),
          ),
          // مجموعات الدروس
          if (!cycle.isLocked) ...[
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...cycle.lessonGroups.map((group) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: _buildLessonGroupCard(level, cycle, group),
                      )),
                ],
              ),
            ),
          ] else ...[
            // رسالة الدورة المغلقة
            _buildLockedCycleMessage(level, cycle),
          ],
        ],
      ),
    );
  }

  // بناء بطاقة مجموعة الدروس
  Widget _buildLessonGroupCard(Level level, Cycle cycle, LessonGroup group) {
    final isLocked = level.isLocked || cycle.isLocked || group.isLocked;
    final canAccess = _canAccessGroup(cycle, group);

    return LessonGroupCard(
      lessonGroup: group,
      levelId: level.id,
      cycleId: cycle.id,
      isLocked: isLocked,
      canAccess: canAccess,
      onTap: () => _navigateToLesson(level, cycle, group, canAccess),
    );
  }

  // بناء رسالة المستوى المغلق
  Widget _buildLockedLevelMessage(Level level) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.lock, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'هذا المستوى مغلق',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'أكمل ${level.requiredSentences} جملة لفتح هذا المستوى',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء رسالة الدورة المغلقة
  Widget _buildLockedCycleMessage(Level level, Cycle cycle) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.lock, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'هذه الدورة مغلقة',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'أكمل الدورة السابقة لفتح هذه الدورة',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // التحقق مما إذا كان يمكن الوصول إلى مجموعة معينة
  bool _canAccessGroup(Cycle cycle, LessonGroup group) {
    // المجموعة الأولى دائمًا يمكن الوصول إليها
    if (group.id == 1) return true;

    // البحث عن المجموعة السابقة
    final previousGroupId = group.id - 1;
    final previousGroup = cycle.lessonGroups.firstWhere(
      (g) => g.id == previousGroupId,
      orElse: () => LessonGroup(
        id: 0,
        cycleId: cycle.id,
        globalId: 0,
        title: '',
        type: LessonType.sentenceBatch,
        totalSentences: 0,
        completedSentences: 0,
        accuracy: 0,
        isCompleted: true, // اعتبار المجموعة غير الموجودة مكتملة
        isLocked: false,
      ),
    );

    // يمكن الوصول إلى المجموعة فقط إذا كانت المجموعة السابقة مكتملة
    return previousGroup.isCompleted;
  }

  // التنقل إلى الدرس المناسب
  void _navigateToLesson(
      Level level, Cycle cycle, LessonGroup group, bool canAccess) {
    if (level.isLocked || cycle.isLocked || group.isLocked) return;

    // التحقق مما إذا كان يمكن الوصول إلى المجموعة
    if (!canAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إكمال المجموعة السابقة أولاً'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // التنقل إلى الصفحة المناسبة بناءً على نوع الدرس
    switch (group.type) {
      case LessonType.sentenceBatch:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DailySentencesScreen(
              levelId: level.id,
              cycleId: cycle.id,
              groupId: group.id,
              title: group.title,
            ),
          ),
        );
        break;
      case LessonType.conversation:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ConversationScreen(
              levelId: level.id,
              cycleId: cycle.id,
              groupId: group.id,
              title: group.title,
            ),
          ),
        );
        break;
      case LessonType.review:
        // التنقل إلى صفحة المراجعة مع تفعيل عرض المجموعات
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReviewScreen(
              levelId: level.id,
              cycleId: cycle.id,
              groupId: group.id,
              title: group.title,
              showGroups: true, // عرض بطاقات المجموعات بدلاً من الجمل مباشرة
            ),
          ),
        );
        break;
    }
  }
}
