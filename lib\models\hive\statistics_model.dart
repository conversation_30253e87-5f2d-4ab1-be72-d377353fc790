import 'package:hive/hive.dart';

part 'statistics_model.g.dart';

/// نموذج الإحصائيات المخزنة في Hive
@HiveType(typeId: 5)
class StatisticsModel extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final DateTime date;
  
  @HiveField(2)
  int shownCount;
  
  @HiveField(3)
  int readCount;
  
  @HiveField(4)
  DateTime lastUpdated;
  
  @HiveField(5)
  bool isSynced;

  StatisticsModel({
    required this.id,
    required this.date,
    this.shownCount = 0,
    this.readCount = 0,
    required this.lastUpdated,
    this.isSynced = false,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'shownCount': shownCount,
      'readCount': readCount,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isSynced': isSynced,
    };
  }

  // زيادة عدد الجمل المعروضة
  void incrementShownCount(int count) {
    shownCount += count;
    lastUpdated = DateTime.now();
    isSynced = false;
    save();
  }

  // زيادة عدد الجمل المقروءة
  void incrementReadCount() {
    readCount++;
    lastUpdated = DateTime.now();
    isSynced = false;
    save();
  }

  // تعيين حالة المزامنة
  void markAsSynced() {
    isSynced = true;
    save();
  }
}
