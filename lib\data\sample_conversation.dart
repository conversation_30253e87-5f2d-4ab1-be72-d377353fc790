import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/message_model.dart';
import '../models/conversation_model.dart';

class SampleConversationData {
  static ConversationModel getSampleConversation() {
    final List<MessageModel> messages = [
      MessageModel(
        id: '1',
        englishText:
            'I\'ve been learning Arabic for three months now. It\'s challenging but fascinating!',
        arabicText:
            'لقد كنت أتعلم اللغة العربية منذ ثلاثة أشهر. إنها صعبة ولكنها مثيرة للاهتمام!',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '2',
        englishText: 'That\'s impressive! What motivated you to learn Arabic?',
        arabicText: 'هذا مثير للإعجاب! ما الذي حفزك لتعلم اللغة العربية؟',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 29)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '3',
        englishText:
            'I\'ve always been fascinated by the culture and history of the Middle East. Plus, Arabic literature is incredibly rich.',
        arabicText:
            'لطالما كنت مفتونًا بثقافة وتاريخ الشرق الأوسط. بالإضافة إلى ذلك، الأدب العربي غني بشكل لا يصدق.',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 28)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '4',
        englishText:
            'That\'s wonderful! How are you learning? Are you taking classes or using an app?',
        arabicText: 'هذا رائع! كيف تتعلم؟ هل تأخذ دروسًا أم تستخدم تطبيقًا؟',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 27)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '5',
        englishText:
            'I\'m using a combination of methods. I have a tutor twice a week, and I use language apps daily. I also try to watch Arabic shows with subtitles.',
        arabicText:
            'أنا أستخدم مجموعة من الطرق. لدي مدرس مرتين في الأسبوع، وأستخدم تطبيقات اللغة يوميًا. كما أحاول مشاهدة البرامج العربية مع الترجمات.',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 26)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '6',
        englishText:
            'That sounds like a great approach! What has been the most difficult aspect of learning Arabic for you?',
        arabicText:
            'يبدو ذلك نهجًا رائعًا! ما هو الجانب الأكثر صعوبة في تعلم اللغة العربية بالنسبة لك؟',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 25)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '7',
        englishText:
            'Definitely the pronunciation of certain sounds that don\'t exist in English, like the \'ayn and the ghayn. And remembering the different verb forms can be tricky too.',
        arabicText:
            'بالتأكيد نطق بعض الأصوات التي لا توجد في اللغة الإنجليزية، مثل العين والغين. وتذكر أشكال الأفعال المختلفة يمكن أن يكون صعبًا أيضًا.',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 24)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '8',
        englishText:
            'Yes, those sounds can be challenging! Have you tried practicing with native speakers? That can really help with pronunciation.',
        arabicText:
            'نعم، تلك الأصوات يمكن أن تكون صعبة! هل جربت التدرب مع متحدثين أصليين؟ يمكن أن يساعد ذلك حقًا في النطق.',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 23)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '9',
        englishText:
            'I\'ve joined a language exchange group that meets once a week. It\'s been incredibly helpful, and I\'ve made some good friends too!',
        arabicText:
            'لقد انضممت إلى مجموعة تبادل لغوي تجتمع مرة في الأسبوع. لقد كان مفيدًا بشكل لا يصدق، وقد كوّنت بعض الصداقات الجيدة أيضًا!',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 22)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '10',
        englishText:
            'That\'s the best way to learn! Would you be interested in visiting an Arabic-speaking country someday to practice your skills?',
        arabicText:
            'هذه هي أفضل طريقة للتعلم! هل ستكون مهتمًا بزيارة بلد ناطق باللغة العربية يومًا ما لممارسة مهاراتك؟',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 21)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '11',
        englishText:
            'Absolutely! I\'m actually planning a trip to Jordan next summer. I\'ve heard Amman is a great place for language learners.',
        arabicText:
            'بالتأكيد! أنا في الواقع أخطط لرحلة إلى الأردن الصيف المقبل. سمعت أن عمان مكان رائع لمتعلمي اللغة.',
        isPersonA: true,
        createdAt: DateTime.now().subtract(const Duration(minutes: 20)),
        readBy: {},
        testResults: {},
      ),
      MessageModel(
        id: '12',
        englishText:
            'Jordan is a wonderful choice! The people are very friendly and the historical sites like Petra are breathtaking. Your Arabic will improve dramatically there.',
        arabicText:
            'الأردن خيار رائع! الناس ودودون للغاية والمواقع التاريخية مثل البتراء مذهلة. ستتحسن لغتك العربية بشكل كبير هناك.',
        isPersonA: false,
        createdAt: DateTime.now().subtract(const Duration(minutes: 19)),
        readBy: {},
        testResults: {},
      ),
    ];

    return ConversationModel(
      id: 'sample1',
      title: 'محادثة حول تعلم اللغة العربية',
      category: 'education',
      createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      createdBy: 'admin',
      messages: messages,
      readBy: {},
      difficulty: 'medium',
    );
  }

  // Método para crear la conversación de ejemplo en Firestore
  static Future<void> createSampleConversationInFirestore() async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    final ConversationModel sampleConversation = getSampleConversation();

    await firestore
        .collection('conversations')
        .doc('sample1')
        .set(sampleConversation.toMap());
  }
}
