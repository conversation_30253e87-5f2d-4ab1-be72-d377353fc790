import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import '../../providers/level_provider.dart';
import '../../viewmodels/auth_view_model.dart';
import '../../models/level.dart';
import '../../models/lesson_group.dart';
import '../../models/cycle.dart';

/// شاشة إعدادات المستويات للمسؤولين
class LevelsSettingsScreen extends StatefulWidget {
  const LevelsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<LevelsSettingsScreen> createState() => _LevelsSettingsScreenState();
}

class _LevelsSettingsScreenState extends State<LevelsSettingsScreen> {
  bool _isLoading = false;
  String? _error;
  List<Level> _levels = [];
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _loadLevels();
  }

  @override
  void dispose() {
    // التخلص من وحدات التحكم في النص
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // تحميل المستويات
  Future<void> _loadLevels() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // جلب المستويات من Firebase
      final levelsSnapshot =
          await FirebaseFirestore.instance.collection('levels').get();

      final List<Level> fetchedLevels = [];

      for (var doc in levelsSnapshot.docs) {
        final levelData = doc.data();
        final levelId = int.parse(doc.id);

        // جلب مجموعات الدروس لهذا المستوى
        List<LessonGroup> lessonGroups = [];
        try {
          final lessonGroupsSnapshot = await FirebaseFirestore.instance
              .collection('levels')
              .doc(doc.id)
              .collection('lessonGroups')
              .get();

          for (var groupDoc in lessonGroupsSnapshot.docs) {
            final groupData = groupDoc.data();
            final groupId = int.parse(groupDoc.id);

            // إنشاء وحدات تحكم لمجموعات الدروس
            _controllers['level_${levelId}_group_${groupId}_totalSentences'] =
                TextEditingController(
                    text: groupData['totalSentences']?.toString() ?? '0');

            lessonGroups.add(LessonGroup(
              id: groupId,
              cycleId: 1, // Default cycle ID
              globalId: levelId * 100 + groupId, // Generate a unique global ID
              title: groupData['title'] ?? '',
              type: LessonTypeExtension.fromJson(
                  groupData['type'] ?? 'sentenceBatch'),
              totalSentences: groupData['totalSentences'] ?? 0,
              completedSentences: 0,
              accuracy: 0.0,
              isCompleted: false,
              isLocked: groupData['isLocked'] ?? false,
              routePath: groupData['routePath'],
            ));
          }
        } catch (e) {
          debugPrint('خطأ في جلب مجموعات الدروس للمستوى $levelId: $e');
        }

        // إنشاء وحدات تحكم للمستوى
        _controllers['level_${levelId}_requiredSentences'] =
            TextEditingController(
                text: levelData['requiredSentences']?.toString() ?? '0');
        _controllers['level_${levelId}_totalEducationalPoints'] =
            TextEditingController(
                text: levelData['totalEducationalPoints']?.toString() ?? '0');

        // إنشاء وحدة تحكم لمستوى الصعوبة
        String difficulty = levelData['difficulty'] ?? 'medium';
        _controllers['level_${levelId}_difficulty'] =
            TextEditingController(text: difficulty);

        // Crear un ciclo por defecto con los grupos de lecciones
        List<Cycle> cycles = [
          Cycle(
            id: 1,
            title: 'الدورة الأولى',
            isLocked: false,
            isCompleted: false,
            lessonGroups: lessonGroups,
            totalSentences: lessonGroups.fold(
                0, (sum, group) => sum + group.totalSentences),
            completedSentences: 0,
            accuracy: 0.0,
          )
        ];

        fetchedLevels.add(Level(
          id: levelId,
          title: levelData['title'] ?? '',
          isLocked: false,
          isCurrent: false,
          requiredSentences: levelData['requiredSentences'] ?? 0,
          cycles: cycles,
          totalEducationalPoints: levelData['totalEducationalPoints'] ?? 0,
          earnedEducationalPoints: 0,
          difficulty: levelData['difficulty'] ?? 'medium',
        ));
      }

      // ترتيب المستويات حسب المعرف
      fetchedLevels.sort((a, b) => a.id.compareTo(b.id));

      setState(() {
        _levels = fetchedLevels;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء تحميل المستويات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // حفظ إعدادات المستويات
  Future<void> _saveLevelsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحديث كل مستوى
      for (var level in _levels) {
        final levelRef = FirebaseFirestore.instance
            .collection('levels')
            .doc(level.id.toString());

        // تحديث بيانات المستوى
        await levelRef.update({
          'requiredSentences': int.parse(
              _controllers['level_${level.id}_requiredSentences']!.text),
          'totalEducationalPoints': int.parse(
              _controllers['level_${level.id}_totalEducationalPoints']!.text),
          'difficulty': _controllers['level_${level.id}_difficulty']!.text,
        });

        // تحديث مجموعات الدروس
        if (level.cycles.isNotEmpty) {
          for (var cycle in level.cycles) {
            final cycleRef =
                levelRef.collection('cycles').doc(cycle.id.toString());

            // Actualizar la información del ciclo
            await cycleRef.set({
              'title': cycle.title,
              'isLocked': cycle.isLocked,
              'isCompleted': cycle.isCompleted,
              'totalSentences': cycle.totalSentences,
            });

            // Actualizar las lecciones del ciclo
            for (var group in cycle.lessonGroups) {
              final groupRef =
                  cycleRef.collection('lessonGroups').doc(group.id.toString());

              await groupRef.update({
                'totalSentences': int.parse(_controllers[
                        'level_${level.id}_group_${group.id}_totalSentences']!
                    .text),
              });
            }
          }
        }
      }

      // تحديث مزود المستويات
      if (mounted) {
        final levelProvider =
            Provider.of<LevelProvider>(context, listen: false);
        await levelProvider.fetchLevels();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ إعدادات المستويات بنجاح')),
        );
      }
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء حفظ إعدادات المستويات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authViewModel = Provider.of<AuthViewModel>(context);

    // التحقق من أن المستخدم مسؤول
    if (!authViewModel.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('إعدادات المستويات')),
        body: const Center(
          child: Text('غير مصرح لك بالوصول إلى هذه الصفحة'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المستويات'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ..._levels.map((level) => _buildLevelCard(level)),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveLevelsSettings,
                          child: _isLoading
                              ? const CircularProgressIndicator()
                              : const Text('حفظ الإعدادات'),
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  // بناء بطاقة المستوى
  Widget _buildLevelCard(Level level) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المستوى ${level.id}: ${level.title}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingField(
              'level_${level.id}_requiredSentences',
              'عدد الجمل المطلوبة للترقية',
            ),
            _buildSettingField(
              'level_${level.id}_totalEducationalPoints',
              'إجمالي النقاط التعليمية',
            ),
            _buildDifficultyField(
              'level_${level.id}_difficulty',
              'مستوى الصعوبة',
            ),
            const SizedBox(height: 16),
            const Text(
              'مجموعات الدروس',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...level.cycles.expand((cycle) => cycle.lessonGroups
                .map((group) => _buildLessonGroupCard(level.id, group))),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة مجموعة الدروس
  Widget _buildLessonGroupCard(int levelId, LessonGroup group) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${group.title} (${group.type.arabicName})',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildSettingField(
              'level_${levelId}_group_${group.id}_totalSentences',
              'عدد الجمل',
            ),
          ],
        ),
      ),
    );
  }

  // بناء حقل إعداد
  Widget _buildSettingField(String key, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: TextFormField(
        controller: _controllers[key],
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        keyboardType: TextInputType.number,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال قيمة';
          }
          if (int.tryParse(value) == null) {
            return 'يرجى إدخال رقم صحيح';
          }
          return null;
        },
      ),
    );
  }

  // بناء حقل مستوى الصعوبة
  Widget _buildDifficultyField(String key, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: DropdownButtonFormField<String>(
        value: _controllers[key]?.text ?? 'medium',
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        items: const [
          DropdownMenuItem(
            value: 'easy',
            child: Text('سهل'),
          ),
          DropdownMenuItem(
            value: 'medium',
            child: Text('متوسط'),
          ),
          DropdownMenuItem(
            value: 'hard',
            child: Text('صعب'),
          ),
        ],
        onChanged: (value) {
          if (value != null) {
            _controllers[key]?.text = value;
          }
        },
      ),
    );
  }
}
