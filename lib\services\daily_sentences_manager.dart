import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/hive_constants.dart';
import '../models/hive/hive_sentence_model.dart';

/// خدمة إدارة الجمل اليومية - نسخة محسنة
/// تقوم هذه الخدمة بإدارة الجمل اليومية باستخدام Hive كمصدر أساسي للبيانات
/// مع إمكانية جلب الجمل من Firebase عند الحاجة
class DailySentencesManager {
  // الحصول على صناديق Hive
  final Box<HiveSentenceModel> _sentencesBox =
      Hive.box<HiveSentenceModel>(HiveConstants.sentencesBox);
  final Box<String> _readSentencesBox =
      Hive.box<String>(HiveConstants.readSentencesBox);
  final Box _userBox = Hive.box(HiveConstants.userBox);

  // الوصول إلى Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // اسم المفتاح الذي سيتم استخدامه لتخزين الجمل اليومية
  static const String _dailySentencesKey = 'daily_sentences_ids';
  static const String _todayDateKey = HiveConstants.todayDateKey;
  static const String _todayShownCountKey = HiveConstants.todayShownCountKey;
  static const String _todayReadCountKey = HiveConstants.todayReadCountKey;

  // اسم مجموعة الجمل في Firestore
  static const String _sentencesCollection = 'sentences';

  /// الحصول على الجمل اليومية الحالية
  /// يمكن تحديد ما إذا كنت تريد فقط الجمل غير المقروءة
  List<HiveSentenceModel> getDailySentences({bool onlyUnread = false}) {
    try {
      // الحصول على معرفات الجمل اليومية
      final dailySentenceIds = _getDailySentenceIds();

      if (dailySentenceIds.isEmpty) {
        debugPrint('لا توجد جمل يومية حالية');
        return [];
      }

      // الحصول على الجمل من صندوق الجمل
      var sentences = dailySentenceIds
          .map((id) => _sentencesBox.get(id))
          .whereType<HiveSentenceModel>()
          .toList();

      // تحديث حالة القراءة من صندوق الجمل المقروءة للتأكد من التزامن
      _updateReadStatusFromReadBox(sentences);

      // تصفية الجمل غير المقروءة إذا طلب ذلك
      if (onlyUnread) {
        sentences = sentences.where((s) => !s.isReadByCurrentUser).toList();
      }

      debugPrint(
          'تم الحصول على ${sentences.length} جملة يومية${onlyUnread ? " غير مقروءة" : ""}');
      return sentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجمل اليومية: $e');
      return [];
    }
  }

  /// الحصول على جميع الجمل المخزنة محليًا
  List<HiveSentenceModel> getAllSentences() {
    try {
      // الحصول على جميع الجمل من صندوق الجمل
      final sentences = _sentencesBox.values.toList();

      // تحديث حالة القراءة من صندوق الجمل المقروءة للتأكد من التزامن
      _updateReadStatusFromReadBox(sentences);

      debugPrint('تم الحصول على ${sentences.length} جملة من التخزين المحلي');
      return sentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على جميع الجمل: $e');
      return [];
    }
  }

  /// تحديث حالة القراءة من صندوق الجمل المقروءة
  void _updateReadStatusFromReadBox(List<HiveSentenceModel> sentences) {
    try {
      final readIds = _readSentencesBox.keys.cast<String>().toList();

      for (var sentence in sentences) {
        // إذا كانت الجملة في صندوق الجمل المقروءة ولكنها غير معلمة كمقروءة في النموذج
        if (readIds.contains(sentence.id) && !sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = true;
          sentence.save();
          debugPrint(
              'تم تحديث حالة القراءة للجملة ${sentence.id} من صندوق الجمل المقروءة');
        }
        // إذا كانت الجملة ليست في صندوق الجمل المقروءة ولكنها معلمة كمقروءة في النموذج
        else if (!readIds.contains(sentence.id) &&
            sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = false;
          sentence.save();
          debugPrint(
              'تم تصحيح حالة القراءة للجملة ${sentence.id} لتكون غير مقروءة');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة القراءة من صندوق الجمل المقروءة: $e');
    }
  }

  /// الحصول على معرفات الجمل اليومية
  List<String> _getDailySentenceIds() {
    try {
      final dailySentenceIds =
          _userBox.get(_dailySentencesKey, defaultValue: <String>[]);

      if (dailySentenceIds is List) {
        return List<String>.from(dailySentenceIds);
      }

      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على معرفات الجمل اليومية: $e');
      return [];
    }
  }

  /// تعيين الجمل اليومية
  Future<void> setDailySentences(List<String> sentenceIds) async {
    try {
      await _userBox.put(_dailySentencesKey, sentenceIds);
      await _userBox.flush(); // ضمان حفظ التغييرات على القرص
      debugPrint('تم تعيين ${sentenceIds.length} جملة يومية');
    } catch (e) {
      debugPrint('خطأ في تعيين الجمل اليومية: $e');
    }
  }

  /// مسح الجمل اليومية
  Future<void> clearDailySentences() async {
    try {
      await _userBox.put(_dailySentencesKey, <String>[]);
      await _userBox.flush(); // ضمان حفظ التغييرات على القرص
      debugPrint('تم مسح الجمل اليومية');
    } catch (e) {
      debugPrint('خطأ في مسح الجمل اليومية: $e');
    }
  }

  /// إزالة جملة من الجمل اليومية
  Future<void> removeSentenceFromDaily(String sentenceId) async {
    try {
      final dailySentenceIds = _getDailySentenceIds();

      if (dailySentenceIds.contains(sentenceId)) {
        dailySentenceIds.remove(sentenceId);
        await setDailySentences(dailySentenceIds);
        debugPrint('تم إزالة الجملة $sentenceId من الجمل اليومية');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الجملة من الجمل اليومية: $e');
    }
  }

  /// تعليم جملة كمقروءة
  Future<bool> markSentenceAsRead(String sentenceId) async {
    try {
      // إضافة الجملة إلى صندوق الجمل المقروءة
      if (!_readSentencesBox.containsKey(sentenceId)) {
        await _readSentencesBox.put(sentenceId, sentenceId);
        await _readSentencesBox.flush(); // ضمان حفظ التغييرات على القرص
        debugPrint('تم إضافة الجملة $sentenceId إلى صندوق الجمل المقروءة');
      }

      // تحديث حالة الجملة في صندوق الجمل
      final sentence = _sentencesBox.get(sentenceId);
      if (sentence != null && !sentence.isReadByCurrentUser) {
        sentence.isReadByCurrentUser = true;
        await sentence.save();
        await _sentencesBox.flush(); // ضمان حفظ التغييرات على القرص
        debugPrint('تم تحديث حالة القراءة للجملة $sentenceId في صندوق الجمل');
      }

      // زيادة عداد الجمل المقروءة اليوم
      await incrementTodayReadCount();

      // إزالة الجملة من الجمل اليومية
      // ملاحظة: لا نقوم بإزالة الجملة من الجمل اليومية، فقط نعلمها كمقروءة
      // وسيتم تصفيتها عند استخدام getDailySentences(onlyUnread: true)

      debugPrint('تم تعليم الجملة $sentenceId كمقروءة بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تعليم الجملة كمقروءة: $e');
      return false;
    }
  }

  /// جلب الجمل من Firebase وتخزينها في Hive
  Future<List<HiveSentenceModel>> fetchSentencesFromFirebase(
      String userId, int limit) async {
    try {
      debugPrint('جلب الجمل من Firebase للمستخدم $userId');

      // الحصول على الجمل المقروءة من Firestore
      final readSentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .get();

      // إنشاء مجموعة من معرفات الجمل المقروءة
      final readSentenceIds =
          readSentencesSnapshot.docs.map((doc) => doc.id).toSet();

      // إضافة الجمل المقروءة محليًا
      readSentenceIds.addAll(_readSentencesBox.keys.cast<String>());

      // الحصول على الجمل المفضلة
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      final favoriteSentenceIds =
          favoritesSnapshot.docs.map((doc) => doc.id).toSet();

      // الحصول على جميع الجمل من Firestore
      final allSentencesSnapshot =
          await _firestore.collection(_sentencesCollection).get();

      debugPrint(
          'تم العثور على ${allSentencesSnapshot.docs.length} جملة في Firestore');

      // تصفية الجمل غير المقروءة
      final unreadDocs = allSentencesSnapshot.docs
          .where((doc) => !readSentenceIds.contains(doc.id))
          .toList();

      debugPrint(
          'تم العثور على ${unreadDocs.length} جملة غير مقروءة في Firestore');

      if (unreadDocs.isEmpty) {
        debugPrint('لا توجد جمل غير مقروءة في Firestore');
        return [];
      }

      // خلط الجمل بشكل عشوائي
      unreadDocs.shuffle();

      // اختيار جميع الجمل غير المقروءة لتخزينها محليًا
      // تم تعديل هذا الجزء لتخزين جميع الجمل بدلاً من نصفها فقط
      final selectedDocs = unreadDocs.toList();

      debugPrint('سيتم تخزين ${selectedDocs.length} جملة في Hive');

      // تحويل الجمل إلى نماذج Hive وتخزينها
      final hiveSentences = <HiveSentenceModel>[];

      for (final doc in selectedDocs) {
        final data = doc.data();
        final isFavorite = favoriteSentenceIds.contains(doc.id);

        // تحويل البيانات إلى نموذج Hive
        final hiveSentence = HiveSentenceModel(
          id: doc.id,
          arabicText: data['arabicText'] ?? '',
          englishText: data['englishText'] ?? '',
          category: data['category'] ?? '',
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          audioUrl: data['audioUrl'],
          difficulty: data['difficulty'] ?? 'medium',
          isReadByCurrentUser: false,
          isFavoriteByCurrentUser: isFavorite,
          lastModified: DateTime.now(),
        );

        // تخزين الجملة في Hive
        await _sentencesBox.put(doc.id, hiveSentence);

        hiveSentences.add(hiveSentence);
      }

      debugPrint('تم تخزين ${hiveSentences.length} جملة في Hive');

      // إذا كان المطلوب عدد محدد من الجمل للعرض، نعيد فقط العدد المطلوب
      final sentencesToReturn = hiveSentences.take(limit).toList();
      debugPrint('سيتم إرجاع ${sentencesToReturn.length} جملة للعرض');

      return sentencesToReturn;
    } catch (e) {
      debugPrint('خطأ في جلب الجمل من Firebase: $e');
      return [];
    }
  }

  /// جلب جميع الجمل المتاحة من Firebase وتخزينها في Hive
  Future<int> fetchAllAvailableSentences(String userId) async {
    try {
      debugPrint('جلب جميع الجمل المتاحة من Firebase للمستخدم $userId');

      // الحصول على الجمل المقروءة من Firestore
      final readSentencesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('readSentences')
          .get();

      // إنشاء مجموعة من معرفات الجمل المقروءة
      final readSentenceIds =
          readSentencesSnapshot.docs.map((doc) => doc.id).toSet();

      // إضافة الجمل المقروءة محليًا
      readSentenceIds.addAll(_readSentencesBox.keys.cast<String>());

      // الحصول على الجمل المفضلة
      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .get();
      final favoriteSentenceIds =
          favoritesSnapshot.docs.map((doc) => doc.id).toSet();

      // الحصول على جميع الجمل من Firestore
      final allSentencesSnapshot =
          await _firestore.collection(_sentencesCollection).get();

      debugPrint(
          'تم العثور على ${allSentencesSnapshot.docs.length} جملة في Firestore');

      // تحويل جميع الجمل إلى نماذج Hive وتخزينها
      int storedCount = 0;

      for (final doc in allSentencesSnapshot.docs) {
        // تحقق مما إذا كانت الجملة موجودة بالفعل في Hive
        if (_sentencesBox.containsKey(doc.id)) {
          continue; // تخطي الجمل الموجودة بالفعل
        }

        final data = doc.data();
        final isFavorite = favoriteSentenceIds.contains(doc.id);
        final isRead = readSentenceIds.contains(doc.id);

        // تحويل البيانات إلى نموذج Hive
        final hiveSentence = HiveSentenceModel(
          id: doc.id,
          arabicText: data['arabicText'] ?? '',
          englishText: data['englishText'] ?? '',
          category: data['category'] ?? '',
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          audioUrl: data['audioUrl'],
          difficulty: data['difficulty'] ?? 'medium',
          isReadByCurrentUser: isRead,
          isFavoriteByCurrentUser: isFavorite,
          lastModified: DateTime.now(),
        );

        // تخزين الجملة في Hive
        await _sentencesBox.put(doc.id, hiveSentence);
        storedCount++;

        // طباعة تقدم التخزين كل 50 جملة
        if (storedCount % 50 == 0) {
          debugPrint('تم تخزين $storedCount جملة حتى الآن...');
        }
      }

      debugPrint('تم تخزين $storedCount جملة جديدة في Hive');
      return storedCount;
    } catch (e) {
      debugPrint('خطأ في جلب جميع الجمل المتاحة من Firebase: $e');
      return 0;
    }
  }

  /// الحصول على جمل جديدة للقراءة اليومية
  Future<List<HiveSentenceModel>> getNewDailySentences(int count,
      {String? userId}) async {
    try {
      // مسح الجمل اليومية الحالية أولاً
      await clearDailySentences();

      // الحصول على جميع الجمل
      final allSentences = _sentencesBox.values.toList();

      // إذا كانت قاعدة البيانات المحلية فارغة وتم توفير معرف المستخدم، نحاول جلب الجمل من Firebase
      if (allSentences.isEmpty && userId != null) {
        debugPrint('قاعدة البيانات المحلية فارغة، جلب الجمل من Firebase');
        final firebaseSentences =
            await fetchSentencesFromFirebase(userId, count);

        if (firebaseSentences.isNotEmpty) {
          // تخزين معرفات الجمل المختارة كجمل يومية
          final selectedIds = firebaseSentences.map((s) => s.id).toList();
          await setDailySentences(selectedIds);

          // زيادة عداد الجمل المعروضة اليوم
          await incrementTodayShownCount(firebaseSentences.length);

          debugPrint(
              'تم اختيار ${firebaseSentences.length} جملة جديدة من Firebase للقراءة اليومية');
          return firebaseSentences;
        } else {
          debugPrint('لا توجد جمل غير مقروءة في Firebase');
          return [];
        }
      }

      // إذا كانت قاعدة البيانات المحلية فارغة ولم يتم توفير معرف المستخدم
      if (allSentences.isEmpty) {
        debugPrint('قاعدة البيانات المحلية فارغة ولم يتم توفير معرف المستخدم');
        return [];
      }

      // الحصول على معرفات الجمل المقروءة
      final readSentenceIds = _readSentencesBox.keys.cast<String>().toList();

      // تصفية الجمل غير المقروءة
      var unreadSentences = allSentences
          .where((sentence) => !readSentenceIds.contains(sentence.id))
          .toList();

      debugPrint(
          'تم العثور على ${unreadSentences.length} جملة غير مقروءة من أصل ${allSentences.length}');

      // إذا كان عدد الجمل غير المقروءة أقل من العدد المطلوب وتم توفير معرف المستخدم
      // نحاول جلب المزيد من الجمل من Firebase
      if (unreadSentences.length < count && userId != null) {
        debugPrint(
            'عدد الجمل غير المقروءة أقل من العدد المطلوب، جلب المزيد من Firebase');
        final firebaseSentences = await fetchSentencesFromFirebase(
            userId, count * 2); // جلب ضعف العدد المطلوب للتأكد

        if (firebaseSentences.isNotEmpty) {
          // إضافة الجمل الجديدة إلى قائمة الجمل غير المقروءة
          unreadSentences.addAll(firebaseSentences);
          debugPrint(
              'تم إضافة ${firebaseSentences.length} جملة جديدة من Firebase');
        }
      }

      // إذا لم تكن هناك جمل غير مقروءة بعد كل المحاولات
      if (unreadSentences.isEmpty) {
        debugPrint(
            'لا توجد جمل غير مقروءة، إعادة تعيين حالة القراءة للجمل القديمة');

        // إذا كانت جميع الجمل مقروءة، نعيد تعيين حالة القراءة لجميع الجمل
        await resetAllSentencesReadStatus();

        // بعد إعادة التعيين، جميع الجمل تعتبر غير مقروءة
        unreadSentences = allSentences;

        // إضافة سجل لتتبع الجمل التي تم إعادة تعيينها
        await _userBox.put(
            'reset_sentences_history', DateTime.now().toIso8601String());
        debugPrint('تم تسجيل إعادة تعيين الجمل في التاريخ');
      }

      // خلط الجمل غير المقروءة
      unreadSentences.shuffle(Random());

      // اختيار العدد المطلوب من الجمل
      final selectedSentences = unreadSentences.take(count).toList();

      // طباعة معرفات الجمل المختارة للتتبع
      debugPrint(
          'معرفات الجمل المختارة: ${selectedSentences.map((s) => s.id).join(', ')}');

      // تخزين معرفات الجمل المختارة كجمل يومية
      final selectedIds = selectedSentences.map((s) => s.id).toList();
      await setDailySentences(selectedIds);

      // زيادة عداد الجمل المعروضة اليوم
      await incrementTodayShownCount(selectedSentences.length);

      debugPrint(
          'تم اختيار ${selectedSentences.length} جملة جديدة للقراءة اليومية');
      return selectedSentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل جديدة للقراءة اليومية: $e');
      return [];
    }
  }

  /// الحصول على جمل جديدة للقراءة اليومية بشكل قسري
  /// يتجاهل الجمل الحالية ويجلب جمل جديدة
  Future<List<HiveSentenceModel>> forceGetNewDailySentences(int count,
      {String? userId}) async {
    try {
      debugPrint('جلب جمل جديدة بشكل قسري');

      // مسح الجمل اليومية الحالية أولاً
      await clearDailySentences();

      // الحصول على جميع الجمل
      final allSentences = _sentencesBox.values.toList();

      // الحصول على معرفات الجمل المقروءة
      final readSentenceIds = _readSentencesBox.keys.cast<String>().toList();

      // الحصول على معرفات الجمل اليومية الحالية (التي نريد تجنبها)
      final currentDailyIds = _getDailySentenceIds();

      // تصفية الجمل غير المقروءة وغير الموجودة في الجمل اليومية الحالية
      var availableSentences = allSentences
          .where((sentence) =>
              !readSentenceIds.contains(sentence.id) &&
              !currentDailyIds.contains(sentence.id))
          .toList();

      debugPrint(
          'تم العثور على ${availableSentences.length} جملة متاحة من أصل ${allSentences.length}');

      // إذا كان عدد الجمل المتاحة أقل من العدد المطلوب وتم توفير معرف المستخدم
      // نحاول جلب المزيد من الجمل من Firebase
      if (availableSentences.length < count && userId != null) {
        debugPrint(
            'عدد الجمل المتاحة أقل من العدد المطلوب، جلب المزيد من Firebase');
        final firebaseSentences = await fetchSentencesFromFirebase(
            userId, count * 2); // جلب ضعف العدد المطلوب للتأكد

        if (firebaseSentences.isNotEmpty) {
          // إضافة الجمل الجديدة إلى قائمة الجمل المتاحة
          availableSentences.addAll(firebaseSentences);
          debugPrint(
              'تم إضافة ${firebaseSentences.length} جملة جديدة من Firebase');
        }
      }

      // إذا لم تكن هناك جمل متاحة بعد كل المحاولات
      if (availableSentences.isEmpty) {
        debugPrint('لا توجد جمل متاحة، استخدام جميع الجمل');

        // استخدام جميع الجمل
        availableSentences = allSentences;
      }

      // خلط الجمل المتاحة
      availableSentences.shuffle(Random());

      // اختيار العدد المطلوب من الجمل
      final selectedSentences = availableSentences.take(count).toList();

      // طباعة معرفات الجمل المختارة للتتبع
      debugPrint(
          'معرفات الجمل المختارة بشكل قسري: ${selectedSentences.map((s) => s.id).join(', ')}');

      // تخزين معرفات الجمل المختارة كجمل يومية
      final selectedIds = selectedSentences.map((s) => s.id).toList();
      await setDailySentences(selectedIds);

      // زيادة عداد الجمل المعروضة اليوم
      await incrementTodayShownCount(selectedSentences.length);

      debugPrint(
          'تم اختيار ${selectedSentences.length} جملة جديدة للقراءة اليومية بشكل قسري');
      return selectedSentences;
    } catch (e) {
      debugPrint('خطأ في الحصول على جمل جديدة للقراءة اليومية بشكل قسري: $e');
      return [];
    }
  }

  /// إعادة تعيين حالة القراءة لجميع الجمل
  Future<void> resetAllSentencesReadStatus() async {
    try {
      // مسح صندوق الجمل المقروءة
      await _readSentencesBox.clear();
      await _readSentencesBox.flush(); // ضمان حفظ التغييرات على القرص

      // إعادة تعيين حالة القراءة لجميع الجمل
      for (var sentence in _sentencesBox.values) {
        if (sentence.isReadByCurrentUser) {
          sentence.isReadByCurrentUser = false;
          await sentence.save();
        }
      }

      await _sentencesBox.flush(); // ضمان حفظ التغييرات على القرص

      debugPrint('تم إعادة تعيين حالة القراءة لجميع الجمل');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين حالة القراءة لجميع الجمل: $e');
    }
  }

  /// التحقق مما إذا كانت جميع الجمل اليومية مقروءة
  bool areAllDailySentencesRead() {
    try {
      final dailySentences = getDailySentences(onlyUnread: true);
      return dailySentences.isEmpty;
    } catch (e) {
      debugPrint('خطأ في التحقق مما إذا كانت جميع الجمل اليومية مقروءة: $e');
      return false;
    }
  }

  /// التحقق مما إذا كان اليوم جديدًا
  bool isNewDay() {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final lastDate = _userBox.get(_todayDateKey);

      if (lastDate == null) {
        // إذا لم يكن هناك تاريخ سابق، فهذا يوم جديد
        _userBox.put(_todayDateKey, today.toIso8601String());
        _resetDailyCounters(); // إعادة تعيين العدادات اليومية
        return true;
      }

      final lastDateTime = DateTime.parse(lastDate);
      final lastDay =
          DateTime(lastDateTime.year, lastDateTime.month, lastDateTime.day);

      // التحقق مما إذا كان اليوم الحالي مختلفًا عن آخر يوم
      final isNew = today.isAfter(lastDay);

      if (isNew) {
        // تحديث تاريخ اليوم الحالي
        _userBox.put(_todayDateKey, today.toIso8601String());
        _resetDailyCounters(); // إعادة تعيين العدادات اليومية
      }

      return isNew;
    } catch (e) {
      debugPrint('خطأ في التحقق مما إذا كان اليوم جديدًا: $e');
      return false;
    }
  }

  /// إعادة تعيين العدادات اليومية
  void _resetDailyCounters() {
    try {
      _userBox.put(_todayShownCountKey, 0);
      _userBox.put(_todayReadCountKey, 0);
      debugPrint('تم إعادة تعيين العدادات اليومية');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين العدادات اليومية: $e');
    }
  }

  /// زيادة عداد الجمل المعروضة اليوم
  Future<void> incrementTodayShownCount(int count) async {
    try {
      final currentCount = _userBox.get(_todayShownCountKey, defaultValue: 0);
      await _userBox.put(_todayShownCountKey, currentCount + count);
      debugPrint(
          'تم زيادة عداد الجمل المعروضة اليوم بمقدار $count ليصبح ${currentCount + count}');
    } catch (e) {
      debugPrint('خطأ في زيادة عداد الجمل المعروضة اليوم: $e');
    }
  }

  /// زيادة عداد الجمل المقروءة اليوم
  Future<void> incrementTodayReadCount() async {
    try {
      final currentCount = _userBox.get(_todayReadCountKey, defaultValue: 0);
      await _userBox.put(_todayReadCountKey, currentCount + 1);
      debugPrint(
          'تم زيادة عداد الجمل المقروءة اليوم ليصبح ${currentCount + 1}');
    } catch (e) {
      debugPrint('خطأ في زيادة عداد الجمل المقروءة اليوم: $e');
    }
  }

  /// الحصول على عدد الجمل المعروضة اليوم
  int getTodayShownCount() {
    try {
      return _userBox.get(_todayShownCountKey, defaultValue: 0);
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الجمل المعروضة اليوم: $e');
      return 0;
    }
  }

  /// الحصول على عدد الجمل المقروءة اليوم
  int getTodayReadCount() {
    try {
      return _userBox.get(_todayReadCountKey, defaultValue: 0);
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الجمل المقروءة اليوم: $e');
      return 0;
    }
  }
}
