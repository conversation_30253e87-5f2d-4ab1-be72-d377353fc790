import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import '../viewmodels/auth_view_model.dart';
import '../viewmodels/settings_view_model.dart';
import '../viewmodels/sentence_view_model.dart';
import '../viewmodels/hive_sentence_view_model.dart';
import '../providers/points_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/app_card.dart';
import 'login_screen.dart';
import 'edit_profile_screen.dart';
import 'notification_settings_screen.dart';
import 'sync_settings_screen.dart';
import 'initialize_data_screen.dart';
import 'admin/admin_dashboard_screen.dart';
// No podemos importar profile_details_screen.dart aquí porque causaría una importación circular

class ProfileDetailsScreen extends StatelessWidget {
  const ProfileDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: const [
          ProfileCard(),
          SizedBox(height: 16),
          SettingsSection(),
        ],
      ),
    );
  }
}

class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key});

  Widget _buildUserAvatar(String? photoURL, double size, double iconSize) {
    if (photoURL == null) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.grey[200],
        ),
        child: Icon(Icons.person, size: iconSize, color: Colors.grey),
      );
    }

    if (photoURL.startsWith('assets/')) {
      // استخدام AssetImage بشكل صحيح بدون إضافة file:///
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: AssetImage(photoURL),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else if (photoURL.startsWith('file:///assets/')) {
      // إصلاح مسار الملف الخاطئ
      final correctedPath = photoURL.replaceFirst('file:///', '');
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(
            image: AssetImage(correctedPath),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      // استخدام NetworkImage للروابط
      try {
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: NetworkImage(photoURL),
              fit: BoxFit.cover,
              onError: (exception, stackTrace) {
                debugPrint('Error loading profile image: $exception');
              },
            ),
          ),
        );
      } catch (e) {
        debugPrint('Error creating NetworkImage: $e');
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[200],
          ),
          child: Icon(Icons.person, size: iconSize, color: Colors.grey),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authVM = context.watch<AuthViewModel>();
    final user = authVM.user;
    final userModel = authVM.userModel;

    // استخدام Provider للحصول على SentenceViewModel
    final sentenceVM = Provider.of<SentenceViewModel>(context, listen: false);

    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final profileImageSize =
        isVerySmallScreen ? 60.0 : (isSmallScreen ? 70.0 : 80.0);
    final profileIconSize =
        isVerySmallScreen ? 30.0 : (isSmallScreen ? 35.0 : 40.0);
    final editIconSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0);
    final editPadding = isVerySmallScreen ? 3.0 : (isSmallScreen ? 3.5 : 4.0);
    final nameFontSize =
        isVerySmallScreen ? 16.0 : (isSmallScreen ? 18.0 : 20.0);
    final emailFontSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0);
    final verticalSpacing1 =
        isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0);
    final verticalSpacing2 =
        isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);
    final verticalSpacing3 =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 18.0 : 24.0);

    return AppCard(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EditProfileScreen(),
          ),
        );
      },
      padding: EdgeInsets.all(
          isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              // صورة المستخدم
              _buildUserAvatar(userModel?.photoURL ?? user?.photoURL,
                  profileImageSize, profileIconSize),
              // أيقونة القلم للتعديل
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  padding: EdgeInsets.all(editPadding),
                  decoration: const BoxDecoration(
                    color: AppTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.edit,
                    size: editIconSize,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: verticalSpacing1),
          // اسم المستخدم
          Text(
            user?.displayName ?? 'المستخدم',
            style: TextStyle(
              fontSize: nameFontSize,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: verticalSpacing2),
          // البريد الإلكتروني
          Text(
            user?.email ?? '<EMAIL>',
            style: TextStyle(
              fontSize: emailFontSize,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: verticalSpacing3),
          // إحصائيات القراءة
          FutureBuilder<Map<String, dynamic>>(
            future:
                sentenceVM.getSentenceService().getUserReadingStats(user!.uid),
            builder: (context, snapshot) {
              // عرض shimmer أثناء تحميل البيانات
              if (!snapshot.hasData) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStat('إجمالي التحدي', '', isSmallScreen,
                        isVerySmallScreen, true),
                    _buildStat('تمت القراءة', '', isSmallScreen,
                        isVerySmallScreen, true),
                    _buildStat('نسبة الإنجاز', '', isSmallScreen,
                        isVerySmallScreen, true),
                  ],
                );
              }

              // استخراج البيانات من نتيجة الاستعلام
              final stats = snapshot.data!;
              final readCount = stats['readCount'] ?? 0;
              final totalSentences = stats['totalSentences'] ?? 0;
              final progressPercentage = stats['progressPercentage'] ?? '0';

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStat('إجمالي التحدي', totalSentences.toString(),
                      isSmallScreen, isVerySmallScreen),
                  _buildStat('تمت القراءة', readCount.toString(), isSmallScreen,
                      isVerySmallScreen),
                  _buildStat('نسبة الإنجاز', '$progressPercentage%',
                      isSmallScreen, isVerySmallScreen),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStat(String label, String value,
      [bool isSmallScreen = false,
      bool isVerySmallScreen = false,
      bool isLoading = false]) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        isLoading
            ? Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width:
                      isVerySmallScreen ? 40.0 : (isSmallScreen ? 50.0 : 60.0),
                  height:
                      isVerySmallScreen ? 24.0 : (isSmallScreen ? 28.0 : 32.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                ),
              )
            : Text(
                value,
                style: TextStyle(
                  fontSize:
                      isVerySmallScreen ? 18.0 : (isSmallScreen ? 20.0 : 24.0),
                  fontWeight: FontWeight.bold,
                ),
              ),
        Text(
          label,
          style: TextStyle(
            fontSize: isVerySmallScreen ? 10.0 : (isSmallScreen ? 12.0 : 14.0),
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}

class SettingsSection extends StatelessWidget {
  const SettingsSection({super.key});

  // عرض مربع حوار لتأكيد مسح بيانات التعلم
  void _showClearLearningDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح نتائج التعلم'),
        content: const Text(
          'هذا الإجراء سيؤدي إلى مسح نتائج التعلم الخاصة بك مثل الجمل المقروءة والمفضلة والنقاط.\n\n'
          'لن يتم مسح الجمل نفسها أو هيكل المستويات، فقط سجل تقدمك ونتائجك.\n\n'
          'سيتم مسح البيانات المحلية بشكل كامل، وسيتم محاولة مسح البيانات من السيرفر أيضًا.\n\n'
          'هل أنت متأكد من الاستمرار؟',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _clearLearningData(context);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('إعادة ضبط'),
          ),
        ],
      ),
    );
  }

  // مسح بيانات التعلم
  Future<void> _clearLearningData(BuildContext context) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // عرض مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري مسح البيانات...'),
          duration: Duration(seconds: 2),
        ),
      );

      // مسح البيانات المحلية أولاً (هذا سيعمل دائمًا)
      debugPrint('بدء مسح البيانات المحلية...');

      // 1. مسح البيانات المحلية (Hive)
      try {
        // أولاً، نحاول فتح الصناديق ومسح محتوياتها (أكثر أمانًا من حذف الملفات مباشرة)
        final boxNames = [
          'sentences',
          'dailySentences',
          'readSentences',
          'favorites',
          'syncQueue',
          'dateBox',
          'pointsBox',
          'userProgress'
        ];

        for (final boxName in boxNames) {
          try {
            final box = await Hive.openBox(boxName);
            await box.clear();
            await box.close();
            debugPrint('تم مسح محتويات صندوق $boxName بنجاح');
          } catch (boxError) {
            debugPrint('خطأ في مسح صندوق $boxName: $boxError');
          }
        }

        // ثم نحاول حذف الملفات من القرص
        for (final boxName in boxNames) {
          try {
            await Hive.deleteBoxFromDisk(boxName);
            debugPrint('تم حذف ملف صندوق $boxName من القرص');
          } catch (deleteError) {
            debugPrint('خطأ في حذف ملف صندوق $boxName: $deleteError');
          }
        }

        debugPrint('تم مسح صناديق Hive بنجاح');
      } catch (e) {
        debugPrint('خطأ في مسح صناديق Hive: $e');
      }

      // 2. محاولة مسح البيانات من Firebase (قد تفشل بسبب الصلاحيات)
      debugPrint('محاولة مسح البيانات من Firebase...');
      bool firebaseSuccess = true;

      // قائمة بالمجموعات الفرعية التي نريد مسحها (فقط البيانات الخاصة بالمستخدم وليس الجمل أو المستويات)
      final subCollections = [
        'readSentences',
        'favorites',
        'conversations',
      ];

      // مسح المجموعات الفرعية
      for (final collection in subCollections) {
        try {
          final snapshot = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .collection(collection)
              .limit(100)
              .get();

          final batch = FirebaseFirestore.instance.batch();
          int count = 0;

          for (var doc in snapshot.docs) {
            batch.delete(doc.reference);
            count++;
          }

          if (count > 0) {
            await batch.commit();
            debugPrint('تم مسح $count وثيقة من مجموعة $collection');
          } else {
            debugPrint('لم يتم العثور على وثائق في مجموعة $collection');
          }
        } catch (e) {
          debugPrint('فشل مسح مجموعة $collection: $e');
          firebaseSuccess = false;
        }
      }

      // مسح سجل النقاط
      try {
        final recordsSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('history')
            .collection('records')
            .limit(100)
            .get();

        if (recordsSnapshot.docs.isNotEmpty) {
          final batch = FirebaseFirestore.instance.batch();
          for (var doc in recordsSnapshot.docs) {
            batch.delete(doc.reference);
          }
          await batch.commit();
          debugPrint(
              'تم مسح ${recordsSnapshot.docs.length} سجل من سجلات النقاط');
        }

        // إعادة تعيين ملخص النقاط
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('points')
            .doc('summary')
            .set({
          'educational': 0,
          'reward': 0,
        });
        debugPrint('تم إعادة تعيين النقاط في Firebase');
      } catch (e) {
        debugPrint('فشل مسح سجلات النقاط: $e');
        firebaseSuccess = false;
      }

      // محاولة مسح تقدم المستخدم في المستويات
      try {
        final progressSnapshot = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .collection('progress')
            .get();

        if (progressSnapshot.docs.isNotEmpty) {
          final batch = FirebaseFirestore.instance.batch();
          for (var doc in progressSnapshot.docs) {
            batch.delete(doc.reference);
          }
          await batch.commit();
          debugPrint(
              'تم مسح ${progressSnapshot.docs.length} سجل من سجلات التقدم');
        }
      } catch (e) {
        debugPrint('فشل مسح سجلات التقدم: $e');
        firebaseSuccess = false;
      }

      // 3. إعادة تحميل البيانات
      if (context.mounted) {
        try {
          // إعادة تحميل البيانات من SentenceViewModel
          final sentenceVM =
              Provider.of<SentenceViewModel>(context, listen: false);
          await sentenceVM.loadSentences(forceRefresh: true);
          debugPrint('تم إعادة تحميل بيانات SentenceViewModel');
        } catch (e) {
          debugPrint('خطأ في إعادة تحميل بيانات SentenceViewModel: $e');
        }
      }

      // إعادة تحميل البيانات من HiveSentenceViewModel
      if (context.mounted) {
        try {
          final hiveSentenceVM =
              Provider.of<HiveSentenceViewModel>(context, listen: false);
          await hiveSentenceVM.loadDailySentences(forceRefresh: true);
          debugPrint('تم إعادة تحميل بيانات HiveSentenceViewModel');
        } catch (e) {
          debugPrint('خطأ في إعادة تحميل بيانات HiveSentenceViewModel: $e');
        }
      }

      // إعادة تحميل بيانات النقاط
      if (context.mounted) {
        try {
          final pointsProvider =
              Provider.of<PointsProvider>(context, listen: false);
          await pointsProvider.fetchPoints();
          debugPrint('تم إعادة تحميل بيانات النقاط');
        } catch (e) {
          debugPrint('خطأ في إعادة تحميل بيانات النقاط: $e');
        }
      }

      // إظهار رسالة نجاح
      if (context.mounted) {
        if (firebaseSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم مسح بيانات التعلم بنجاح'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'تم مسح البيانات المحلية فقط. فشل مسح بعض البيانات من Firebase بسبب نقص الصلاحيات'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }

      // انتظار لحظة قبل إعادة تحميل الشاشة
      await Future.delayed(const Duration(milliseconds: 500));

      // لا يمكن استخدام setState() لأن هذه دالة ساكنة في StatelessWidget
      // بدلاً من ذلك، سنستخدم Navigator للعودة ثم إعادة الدخول للشاشة
      if (context.mounted) {
        try {
          // إعادة تحميل الشاشة الحالية عن طريق العودة ثم إعادة الدخول
          Navigator.of(context).pop();
          Navigator.of(context).pushNamed('/profile');
          debugPrint('تم إعادة تحميل الشاشة الحالية');
        } catch (e) {
          debugPrint('خطأ في إعادة تحميل الشاشة: $e');
        }
      }
    } catch (e) {
      debugPrint('خطأ في مسح بيانات التعلم: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء مسح البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsVM = context.watch<SettingsViewModel>();
    final hiveSentenceVM = context.watch<HiveSentenceViewModel>();

    // تحديد ما إذا كانت الشاشة صغيرة
    final isSmallScreen = MediaQuery.of(context).size.height < 600;
    // تحديد ما إذا كانت الشاشة صغيرة جدًا
    final isVerySmallScreen = MediaQuery.of(context).size.height < 500;

    // تحديد الأحجام بناءً على حجم الشاشة
    final titleFontSize =
        isVerySmallScreen ? 14.0 : (isSmallScreen ? 16.0 : 18.0);
    final contentPadding =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 14.0 : 16.0);
    final iconSize = isVerySmallScreen ? 18.0 : (isSmallScreen ? 20.0 : 24.0);
    final tilePadding = isVerySmallScreen
        ? const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0)
        : (isSmallScreen
            ? const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0)
            : null);

    return AppCard(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.all(contentPadding),
            child: Text(
              'الإعدادات',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ListTile(
            leading: Icon(Icons.brightness_6, size: iconSize),
            title: const Text('الوضع الليلي'),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            trailing: Switch(
              value: settingsVM.isDarkMode,
              onChanged: (value) {
                // تعيين الوضع المظلم أو العادي حسب اختيار المستخدم فقط
                settingsVM.setThemeMode(
                  value ? ThemeMode.dark : ThemeMode.light,
                );
              },
            ),
          ),
          ListTile(
            leading: Icon(Icons.language, size: iconSize),
            title: const Text('اللغة'),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            trailing: const Text('العربية'),
          ),
          ListTile(
            leading: Icon(Icons.notifications, size: iconSize),
            title: const Text('الإشعارات'),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsScreen(),
                ),
              );
            },
          ),
          // إحصائيات القراءة
          ListTile(
            leading: Icon(Icons.bar_chart, size: iconSize),
            title: const Text('الإحصائيات'),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/calendar-stats');
            },
          ),

          // لوحة تحكم المسؤول (للمسؤولين فقط)
          Consumer<AuthViewModel>(
            builder: (context, authVM, _) {
              if (authVM.isAdmin) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(Icons.admin_panel_settings, size: iconSize),
                      title: const Text('لوحة تحكم المسؤول'),
                      contentPadding: tilePadding,
                      dense: isVerySmallScreen,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AdminDashboardScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading:
                          Icon(Icons.settings_applications, size: iconSize),
                      title: const Text('تهيئة البيانات'),
                      contentPadding: tilePadding,
                      dense: isVerySmallScreen,
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const InitializeDataScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // إعدادات المزامنة
          ListTile(
            leading: Icon(
              Icons.sync,
              size: iconSize,
              color: hiveSentenceVM.isSyncing ? AppTheme.primaryColor : null,
            ),
            title: const Text('إعدادات المزامنة'),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (hiveSentenceVM.isSyncing)
                  Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.only(left: 8),
                    decoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                if (hiveSentenceVM.getPendingSyncItemsCount() > 0)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${hiveSentenceVM.getPendingSyncItemsCount()}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const Icon(Icons.arrow_forward_ios, size: 16),
              ],
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SyncSettingsScreen(),
                ),
              );
            },
          ),

          // زر مؤقت لمسح بيانات التعلم (للتجريب فقط)
          ListTile(
            leading: Icon(
              Icons.delete_forever,
              size: iconSize,
              color: Colors.orange,
            ),
            title: const Text(
              'إعادة ضبط نتائج التعلم',
              style: TextStyle(color: Colors.orange),
            ),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            onTap: () {
              _showClearLearningDataDialog(context);
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: Icon(Icons.logout, color: Colors.red, size: iconSize),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.red),
            ),
            contentPadding: tilePadding,
            dense: isVerySmallScreen,
            onTap: () async {
              final authVM = context.read<AuthViewModel>();
              await authVM.signOut();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const LoginScreen()),
                  (route) => false,
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
