import 'package:flutter/material.dart';
import 'package:google_mlkit_smart_reply/google_mlkit_smart_reply.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Smart Reply Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const MyHomePage(title: 'Smart Reply Test'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final SmartReply _smartReply = SmartReply();
  List<String> _suggestions = [];
  final TextEditingController _messageController = TextEditingController();

  @override
  void dispose() {
    _smartReply.close();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _getSuggestions() async {
    if (_messageController.text.isEmpty) return;

    // Add a message to the conversation
    _smartReply.addMessageToConversationFromRemoteUser(
      _messageController.text,
      DateTime.now().millisecondsSinceEpoch,
      'user1',
    );

    // Generate replies
    final response = await _smartReply.suggestReplies();
    
    setState(() {
      _suggestions = response.suggestions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Enter a message',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _getSuggestions,
              child: const Text('Get Suggestions'),
            ),
            const SizedBox(height: 16),
            const Text('Suggested Replies:'),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(_suggestions[index]),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
