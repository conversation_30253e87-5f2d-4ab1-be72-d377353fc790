import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import '../viewmodels/auth_view_model.dart';
import '../theme/app_theme.dart';
import 'home_screen.dart'; // استخدام الشاشة الرئيسية الأصلية
import 'sentences_screen.dart';
import 'read_sentences_screen.dart';
import 'login_screen.dart';
import 'conversations_screen.dart';
import 'timeline_screen.dart';
import 'calendar_stats_screen.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  late PersistentTabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PersistentTabController(initialIndex: 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  List<Widget> _buildScreens() {
    return [
      const HomeScreen(), // استخدام الشاشة الرئيسية الأصلية
      const SentencesScreen(),
      const ReadSentencesScreen(),
      const ConversationsScreen(), // إضافة شاشة المحادثات
      const TimelineScreen(), // إضافة شاشة مسار التعلم
      const CalendarStatsScreen(), // إضافة شاشة الإحصائيات بدلاً من الملف الشخصي
    ];
  }

  List<PersistentBottomNavBarItem> _navBarsItems(BuildContext context) {
    return [
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.home),
        title: 'الرئيسية',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.menu_book),
        title: 'الجمل',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.check_circle),
        title: 'المقروءة',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.chat_bubble_outline),
        title: 'المحادثات',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.timeline),
        title: 'مسار التعلم',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.bar_chart),
        title: 'الإحصائيات',
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: AppTheme.textSecondary,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authVM, _) {
        // التحقق من حالة المصادقة
        if (!authVM.isAuthenticated) {
          // إذا لم يكن المستخدم مسجل الدخول، توجيهه إلى شاشة تسجيل الدخول
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (_) => const LoginScreen()),
            );
          });
          // عرض شاشة تحميل بسيطة أثناء التوجيه
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // إذا كان المستخدم مسجل الدخول، عرض الصفحة الرئيسية مع شريط التنقل الثابت
        return PersistentTabView(
          context,
          controller: _controller,
          screens: _buildScreens(),
          items: _navBarsItems(context),
          confineToSafeArea: true,
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppTheme.backgroundDark
              : Colors.white,
          handleAndroidBackButtonPress: true,
          resizeToAvoidBottomInset: true,
          stateManagement: true,
          decoration: NavBarDecoration(
            borderRadius: BorderRadius.circular(10.0),
            colorBehindNavBar: Theme.of(context).brightness == Brightness.dark
                ? AppTheme.backgroundDark
                : Colors.white,
            boxShadow: AppTheme.cardShadow,
          ),
          navBarStyle: NavBarStyle.style1, // استخدام النمط الأول كما هو مطلوب
        );
      },
    );
  }
}
