import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';

class UserService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final String _collection = 'users';

  Future<void> updateUserData(UserModel user) async {
    try {
      await _firestore.collection(_collection).doc(user.id).set(
            user.toMap(),
            SetOptions(merge: true),
          );

      final currentUser = _auth.currentUser;
      if (currentUser != null && user.displayName != currentUser.displayName) {
        await currentUser.updateDisplayName(user.displayName);
      }
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: $e');
    }
  }

  Future<UserModel?> getUserData(String userId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(userId).get();
      final authUser = _auth.currentUser;

      if (!doc.exists) {
        if (authUser != null) {
          final newUser = UserModel(
            id: authUser.uid,
            email: authUser.email,
            displayName: authUser.displayName,
          );
          await updateUserData(newUser);
          return newUser;
        }
        return null;
      }

      final data = Map<String, dynamic>.from(doc.data() ?? {});
      data['id'] = userId;

      // تحديث البيانات من Firebase Auth إذا كانت موجودة
      if (authUser != null) {
        data['email'] = authUser.email ?? data['email'];
        data['displayName'] = authUser.displayName ?? data['displayName'];
      }

      return UserModel.fromMap(data);
    } catch (e) {
      throw Exception('فشل في جلب بيانات المستخدم: $e');
    }
  }

  Future<void> updateUserField(
      String userId, String field, dynamic value) async {
    try {
      await _firestore.collection(_collection).doc(userId).update({
        field: value,
      });
    } catch (e) {
      throw Exception('فشل في تحديث حقل $field: $e');
    }
  }
}
