import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/conversation_model.dart';
import '../services/conversation_service.dart';
import '../theme/app_theme.dart';
import 'conversation_detail_screen.dart';
import '../data/sample_conversation.dart';

class ConversationsScreen extends StatefulWidget {
  const ConversationsScreen({Key? key}) : super(key: key);

  @override
  State<ConversationsScreen> createState() => _ConversationsScreenState();
}

class _ConversationsScreenState extends State<ConversationsScreen> {
  String _selectedCategory = 'all';
  String _selectedDifficulty = 'all';
  String _selectedLevel = 'all';
  bool _showOnlyRead = false;
  bool _isSyncing = false;

  @override
  Widget build(BuildContext context) {
    final conversationService = Provider.of<ConversationService>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات'),
        actions: [
          // Botón para sincronizar conversaciones
          IconButton(
            icon: const Icon(Icons.sync),
            tooltip: 'مزامنة المحادثات',
            onPressed: _syncConversations,
          ),
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: 'المحادثات المقروءة',
            onPressed: () {
              setState(() {
                _showOnlyRead = !_showOnlyRead;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtros activos
          if (_selectedCategory != 'all' ||
              _selectedDifficulty != 'all' ||
              _selectedLevel != 'all' ||
              _showOnlyRead)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.grey.shade200,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    const Text('التصفية النشطة: ',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    if (_selectedCategory != 'all')
                      Chip(
                        label: Text(_selectedCategory),
                        onDeleted: () {
                          setState(() {
                            _selectedCategory = 'all';
                          });
                        },
                      ),
                    const SizedBox(width: 8),
                    if (_selectedDifficulty != 'all')
                      Chip(
                        label: Text(_getDifficultyLabel(_selectedDifficulty)),
                        onDeleted: () {
                          setState(() {
                            _selectedDifficulty = 'all';
                          });
                        },
                      ),
                    const SizedBox(width: 8),
                    if (_selectedLevel != 'all')
                      Chip(
                        label: Text('المستوى $_selectedLevel'),
                        onDeleted: () {
                          setState(() {
                            _selectedLevel = 'all';
                          });
                        },
                      ),
                    const SizedBox(width: 8),
                    if (_showOnlyRead)
                      Chip(
                        label: const Text('المقروءة فقط'),
                        onDeleted: () {
                          setState(() {
                            _showOnlyRead = false;
                          });
                        },
                      ),
                  ],
                ),
              ),
            ),

          // Lista de conversaciones
          Expanded(
            child: _buildConversationsList(conversationService),
          ),
        ],
      ),
      floatingActionButton: _buildAddButton(),
    );
  }

  Widget _buildConversationsList(ConversationService conversationService) {
    // Determinar qué stream usar según los filtros
    Stream<List<ConversationModel>> conversationsStream;

    if (_showOnlyRead) {
      conversationsStream = conversationService.getReadConversations();
    } else if (_selectedLevel != 'all') {
      // Filtrar por nivel
      conversationsStream =
          conversationService.getConversationsByLevel(_selectedLevel);
    } else if (_selectedCategory != 'all' && _selectedDifficulty != 'all') {
      // Filtrar por categoría y dificultad (esto requeriría una consulta compuesta)
      conversationsStream = conversationService.getConversations();
    } else if (_selectedCategory != 'all') {
      conversationsStream =
          conversationService.getConversationsByCategory(_selectedCategory);
    } else if (_selectedDifficulty != 'all') {
      conversationsStream =
          conversationService.getConversationsByDifficulty(_selectedDifficulty);
    } else {
      conversationsStream = conversationService.getConversations();
    }

    return StreamBuilder<List<ConversationModel>>(
      stream: conversationsStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('لا توجد محادثات متاحة'));
        }

        List<ConversationModel> conversations = snapshot.data!;

        // Aplicar filtros adicionales si es necesario
        if (_selectedCategory != 'all' &&
            _selectedDifficulty != 'all' &&
            _selectedLevel != 'all') {
          conversations = conversations
              .where((conv) =>
                  conv.category == _selectedCategory &&
                  conv.difficulty == _selectedDifficulty &&
                  conv.level == _selectedLevel)
              .toList();
        } else if (_selectedCategory != 'all' && _selectedDifficulty != 'all') {
          conversations = conversations
              .where((conv) =>
                  conv.category == _selectedCategory &&
                  conv.difficulty == _selectedDifficulty)
              .toList();
        } else if (_selectedCategory != 'all' && _selectedLevel != 'all') {
          conversations = conversations
              .where((conv) =>
                  conv.category == _selectedCategory &&
                  conv.level == _selectedLevel)
              .toList();
        } else if (_selectedDifficulty != 'all' && _selectedLevel != 'all') {
          conversations = conversations
              .where((conv) =>
                  conv.difficulty == _selectedDifficulty &&
                  conv.level == _selectedLevel)
              .toList();
        }

        return ListView.builder(
          itemCount: conversations.length,
          itemBuilder: (context, index) {
            final conversation = conversations[index];
            return _buildConversationCard(conversation);
          },
        );
      },
    );
  }

  Widget _buildConversationCard(ConversationModel conversation) {
    final userId =
        Provider.of<ConversationService>(context, listen: false).currentUserId;
    final bool isRead =
        userId != null && conversation.isReadByCurrentUser(userId);
    final double progress =
        userId != null ? conversation.getReadProgress(userId) : 0.0;
    final double score =
        userId != null ? conversation.getPronunciationScore(userId) : 0.0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ConversationDetailScreen(
                conversationId: conversation.id,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      conversation.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(conversation.difficulty),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getDifficultyLabel(conversation.difficulty),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    'الفئة: ${conversation.category}',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'المستوى: ${conversation.level}',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    isRead ? Icons.check_circle : Icons.circle_outlined,
                    color: isRead ? Colors.green : Colors.grey,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isRead ? 'تمت القراءة' : 'لم تتم القراءة بعد',
                    style: TextStyle(
                      color: isRead ? Colors.green : Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${conversation.messages.length} رسائل',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              if (isRead) ...[
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم: ${(progress * 100).toInt()}%',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'النتيجة: ${(score * 100).toInt()}%',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget? _buildAddButton() {
    // Solo mostrar el botón de añadir si el usuario es administrador
    // Esto se podría verificar con un servicio de autenticación
    return FloatingActionButton(
      onPressed: () async {
        // Mostrar diálogo de opciones
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('إضافة محادثة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.add_circle),
                  title: const Text('إنشاء محادثة جديدة'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/create_conversation');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.content_paste),
                  title: const Text('إنشاء محادثة بالنسخ واللصق'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/create_conversation_bulk');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.chat_bubble_outline),
                  title: const Text('إضافة محادثة نموذجية'),
                  onTap: () {
                    // Cerrar el diálogo de opciones
                    Navigator.pop(context);

                    // Crear la conversación de ejemplo
                    _createSampleConversation();
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        );
      },
      tooltip: 'إضافة محادثة جديدة',
      child: const Icon(Icons.add),
    );
  }

  // Método para crear una conversación de ejemplo
  Future<void> _createSampleConversation() async {
    // Mostrar indicador de carga
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري إنشاء المحادثة النموذجية...'),
          ],
        ),
      ),
    );

    try {
      // Crear la conversación de ejemplo en Firestore
      await SampleConversationData.createSampleConversationInFirestore();

      if (mounted) {
        // Cerrar el diálogo de carga
        Navigator.of(context).pop();

        // Mostrar mensaje de éxito
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المحادثة النموذجية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Cerrar el diálogo de carga
        Navigator.of(context).pop();

        // Mostrar mensaje de error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية المحادثات'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Filtro por categoría
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الفئة',
                    ),
                    value: _selectedCategory,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(
                          value: 'greetings', child: Text('التحيات')),
                      DropdownMenuItem(value: 'travel', child: Text('السفر')),
                      DropdownMenuItem(value: 'food', child: Text('الطعام')),
                      DropdownMenuItem(
                          value: 'shopping', child: Text('التسوق')),
                      DropdownMenuItem(value: 'work', child: Text('العمل')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // Filtro por dificultad
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'مستوى الصعوبة',
                    ),
                    value: _selectedDifficulty,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(value: 'easy', child: Text('سهل')),
                      DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                      DropdownMenuItem(value: 'hard', child: Text('صعب')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDifficulty = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Filtro por nivel
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'المستوى',
                    ),
                    value: _selectedLevel,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(value: '1', child: Text('المستوى 1')),
                      DropdownMenuItem(value: '2', child: Text('المستوى 2')),
                      DropdownMenuItem(value: '3', child: Text('المستوى 3')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedLevel = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // Filtro por estado de lectura
                  CheckboxListTile(
                    title: const Text('المحادثات المقروءة فقط'),
                    value: _showOnlyRead,
                    onChanged: (value) {
                      setState(() {
                        _showOnlyRead = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    this.setState(() {
                      // Los valores ya se han actualizado en el StatefulBuilder
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _getDifficultyLabel(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return difficulty;
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Método para sincronizar las conversaciones
  Future<void> _syncConversations() async {
    if (_isSyncing) return;

    // Obtener el servicio antes de cualquier operación asíncrona
    final conversationService =
        Provider.of<ConversationService>(context, listen: false);

    setState(() {
      _isSyncing = true;
    });

    // Mostrar indicador de carga
    _showLoadingDialog();

    try {
      // Sincronizar con un tiempo límite
      final conversations =
          await _syncConversationsWithTimeout(conversationService);

      // Cerrar el diálogo y mostrar mensaje de éxito
      if (mounted) {
        _hideLoadingDialog();
        _showSuccessMessage(conversations.length);
      }
    } catch (e) {
      // Cerrar el diálogo y mostrar mensaje de error
      if (mounted) {
        _hideLoadingDialog();
        _showErrorMessage(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  // Mostrar diálogo de carga
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const PopScope(
          canPop: false,
          child: AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('جاري مزامنة المحادثات...'),
              ],
            ),
          ),
        );
      },
    );
  }

  // Ocultar diálogo de carga
  void _hideLoadingDialog() {
    Navigator.of(context).pop();
  }

  // Mostrar mensaje de éxito
  void _showSuccessMessage(int count) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مزامنة $count محادثة بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  // Mostrar mensaje de error
  void _showErrorMessage(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('حدث خطأ أثناء المزامنة: $error'),
        backgroundColor: Colors.red,
      ),
    );
  }

  // Sincronizar conversaciones con tiempo límite
  Future<List<ConversationModel>> _syncConversationsWithTimeout(
      ConversationService service) async {
    return await Future.delayed(const Duration(milliseconds: 500), () async {
      return await service.syncAllConversations();
    }).timeout(
      const Duration(seconds: 15),
      onTimeout: () {
        throw Exception(
            'انتهت مهلة المزامنة. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.');
      },
    );
  }
}
