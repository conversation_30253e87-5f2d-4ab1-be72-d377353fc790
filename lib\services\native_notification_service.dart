import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'simple_notification_service.dart';

/// A notification service that uses platform channels to send notifications
/// This avoids the compatibility issues with flutter_local_notifications
class NativeNotificationService {
  // Singleton pattern
  static final NativeNotificationService _instance =
      NativeNotificationService._internal();
  factory NativeNotificationService() => _instance;
  NativeNotificationService._internal();

  // Platform channel for native communication
  static const platform = MethodChannel('com.example.test05/notifications');

  // Send a test notification
  Future<void> showTestNotification() async {
    try {
      await platform.invokeMethod('showTestNotification', {
        'title': 'اختبار الإشعارات',
        'body': 'هذا إشعار تجريبي من تطبيق 10 Again',
        'channelId': 'test_channel',
        'channelName': 'Test Channel',
        'channelDescription': 'Channel for test notifications',
      });
      debugPrint('Test notification sent successfully');
    } on PlatformException catch (e) {
      debugPrint('Failed to send test notification: ${e.message}');
    } catch (e) {
      debugPrint('Error sending test notification: $e');
    }
  }

  // Send a notification for unfinished sentences
  Future<void> showUnfinishedSentencesNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['unfinishedSentencesEnabled'] as bool;

      if (!isEnabled) {
        debugPrint('Unfinished sentences notifications are disabled');
        return;
      }

      await platform.invokeMethod('showNotification', {
        'title': 'جمل غير مكتملة',
        'body': 'لديك جمل لم تكملها بعد. هيا لنكملها الآن!',
        'channelId': 'unfinished_sentences_channel',
        'channelName': 'Unfinished Sentences',
        'channelDescription': 'Notifications for unfinished sentences',
      });
      debugPrint('Unfinished sentences notification sent successfully');
    } on PlatformException catch (e) {
      debugPrint(
          'Failed to send unfinished sentences notification: ${e.message}');
    } catch (e) {
      debugPrint('Error sending unfinished sentences notification: $e');
    }
  }

  // Schedule unfinished sentences notification at the specified time
  Future<void> scheduleUnfinishedSentencesNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['unfinishedSentencesEnabled'] as bool;
      final timeString = settings['unfinishedSentencesTime'] as String;

      if (!isEnabled) {
        debugPrint('Unfinished sentences notifications are disabled');
        return;
      }

      // Parse time string to get hour and minute
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // Calculate delay until the specified time
      final now = DateTime.now();
      final scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      // If the time has already passed today, schedule for tomorrow
      DateTime targetTime = scheduledTime;
      if (scheduledTime.isBefore(now)) {
        targetTime = scheduledTime.add(const Duration(days: 1));
      }

      // Calculate delay in milliseconds
      final delayInMillis = targetTime.difference(now).inMilliseconds;

      // Cancel any existing notification with the same ID
      try {
        await platform.invokeMethod('cancelScheduledNotification', {
          'title': 'جمل غير مكتملة',
          'body': 'لديك جمل لم تكملها بعد. هيا لنكملها الآن!',
          'channelId': 'unfinished_sentences_channel',
        });
        debugPrint('Cancelled existing unfinished sentences notification');
      } catch (e) {
        debugPrint('Error cancelling notification: $e');
      }

      // Only schedule if the delay is reasonable (more than 1 minute and less than 24 hours)
      if (delayInMillis > 60000 && delayInMillis < 86400000) {
        // Schedule the notification
        await platform.invokeMethod('scheduleNotification', {
          'title': 'جمل غير مكتملة',
          'body': 'لديك جمل لم تكملها بعد. هيا لنكملها الآن!',
          'channelId': 'unfinished_sentences_channel',
          'channelName': 'Unfinished Sentences',
          'channelDescription': 'Notifications for unfinished sentences',
          'delayInMillis': delayInMillis,
        });

        debugPrint(
            'Unfinished sentences notification scheduled for $targetTime (in ${delayInMillis / 1000 / 60} minutes)');
      } else {
        debugPrint(
            'Skipping unfinished sentences notification scheduling due to invalid delay: $delayInMillis ms');
      }
    } on PlatformException catch (e) {
      debugPrint(
          'Failed to schedule unfinished sentences notification: ${e.message}');
    } catch (e) {
      debugPrint('Error scheduling unfinished sentences notification: $e');
    }
  }

  // Send a notification for new sentences added
  Future<void> showNewSentencesAddedNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['newSentencesAddedEnabled'] as bool;

      if (!isEnabled) {
        debugPrint('New sentences notifications are disabled');
        return;
      }

      // Show immediate notification
      await platform.invokeMethod('showNotification', {
        'title': 'جمل جديدة',
        'body': 'تم إضافة جمل جديدة إلى قاعدة البيانات. هيا لنقرأها!',
        'channelId': 'new_sentences_channel',
        'channelName': 'New Sentences',
        'channelDescription': 'Notifications for new sentences',
      });

      // Also schedule a notification with a slight delay to ensure it appears
      // even when the app is in foreground
      await platform.invokeMethod('scheduleNotification', {
        'title': 'جمل جديدة',
        'body': 'تم إضافة جمل جديدة إلى قاعدة البيانات. هيا لنقرأها!',
        'channelId': 'new_sentences_channel',
        'channelName': 'New Sentences',
        'channelDescription': 'Notifications for new sentences',
        'delayInMillis':
            500, // Short delay to ensure it appears even if the app is in foreground
      });

      debugPrint(
          'New sentences notification sent successfully (immediate and scheduled)');
    } on PlatformException catch (e) {
      debugPrint('Failed to send new sentences notification: ${e.message}');
    } catch (e) {
      debugPrint('Error sending new sentences notification: $e');
    }
  }

  // Send a morning reminder notification
  Future<void> showMorningReminderNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['morningReminderEnabled'] as bool;

      if (!isEnabled) {
        debugPrint('Morning reminder notifications are disabled');
        return;
      }

      await platform.invokeMethod('showNotification', {
        'title': 'تذكير صباحي',
        'body': 'لديك جمل من اليوم السابق لم تقرأها بعد. هيا لنقرأها الآن!',
        'channelId': 'morning_reminder_channel',
        'channelName': 'Morning Reminder',
        'channelDescription': 'Morning reminder notifications',
      });
      debugPrint('Morning reminder notification sent successfully');
    } on PlatformException catch (e) {
      debugPrint('Failed to send morning reminder notification: ${e.message}');
    } catch (e) {
      debugPrint('Error sending morning reminder notification: $e');
    }
  }

  // Schedule morning reminder notification
  Future<void> scheduleMorningReminderNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['morningReminderEnabled'] as bool;
      final timeString = settings['morningReminderTime'] as String;

      if (!isEnabled) {
        debugPrint('Morning reminder notifications are disabled');
        return;
      }

      // Parse time string to get hour and minute
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // Calculate delay until the specified time
      final now = DateTime.now();
      final scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      // If the time has already passed today, schedule for tomorrow
      DateTime targetTime = scheduledTime;
      if (scheduledTime.isBefore(now)) {
        targetTime = scheduledTime.add(const Duration(days: 1));
      }

      // Calculate delay in milliseconds
      final delayInMillis = targetTime.difference(now).inMilliseconds;

      // Cancel any existing notification with the same ID
      try {
        await platform.invokeMethod('cancelScheduledNotification', {
          'title': 'تذكير صباحي',
          'body': 'لديك جمل من اليوم السابق لم تقرأها بعد. هيا لنقرأها الآن!',
          'channelId': 'morning_reminder_channel',
        });
        debugPrint('Cancelled existing morning reminder notification');
      } catch (e) {
        debugPrint('Error cancelling notification: $e');
      }

      // Only schedule if the delay is reasonable (more than 1 minute and less than 24 hours)
      if (delayInMillis > 60000 && delayInMillis < 86400000) {
        // Schedule the notification
        await platform.invokeMethod('scheduleNotification', {
          'title': 'تذكير صباحي',
          'body': 'لديك جمل من اليوم السابق لم تقرأها بعد. هيا لنقرأها الآن!',
          'channelId': 'morning_reminder_channel',
          'channelName': 'Morning Reminder',
          'channelDescription': 'Morning reminder notifications',
          'delayInMillis': delayInMillis,
        });

        debugPrint(
            'Morning reminder notification scheduled for $targetTime (in ${delayInMillis / 1000 / 60} minutes)');
      } else {
        debugPrint(
            'Skipping morning reminder notification scheduling due to invalid delay: $delayInMillis ms');
      }
    } on PlatformException catch (e) {
      debugPrint(
          'Failed to schedule morning reminder notification: ${e.message}');
    } catch (e) {
      debugPrint('Error scheduling morning reminder notification: $e');
    }
  }

  // Send a new day motivation notification
  Future<void> showNewDayMotivationNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['newDayMotivationEnabled'] as bool;

      if (!isEnabled) {
        debugPrint('New day motivation notifications are disabled');
        return;
      }

      await platform.invokeMethod('showNotification', {
        'title': 'يوم جديد',
        'body': 'يوم جديد، جمل جديدة! هيا لنبدأ يومنا بقراءة جديدة!',
        'channelId': 'new_day_motivation_channel',
        'channelName': 'New Day Motivation',
        'channelDescription': 'New day motivation notifications',
      });
      debugPrint('New day motivation notification sent successfully');
    } on PlatformException catch (e) {
      debugPrint(
          'Failed to send new day motivation notification: ${e.message}');
    } catch (e) {
      debugPrint('Error sending new day motivation notification: $e');
    }
  }

  // Schedule new day motivation notification
  Future<void> scheduleNewDayMotivationNotification() async {
    try {
      // Get notification settings
      final settings =
          await SimpleNotificationService().getNotificationSettings();
      final isEnabled = settings['newDayMotivationEnabled'] as bool;
      final timeString = settings['newDayMotivationTime'] as String;

      if (!isEnabled) {
        debugPrint('New day motivation notifications are disabled');
        return;
      }

      // Parse time string to get hour and minute
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // Calculate delay until the specified time
      final now = DateTime.now();
      final scheduledTime = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      // If the time has already passed today, schedule for tomorrow
      DateTime targetTime = scheduledTime;
      if (scheduledTime.isBefore(now)) {
        targetTime = scheduledTime.add(const Duration(days: 1));
      }

      // Calculate delay in milliseconds
      final delayInMillis = targetTime.difference(now).inMilliseconds;

      // Cancel any existing notification with the same ID
      try {
        await platform.invokeMethod('cancelScheduledNotification', {
          'title': 'يوم جديد',
          'body': 'يوم جديد، جمل جديدة! هيا لنبدأ يومنا بقراءة جديدة!',
          'channelId': 'new_day_motivation_channel',
        });
        debugPrint('Cancelled existing new day motivation notification');
      } catch (e) {
        debugPrint('Error cancelling notification: $e');
      }

      // Only schedule if the delay is reasonable (more than 1 minute and less than 24 hours)
      if (delayInMillis > 60000 && delayInMillis < 86400000) {
        // Schedule the notification
        await platform.invokeMethod('scheduleNotification', {
          'title': 'يوم جديد',
          'body': 'يوم جديد، جمل جديدة! هيا لنبدأ يومنا بقراءة جديدة!',
          'channelId': 'new_day_motivation_channel',
          'channelName': 'New Day Motivation',
          'channelDescription': 'New day motivation notifications',
          'delayInMillis': delayInMillis,
        });

        debugPrint(
            'New day motivation notification scheduled for $targetTime (in ${delayInMillis / 1000 / 60} minutes)');
      } else {
        debugPrint(
            'Skipping new day motivation notification scheduling due to invalid delay: $delayInMillis ms');
      }
    } on PlatformException catch (e) {
      debugPrint(
          'Failed to schedule new day motivation notification: ${e.message}');
    } catch (e) {
      debugPrint('Error scheduling new day motivation notification: $e');
    }
  }

  // Schedule all notifications based on user settings
  Future<void> scheduleAllNotifications() async {
    try {
      debugPrint('Scheduling all notifications...');

      // Cancel all existing notifications first
      await cancelAllScheduledNotifications();

      // Schedule unfinished sentences notification
      await scheduleUnfinishedSentencesNotification();

      // Schedule morning reminder notification
      await scheduleMorningReminderNotification();

      // Schedule new day motivation notification
      await scheduleNewDayMotivationNotification();

      debugPrint('All notifications scheduled successfully');
    } catch (e) {
      debugPrint('Error scheduling all notifications: $e');
    }
  }

  // Cancel all scheduled notifications
  Future<void> cancelAllScheduledNotifications() async {
    try {
      await platform.invokeMethod('cancelAllScheduledNotifications');
      debugPrint('All scheduled notifications cancelled');
    } on PlatformException catch (e) {
      debugPrint('Failed to cancel all scheduled notifications: ${e.message}');
    } catch (e) {
      debugPrint('Error cancelling all scheduled notifications: $e');
    }
  }
}
