@echo off
set "manifestPath=C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_mlkit_smart_reply-0.9.0\android\src\main\AndroidManifest.xml"

if exist "%manifestPath%" (
    echo Fixing AndroidManifest.xml...
    
    rem Create a temporary file
    type "%manifestPath%" > temp_manifest.xml
    
    rem Replace the package attribute using findstr and echo
    type temp_manifest.xml | findstr /v "package=\"com.google_mlkit_smart_reply\"" > "%manifestPath%"
    
    rem Clean up
    del temp_manifest.xml
    
    echo Successfully fixed AndroidManifest.xml
) else (
    echo AndroidManifest.xml file not found at: %manifestPath%
)
