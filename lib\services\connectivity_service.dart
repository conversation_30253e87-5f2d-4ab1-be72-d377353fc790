import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/sentence_service.dart';

class ConnectivityService extends ChangeNotifier {
  bool _isOnline = true;
  bool _syncInProgress = false;
  DateTime? _lastConnectivityCheck;
  final Connectivity _connectivity = Connectivity();
  final SentenceService _sentenceService = SentenceService();

  // تحديد فترة زمنية للتحقق من الاتصال (5 ثوانٍ)
  static const Duration _connectivityThrottle = Duration(seconds: 5);

  ConnectivityService() {
    _initConnectivity();
    _setupConnectivityStream();
  }

  bool get isOnline => _isOnline;
  bool get isSyncInProgress => _syncInProgress;

  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
      _lastConnectivityCheck = DateTime.now();
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال: $e');
      _isOnline = false;
    }
  }

  void _setupConnectivityStream() {
    _connectivity.onConnectivityChanged.listen((result) {
      // تطبيق التحديد الزمني للتحقق من الاتصال
      final now = DateTime.now();
      if (_lastConnectivityCheck == null ||
          now.difference(_lastConnectivityCheck!) > _connectivityThrottle) {
        _updateConnectionStatus(result);
        _lastConnectivityCheck = now;
      }
    });
  }

  void _updateConnectionStatus(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    if (wasOnline != _isOnline) {
      // إذا كان الاتصال قد عاد بعد انقطاع
      if (_isOnline && !wasOnline) {
        // تأخير المزامنة لتجنب التأثير على أداء واجهة المستخدم
        Future.delayed(const Duration(seconds: 2), () {
          _syncPendingSentences();
        });
      }

      notifyListeners();
    }
  }

  // مزامنة الجمل المعلقة عند عودة الاتصال
  Future<void> _syncPendingSentences() async {
    // تجنب تشغيل عمليات مزامنة متعددة في نفس الوقت
    if (_syncInProgress) {
      debugPrint('المزامنة قيد التنفيذ بالفعل، تم تخطي طلب المزامنة الجديد');
      return;
    }

    try {
      _syncInProgress = true;

      // التحقق مما إذا كان المستخدم مصادقًا عليه
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        debugPrint('تم استعادة الاتصال، جاري مزامنة الجمل المعلقة');
        await _sentenceService.syncPendingSentences(user.uid);
      }
    } catch (e) {
      debugPrint('خطأ في مزامنة الجمل المعلقة: $e');
    } finally {
      _syncInProgress = false;
    }
  }

  // التحقق من الاتصال بالإنترنت (للاستخدام العام)
  Future<bool> checkConnectivity() async {
    // تطبيق التحديد الزمني للتحقق من الاتصال
    final now = DateTime.now();
    if (_lastConnectivityCheck != null &&
        now.difference(_lastConnectivityCheck!) < _connectivityThrottle) {
      // استخدام القيمة المخزنة إذا كان التحقق الأخير حديثًا
      return _isOnline;
    }

    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
      _lastConnectivityCheck = now;
      return _isOnline;
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال: $e');
      return false;
    }
  }
}
