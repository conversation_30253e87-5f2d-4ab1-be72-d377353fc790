import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/conversation_model.dart';
import '../services/conversation_service.dart';
import '../theme/app_theme.dart';
import 'conversation_detail_screen.dart';

class ReadConversationsScreen extends StatefulWidget {
  const ReadConversationsScreen({Key? key}) : super(key: key);

  @override
  State<ReadConversationsScreen> createState() =>
      _ReadConversationsScreenState();
}

class _ReadConversationsScreenState extends State<ReadConversationsScreen> {
  String _selectedCategory = 'all';
  String _selectedDifficulty = 'all';

  @override
  Widget build(BuildContext context) {
    final conversationService = Provider.of<ConversationService>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات المقروءة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية',
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtros activos
          if (_selectedCategory != 'all' || _selectedDifficulty != 'all')
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.grey.shade200,
              child: Row(
                children: [
                  const Text('التصفية النشطة: ',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  if (_selectedCategory != 'all')
                    Chip(
                      label: Text(_selectedCategory),
                      onDeleted: () {
                        setState(() {
                          _selectedCategory = 'all';
                        });
                      },
                    ),
                  const SizedBox(width: 8),
                  if (_selectedDifficulty != 'all')
                    Chip(
                      label: Text(_getDifficultyLabel(_selectedDifficulty)),
                      onDeleted: () {
                        setState(() {
                          _selectedDifficulty = 'all';
                        });
                      },
                    ),
                ],
              ),
            ),

          // Lista de conversaciones leídas
          Expanded(
            child: _buildReadConversationsList(conversationService),
          ),
        ],
      ),
    );
  }

  Widget _buildReadConversationsList(ConversationService conversationService) {
    return StreamBuilder<List<ConversationModel>>(
      stream: conversationService.getReadConversations(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('لم تقرأ أي محادثات بعد'));
        }

        List<ConversationModel> conversations = snapshot.data!;

        // Aplicar filtros
        if (_selectedCategory != 'all') {
          conversations = conversations
              .where((conv) => conv.category == _selectedCategory)
              .toList();
        }
        if (_selectedDifficulty != 'all') {
          conversations = conversations
              .where((conv) => conv.difficulty == _selectedDifficulty)
              .toList();
        }

        if (conversations.isEmpty) {
          return const Center(
              child: Text('لا توجد محادثات تطابق عوامل التصفية'));
        }

        return ListView.builder(
          itemCount: conversations.length,
          itemBuilder: (context, index) {
            final conversation = conversations[index];
            return _buildConversationCard(conversation);
          },
        );
      },
    );
  }

  Widget _buildConversationCard(ConversationModel conversation) {
    final userId =
        Provider.of<ConversationService>(context, listen: false).currentUserId;
    if (userId == null) return const SizedBox.shrink();

    final double progress = conversation.getReadProgress(userId);
    final double score = conversation.getPronunciationScore(userId);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ConversationDetailScreen(
                conversationId: conversation.id,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      conversation.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getDifficultyColor(conversation.difficulty),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getDifficultyLabel(conversation.difficulty),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'الفئة: ${conversation.category}',
                style: TextStyle(
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'تمت القراءة',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${conversation.messages.length} رسائل',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey.shade300,
                valueColor: const AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'التقدم: ${(progress * 100).toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  ),
                  Text(
                    'النتيجة: ${(score * 100).toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية المحادثات'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Filtro por categoría
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الفئة',
                    ),
                    value: _selectedCategory,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(
                          value: 'greetings', child: Text('التحيات')),
                      DropdownMenuItem(value: 'travel', child: Text('السفر')),
                      DropdownMenuItem(value: 'food', child: Text('الطعام')),
                      DropdownMenuItem(
                          value: 'shopping', child: Text('التسوق')),
                      DropdownMenuItem(value: 'work', child: Text('العمل')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  // Filtro por dificultad
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'المستوى',
                    ),
                    value: _selectedDifficulty,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(value: 'easy', child: Text('سهل')),
                      DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                      DropdownMenuItem(value: 'hard', child: Text('صعب')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedDifficulty = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    this.setState(() {
                      // Los valores ya se han actualizado en el StatefulBuilder
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String _getDifficultyLabel(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return difficulty;
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
