rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read: if request.auth != null && (
        request.auth.uid == userId ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm"
      );
      allow update, delete: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null;

      // التحكم في الجمل المقروءة والمفضلة
      match /readSentences/{sentenceId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      match /favorites/{sentenceId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // التحكم في تقدم المستخدم
      match /progress/{progressId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }

      // التحكم في نقاط المستخدم - تبسيط القواعد
      match /points/{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    match /sentences/{sentenceId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && resource.data.createdBy == request.auth.uid;
    }

    match /categories/{categoryId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // السماح بقراءة المستويات لجميع المستخدمين المسجلين
    match /levels/{levelId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");

      // السماح بقراءة مجموعات الدروس
      match /lessonGroups/{groupId} {
        allow read: if request.auth != null;
        allow write: if request.auth != null &&
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
      }

      // السماح بقراءة الدورات
      match /cycles/{cycleId} {
        allow read: if request.auth != null;
        allow write: if request.auth != null &&
          (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");

        // السماح بقراءة مجموعات الدروس داخل الدورات
        match /lessonGroups/{groupId} {
          allow read: if request.auth != null;
          allow write: if request.auth != null &&
            (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
        }
      }
    }

    // السماح بقراءة إعدادات النقاط لجميع المستخدمين المسجلين
    match /settings/{settingId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
    }

    // السماح بقراءة المحادثات لجميع المستخدمين المسجلين
    match /conversations/{conversationId} {
      allow read: if request.auth != null;
      // السماح مؤقتًا بإضافة محادثات لجميع المستخدمين
      allow create: if request.auth != null;
      // السماح بالتعديل والحذف للمسؤولين فقط
      allow update, delete: if request.auth != null &&
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeuser == "adm" ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.typeUser == "adm");
    }

    // السماح بقراءة وكتابة مجموعات المستخدمين
    match /userGroups/{groupId} {
      allow read, write: if request.auth != null;
    }

    // السماح بقراءة وكتابة مجموعات المستويات
    match /levelGroups/{groupId} {
      allow read, write: if request.auth != null;
    }
  }
}

service firebase.storage {
  match /b/{bucket}/o {
    match /user_images/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
