{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/ANDROIDSDK01/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/ANDROIDSDK01/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/ANDROIDSDK01/cmake/3.22.1/bin/ctest.exe", "root": "F:/ANDROIDSDK01/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-73c106f1078306723a27.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-165935e8b8634734c931.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0cb7dacdeff5c9887692.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-165935e8b8634734c931.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0cb7dacdeff5c9887692.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-73c106f1078306723a27.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}