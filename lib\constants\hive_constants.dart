/// هذا الملف يحتوي على ثوابت Hive المستخدمة في التطبيق
class HiveConstants {
  // أسماء صناديق Hive
  static const String sentencesBox = 'sentences_box';
  static const String readSentencesBox = 'read_sentences_box';
  static const String favoritesBox = 'favorites_box';
  static const String categoriesBox = 'categories_box';
  static const String statisticsBox = 'statistics_box';
  static const String userBox = 'user_box';
  static const String syncLogBox = 'sync_log_box';
  static const String displayedSentencesBox = 'displayed_sentences_box';

  // معرفات أنواع Hive
  static const int sentenceTypeId = 1;
  static const int readSentenceTypeId = 2;
  static const int favoriteTypeId = 3;
  static const int categoryTypeId = 4;
  static const int statisticsTypeId = 5;
  static const int syncOperationTypeId = 6;
  static const int syncLogTypeId = 7;
  static const int timestampTypeId = 10;
  static const int dateTimeTypeId = 11;

  // أسماء صناديق إضافية
  static const String conversationsBox = 'conversations_box';

  // مفاتيح خاصة في صناديق Hive
  static const String lastSyncKey = 'last_sync';
  static const String dailySentencesKey = 'daily_sentences';
  static const String todayShownCountKey = 'today_shown_count';
  static const String todayReadCountKey = 'today_read_count';
  static const String todayDateKey = 'today_date';
}
