import 'package:firebase_auth/firebase_auth.dart' as auth;
import '../models/user_model.dart';

class AuthService {
  final auth.FirebaseAuth _auth = auth.FirebaseAuth.instance;

  // تحويل مستخدم Firebase إلى نموذج المستخدم الخاص بنا
  UserModel? _userFromFirebaseUser(auth.User? user) {
    if (user == null) return null;
    return UserModel(
      id: user.uid,
      email: user.email,
      displayName: user.displayName,
    );
  }

  // تسجيل الدخول
  Future<UserModel?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return _userFromFirebaseUser(credential.user);
    } on auth.FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'invalid-email':
          throw Exception('البريد الإلكتروني غير صالح');
        case 'user-disabled':
          throw Exception('هذا الحساب معطل');
        case 'user-not-found':
          throw Exception('لم يتم العثور على حساب بهذا البريد الإلكتروني');
        case 'wrong-password':
          throw Exception('كلمة المرور غير صحيحة');
        case 'too-many-requests':
          throw Exception('تم تجاوز عدد المحاولات المسموح بها، الرجاء المحاولة لاحقاً');
        default:
          throw Exception('خطأ في تسجيل الدخول: ${e.message}');
      }
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع');
    }
  }

  // إنشاء حساب جديد
  Future<UserModel?> registerWithEmailAndPassword(
      String email, String password, String name) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      await credential.user?.updateDisplayName(name);
      await credential.user?.reload();

      return _userFromFirebaseUser(_auth.currentUser);
    } on auth.FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'email-already-in-use':
          throw Exception('هذا البريد الإلكتروني مستخدم بالفعل');
        case 'invalid-email':
          throw Exception('البريد الإلكتروني غير صالح');
        case 'weak-password':
          throw Exception('كلمة المرور ضعيفة جداً');
        default:
          throw Exception('فشل إنشاء الحساب: ${e.message}');
      }
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('فشل تسجيل الخروج: $e');
    }
  }

  // إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on auth.FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'user-not-found':
          throw Exception('البريد الإلكتروني غير مسجل');
        case 'invalid-email':
          throw Exception('البريد الإلكتروني غير صالح');
        default:
          throw Exception('فشل إعادة تعيين كلمة المرور: ${e.message}');
      }
    } catch (e) {
      throw Exception('حدث خطأ غير متوقع: $e');
    }
  }
}
