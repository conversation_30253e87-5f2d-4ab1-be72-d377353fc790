import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'hive_sentence_service.dart';
import '../models/hive/hive_sentence_model.dart';
import '../models/hive/sync_log_model.dart';
import '../models/hive/statistics_model.dart';
import '../constants/hive_constants.dart';

/// خدمة مزامنة البيانات بين Hive و Firebase
class SyncService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final HiveSentenceService _hiveSentenceService;
  final Connectivity _connectivity = Connectivity();

  // مجموعات Firestore
  static const String _sentencesCollection = 'sentences';
  static const String _usersCollection = 'users';
  static const String _readSentencesCollection = 'readSentences';
  static const String _favoritesCollection = 'favorites';
  static const String _statsCollection = 'stats';

  SyncService(this._hiveSentenceService);

  /// التحقق من حالة الاتصال بالإنترنت
  Future<bool> isOnline() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// مزامنة البيانات مع Firebase
  Future<bool> syncWithFirebase(String userId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final online = await isOnline();
      if (!online) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي المزامنة');
        return false;
      }

      debugPrint('بدء المزامنة مع Firebase للمستخدم: $userId');

      // الحصول على سجلات المزامنة غير المتزامنة
      final unsyncedLogs = _hiveSentenceService.getUnsyncedLogs();
      debugPrint('عدد سجلات المزامنة غير المتزامنة: ${unsyncedLogs.length}');

      if (unsyncedLogs.isEmpty) {
        debugPrint('لا توجد تغييرات للمزامنة');
        // تحديث وقت آخر مزامنة حتى لو لم تكن هناك تغييرات
        await _hiveSentenceService.updateLastSyncTime();
        return true;
      }

      // مزامنة سجلات القراءة
      final readLogs = unsyncedLogs
          .where((log) => log.operationType == SyncOperationType.read)
          .toList();

      if (readLogs.isNotEmpty) {
        try {
          await _syncReadSentences(userId, readLogs);

          // تعيين سجلات القراءة كمتزامنة فوراً
          for (final log in readLogs) {
            await _hiveSentenceService.markLogAsSynced(log.id);
          }
          debugPrint('تم تعيين ${readLogs.length} سجل قراءة كمتزامن');
        } catch (e) {
          debugPrint('خطأ في مزامنة سجلات القراءة: $e');
        }
      }

      // مزامنة المفضلة
      final favoriteLogs = unsyncedLogs
          .where((log) =>
              log.operationType == SyncOperationType.favorite ||
              log.operationType == SyncOperationType.unfavorite)
          .toList();

      if (favoriteLogs.isNotEmpty) {
        try {
          await _syncFavorites(userId, favoriteLogs);

          // تعيين سجلات المفضلة كمتزامنة فوراً
          for (final log in favoriteLogs) {
            await _hiveSentenceService.markLogAsSynced(log.id);
          }
          debugPrint('تم تعيين ${favoriteLogs.length} سجل مفضلة كمتزامن');
        } catch (e) {
          debugPrint('خطأ في مزامنة سجلات المفضلة: $e');
        }
      }

      // مزامنة الإحصائيات
      try {
        await _syncStatistics(userId);
      } catch (e) {
        debugPrint('خطأ في مزامنة الإحصائيات: $e');
      }

      // التحقق من أي سجلات متبقية لم يتم تعيينها كمتزامنة
      final remainingUnsyncedLogs =
          unsyncedLogs.where((log) => !log.isSynced).toList();

      if (remainingUnsyncedLogs.isNotEmpty) {
        debugPrint(
            'هناك ${remainingUnsyncedLogs.length} سجل لم يتم تعيينه كمتزامن، محاولة تعيينه الآن');

        // تعيين السجلات المتبقية كمتزامنة
        int successCount = 0;
        for (final log in remainingUnsyncedLogs) {
          try {
            final success = await _hiveSentenceService.markLogAsSynced(log.id);
            if (success) {
              successCount++;
            }
          } catch (e) {
            debugPrint('خطأ في تعيين سجل المزامنة ${log.id} كمتزامن: $e');
          }
        }

        debugPrint(
            'تم تعيين $successCount من أصل ${remainingUnsyncedLogs.length} سجل متبقي كمتزامن');
      } else {
        debugPrint('تم تعيين جميع السجلات كمتزامنة بنجاح');
      }

      // التحقق من عدد العناصر المتبقية بعد المزامنة
      final remainingLogs = _hiveSentenceService.getUnsyncedLogs();
      debugPrint('عدد العناصر المتبقية بعد المزامنة: ${remainingLogs.length}');

      // تحديث وقت آخر مزامنة
      await _hiveSentenceService.updateLastSyncTime();

      debugPrint('تمت المزامنة مع Firebase بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في المزامنة مع Firebase: $e');
      return false;
    }
  }

  /// مزامنة الجمل المقروءة
  Future<void> _syncReadSentences(
      String userId, List<SyncLogModel> readLogs) async {
    try {
      final batch = _firestore.batch();

      for (final log in readLogs) {
        final docRef = _firestore
            .collection(_usersCollection)
            .doc(userId)
            .collection(_readSentencesCollection)
            .doc(log.sentenceId);

        batch.set(docRef, {
          'readAt': Timestamp.fromDate(log.timestamp),
        });
      }

      await batch.commit();
      debugPrint('تمت مزامنة ${readLogs.length} جملة مقروءة مع Firebase');
    } catch (e) {
      debugPrint('خطأ في مزامنة الجمل المقروءة مع Firebase: $e');
      rethrow;
    }
  }

  /// مزامنة المفضلة
  Future<void> _syncFavorites(
      String userId, List<SyncLogModel> favoriteLogs) async {
    try {
      final batch = _firestore.batch();

      for (final log in favoriteLogs) {
        final docRef = _firestore
            .collection(_usersCollection)
            .doc(userId)
            .collection(_favoritesCollection)
            .doc(log.sentenceId);

        if (log.operationType == SyncOperationType.favorite) {
          batch.set(docRef, {
            'addedAt': Timestamp.fromDate(log.timestamp),
          });
        } else {
          batch.delete(docRef);
        }
      }

      await batch.commit();
      debugPrint('تمت مزامنة ${favoriteLogs.length} مفضلة مع Firebase');
    } catch (e) {
      debugPrint('خطأ في مزامنة المفضلة مع Firebase: $e');
      rethrow;
    }
  }

  /// مزامنة الإحصائيات
  Future<void> _syncStatistics(String userId) async {
    try {
      // الحصول على إحصائيات اليوم
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final todayKey = today.toIso8601String();

      final todayStats = _hiveSentenceService.getStatisticsBox().get(todayKey);

      if (todayStats != null) {
        await _firestore
            .collection(_usersCollection)
            .doc(userId)
            .collection(_statsCollection)
            .doc('daily')
            .set({
          'date': Timestamp.fromDate(today),
          'shownCount': todayStats.shownCount,
          'readCount': todayStats.readCount,
          'lastUpdated': Timestamp.fromDate(now),
        }, SetOptions(merge: true));

        // تعيين الإحصائيات كمتزامنة
        todayStats.markAsSynced();

        debugPrint('تمت مزامنة إحصائيات اليوم مع Firebase');
      }
    } catch (e) {
      debugPrint('خطأ في مزامنة الإحصائيات مع Firebase: $e');
      rethrow;
    }
  }

  /// استعادة البيانات من Firebase بعد إعادة التثبيت
  Future<void> restoreDataFromFirebase(String userId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final online = await isOnline();
      if (!online) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي استعادة البيانات');
        return;
      }

      debugPrint('بدء استعادة البيانات من Firebase للمستخدم: $userId');

      // استعادة الجمل المقروءة
      await _restoreReadSentences(userId);

      // استعادة المفضلة
      await _restoreFavorites(userId);

      // استعادة الإحصائيات
      await _restoreStatistics(userId);

      // تحديث وقت آخر مزامنة
      await _hiveSentenceService.updateLastSyncTime();

      debugPrint('تمت استعادة البيانات من Firebase بنجاح');
    } catch (e) {
      debugPrint('خطأ في استعادة البيانات من Firebase: $e');
      rethrow;
    }
  }

  /// استعادة الجمل المقروءة
  Future<void> _restoreReadSentences(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_readSentencesCollection)
          .get();

      for (final doc in snapshot.docs) {
        final sentenceId = doc.id;
        final readAt = (doc.data()['readAt'] as Timestamp).toDate();

        // التحقق من وجود الجملة في Hive
        final sentence = _hiveSentenceService.getSentencesBox().get(sentenceId);

        if (sentence != null) {
          // تعيين الجملة كمقروءة محليًا
          sentence.isReadByCurrentUser = true;
          sentence.lastModified = readAt;
          await sentence.save();
        }

        // إضافة معرف الجملة إلى صندوق الجمل المقروءة
        await _hiveSentenceService
            .getReadSentencesBox()
            .put(sentenceId, sentenceId);
      }

      debugPrint('تمت استعادة ${snapshot.docs.length} جملة مقروءة من Firebase');
    } catch (e) {
      debugPrint('خطأ في استعادة الجمل المقروءة من Firebase: $e');
      rethrow;
    }
  }

  /// استعادة المفضلة
  Future<void> _restoreFavorites(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_favoritesCollection)
          .get();

      for (final doc in snapshot.docs) {
        final sentenceId = doc.id;

        // التحقق من وجود الجملة في Hive
        final sentence = _hiveSentenceService.getSentencesBox().get(sentenceId);

        if (sentence != null) {
          // تعيين الجملة كمفضلة محليًا
          sentence.isFavoriteByCurrentUser = true;
          await sentence.save();
        }

        // إضافة معرف الجملة إلى صندوق المفضلة
        await _hiveSentenceService
            .getFavoritesBox()
            .put(sentenceId, sentenceId);
      }

      debugPrint('تمت استعادة ${snapshot.docs.length} مفضلة من Firebase');
    } catch (e) {
      debugPrint('خطأ في استعادة المفضلة من Firebase: $e');
      rethrow;
    }
  }

  /// استعادة الإحصائيات
  Future<void> _restoreStatistics(String userId) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_statsCollection)
          .doc('daily')
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        final date = (data['date'] as Timestamp).toDate();
        final dateKey =
            DateTime(date.year, date.month, date.day).toIso8601String();

        final stats = StatisticsModel(
          id: dateKey,
          date: date,
          shownCount: data['shownCount'] ?? 0,
          readCount: data['readCount'] ?? 0,
          lastUpdated: (data['lastUpdated'] as Timestamp).toDate(),
          isSynced: true,
        );

        await _hiveSentenceService.getStatisticsBox().put(dateKey, stats);

        // تحديث العدادات في userBox
        await _hiveSentenceService
            .getUserBox()
            .put(HiveConstants.todayShownCountKey, stats.shownCount);

        debugPrint('تمت استعادة إحصائيات اليوم من Firebase');
      }
    } catch (e) {
      debugPrint('خطأ في استعادة الإحصائيات من Firebase: $e');
      rethrow;
    }
  }

  /// جلب جميع الجمل من Firebase عند بدء التطبيق لأول مرة
  Future<List<HiveSentenceModel>> fetchAllSentencesFirstTime(
      String userId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final online = await isOnline();
      if (!online) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي جلب الجمل');
        return [];
      }

      // التحقق مما إذا كان هذا هو التحميل الأول
      final isFirstLoad = _hiveSentenceService.getSentencesBox().isEmpty;

      if (!isFirstLoad) {
        debugPrint('ليس التحميل الأول، تم تخطي جلب جميع الجمل');
        return [];
      }

      debugPrint('بدء تحميل جميع الجمل من Firebase للمرة الأولى');

      // جلب جميع الجمل من Firebase
      final snapshot = await _firestore.collection(_sentencesCollection).get();

      final allSentences = <HiveSentenceModel>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>?;
        final sentenceId = doc.id;

        if (data != null) {
          // تحويل الطابع الزمني إلى DateTime
          DateTime createdAt = DateTime.now();
          if (data['createdAt'] != null) {
            if (data['createdAt'] is Timestamp) {
              createdAt = (data['createdAt'] as Timestamp).toDate();
            } else if (data['createdAt'] is String) {
              try {
                createdAt = DateTime.parse(data['createdAt'] as String);
              } catch (e) {
                debugPrint('خطأ في تحويل التاريخ: $e');
              }
            }
          }

          // إنشاء نموذج HiveSentenceModel
          final sentence = HiveSentenceModel(
            id: sentenceId,
            arabicText: data['arabicText'] != null
                ? data['arabicText'] as String? ?? ''
                : '',
            englishText: data['englishText'] != null
                ? data['englishText'] as String? ?? ''
                : '',
            category: data['category'] != null
                ? data['category'] as String? ?? ''
                : '',
            createdAt: createdAt,
            audioUrl:
                data['audioUrl'] != null ? data['audioUrl'] as String? : null,
            difficulty: data['difficulty'] != null
                ? data['difficulty'] as String?
                : null,
            lastModified: DateTime.now(),
          );

          // تخزين الجملة في Hive
          await _hiveSentenceService
              .getSentencesBox()
              .put(sentenceId, sentence);
          allSentences.add(sentence);
        }
      }

      debugPrint('تم تحميل ${allSentences.length} جملة من Firebase');

      // تحديث وقت آخر مزامنة
      await _hiveSentenceService.updateLastSyncTime();

      return allSentences;
    } catch (e) {
      debugPrint('خطأ في جلب جميع الجمل: $e');
      return [];
    }
  }

  /// جلب جمل عشوائية غير موجودة في قائمة محددة
  Future<List<HiveSentenceModel>> fetchRandomSentencesNotInList(
      String userId, List<String> excludeIds, int limit) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final online = await isOnline();
      if (!online) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي جلب الجمل العشوائية');
        return [];
      }

      debugPrint(
          'Fetching random sentences not in exclude list (${excludeIds.length} excluded)');

      // جلب جميع الجمل من Firebase
      final snapshot = await _firestore.collection(_sentencesCollection).get();

      // تصفية الجمل التي ليست في قائمة الاستبعاد
      final availableDocs =
          snapshot.docs.where((doc) => !excludeIds.contains(doc.id)).toList();

      debugPrint('Found ${availableDocs.length} sentences not in exclude list');

      if (availableDocs.isEmpty) {
        debugPrint('No sentences available after filtering');
        return [];
      }

      // خلط الجمل بشكل عشوائي
      availableDocs.shuffle();

      // اختيار عدد محدد من الجمل
      final selectedDocs = availableDocs.take(limit).toList();
      debugPrint('Selected ${selectedDocs.length} random sentences');

      final newSentences = <HiveSentenceModel>[];

      for (final doc in selectedDocs) {
        final data = doc.data() as Map<String, dynamic>?;
        final sentenceId = doc.id;

        if (data != null) {
          DateTime createdAt;
          try {
            createdAt =
                (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
          } catch (e) {
            createdAt = DateTime.now();
          }

          // إنشاء نموذج HiveSentenceModel
          final sentence = HiveSentenceModel(
            id: sentenceId,
            arabicText: data['arabicText'] != null
                ? data['arabicText'] as String? ?? ''
                : '',
            englishText: data['englishText'] != null
                ? data['englishText'] as String? ?? ''
                : '',
            category: data['category'] != null
                ? data['category'] as String? ?? ''
                : '',
            createdAt: createdAt,
            audioUrl:
                data['audioUrl'] != null ? data['audioUrl'] as String? : null,
            difficulty: data['difficulty'] != null
                ? data['difficulty'] as String?
                : null,
            isReadByCurrentUser: false, // تأكد من أن الجملة غير مقروءة
            isFavoriteByCurrentUser: false,
            lastModified: DateTime.now(),
          );

          // تخزين الجملة في Hive
          await _hiveSentenceService
              .getSentencesBox()
              .put(sentenceId, sentence);
          newSentences.add(sentence);
        }
      }

      debugPrint('Returning ${newSentences.length} new random sentences');
      return newSentences;
    } catch (e) {
      debugPrint('Error fetching random sentences: $e');
      return [];
    }
  }

  /// جلب الجمل الجديدة من Firebase
  Future<List<HiveSentenceModel>> fetchNewSentences(String userId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      final online = await isOnline();
      if (!online) {
        debugPrint('لا يوجد اتصال بالإنترنت، تم تخطي جلب الجمل الجديدة');
        return [];
      }

      // الحصول على آخر وقت مزامنة
      final lastSync = _hiveSentenceService.getLastSyncTime();

      // جلب الجمل الجديدة من Firebase
      Query query = _firestore.collection(_sentencesCollection);

      if (lastSync != null) {
        query = query.where('createdAt',
            isGreaterThan: Timestamp.fromDate(lastSync));
      }

      final snapshot = await query.get();

      final newSentences = <HiveSentenceModel>[];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>?;
        final sentenceId = doc.id;

        // التحقق من عدم وجود الجملة في Hive
        if (!_hiveSentenceService.getSentencesBox().containsKey(sentenceId) &&
            data != null) {
          final createdAt =
              data['createdAt'] != null && data['createdAt'] is Timestamp
                  ? (data['createdAt'] as Timestamp).toDate()
                  : data['createdAt'] != null
                      ? DateTime.parse(data['createdAt'] as String)
                      : DateTime.now();

          final sentence = HiveSentenceModel(
            id: sentenceId,
            arabicText: data['arabicText'] != null
                ? data['arabicText'] as String? ?? ''
                : '',
            englishText: data['englishText'] != null
                ? data['englishText'] as String? ?? ''
                : '',
            category: data['category'] != null
                ? data['category'] as String? ?? ''
                : '',
            createdAt: createdAt,
            audioUrl:
                data['audioUrl'] != null ? data['audioUrl'] as String? : null,
            difficulty: data['difficulty'] != null
                ? data['difficulty'] as String?
                : null,
            lastModified: DateTime.now(),
          );

          await _hiveSentenceService
              .getSentencesBox()
              .put(sentenceId, sentence);
          newSentences.add(sentence);
        }
      }

      debugPrint('تم جلب ${newSentences.length} جملة جديدة من Firebase');
      return newSentences;
    } catch (e) {
      debugPrint('خطأ في جلب الجمل الجديدة من Firebase: $e');
      return [];
    }
  }
}
