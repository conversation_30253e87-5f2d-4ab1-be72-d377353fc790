class UserModel {
  final String? id;
  final String? email;
  final String? displayName;
  final String? gender;
  final DateTime? birthDate;
  final String? country;
  final String? photoURL;
  final String? typeUser; // إضافة حقل نوع المستخدم

  UserModel({
    this.id,
    this.email,
    this.displayName,
    this.gender,
    this.birthDate,
    this.country,
    this.photoURL,
    this.typeUser, // إضافة حقل نوع المستخدم
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'gender': gender,
      'birthDate': birthDate?.toIso8601String(),
      'country': country,
      'photoURL': photoURL,
      'typeUser': typeUser, // إضافة حقل نوع المستخدم
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] as String?,
      email: map['email'] as String?,
      displayName: map['displayName'] as String?,
      gender: map['gender'] as String?,
      birthDate:
          map['birthDate'] != null ? DateTime.parse(map['birthDate']) : null,
      country: map['country'] as String?,
      photoURL: map['photoURL'] as String?,
      typeUser: map['typeUser'] as String?, // إضافة حقل نوع المستخدم
    );
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? gender,
    DateTime? birthDate,
    String? country,
    String? photoURL,
    String? typeUser, // إضافة حقل نوع المستخدم
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      country: country ?? this.country,
      photoURL: photoURL ?? this.photoURL,
      typeUser: typeUser ?? this.typeUser, // إضافة حقل نوع المستخدم
    );
  }
}
