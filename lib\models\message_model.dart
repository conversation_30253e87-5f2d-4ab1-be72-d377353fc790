import 'package:cloud_firestore/cloud_firestore.dart';

class MessageModel {
  final String id;
  final String arabicText;
  final String englishText;
  final bool isPersonA; // true = persona A (derecha), false = persona B (izquierda)
  final DateTime createdAt;
  final Map<String, dynamic> readBy; // Registro de quién ha leído este mensaje
  final Map<String, dynamic> testResults; // Resultados de las pruebas de pronunciación

  MessageModel({
    required this.id,
    required this.arabicText,
    required this.englishText,
    required this.isPersonA,
    required this.createdAt,
    required this.readBy,
    required this.testResults,
  });

  // Constructor para crear desde un Map (para Firestore)
  factory MessageModel.fromMap(Map<String, dynamic> map) {
    return MessageModel(
      id: map['id'] ?? '',
      arabicText: map['arabicText'] ?? '',
      englishText: map['englishText'] ?? '',
      isPersonA: map['isPersonA'] ?? true,
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      readBy: map['readBy'] ?? {},
      testResults: map['testResults'] ?? {},
    );
  }

  // Método para convertir a Map para Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'arabicText': arabicText,
      'englishText': englishText,
      'isPersonA': isPersonA,
      'createdAt': Timestamp.fromDate(createdAt),
      'readBy': readBy,
      'testResults': testResults,
    };
  }

  // Método para verificar si el mensaje ha sido leído por el usuario actual
  bool isReadByUser(String userId) {
    return readBy.containsKey(userId);
  }

  // Método para obtener la puntuación de la prueba de pronunciación
  double getTestScore(String userId) {
    if (!testResults.containsKey(userId)) return 0.0;
    return testResults[userId]['score'] ?? 0.0;
  }

  // Método para actualizar el estado de lectura
  MessageModel markAsRead(String userId) {
    Map<String, dynamic> newReadBy = Map.from(readBy);
    newReadBy[userId] = {
      'readAt': Timestamp.now(),
    };

    return MessageModel(
      id: id,
      arabicText: arabicText,
      englishText: englishText,
      isPersonA: isPersonA,
      createdAt: createdAt,
      readBy: newReadBy,
      testResults: testResults,
    );
  }

  // Método para actualizar los resultados de la prueba
  MessageModel updateTestResults(String userId, double score) {
    Map<String, dynamic> newTestResults = Map.from(testResults);
    newTestResults[userId] = {
      'score': score,
      'testedAt': Timestamp.now(),
    };

    return MessageModel(
      id: id,
      arabicText: arabicText,
      englishText: englishText,
      isPersonA: isPersonA,
      createdAt: createdAt,
      readBy: readBy,
      testResults: newTestResults,
    );
  }
}
