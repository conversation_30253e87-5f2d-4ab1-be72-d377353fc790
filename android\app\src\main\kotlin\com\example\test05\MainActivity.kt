package com.example.test05

import android.Manifest
import android.app.AlarmManager
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.test05/notifications"
    private val NOTIFICATION_PERMISSION_REQUEST_CODE = 123

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    // Override to prevent app from being killed on back button press
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            moveTaskToBack(true)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Check and request notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                )
            }
        }

        // Set up method channel for notifications
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "showTestNotification" -> {
                    val title = call.argument<String>("title") ?: "Test Notification"
                    val body = call.argument<String>("body") ?: "This is a test notification"
                    val channelId = call.argument<String>("channelId") ?: "test_channel"
                    val channelName = call.argument<String>("channelName") ?: "Test Channel"
                    val channelDescription = call.argument<String>("channelDescription") ?: "Channel for test notifications"

                    val notificationService = NotificationService(this)
                    notificationService.showNotification(title, body, channelId, channelName, channelDescription)

                    result.success("Notification sent")
                }
                "showNotification" -> {
                    val title = call.argument<String>("title") ?: "Notification"
                    val body = call.argument<String>("body") ?: "This is a notification"
                    val channelId = call.argument<String>("channelId") ?: "default_channel"
                    val channelName = call.argument<String>("channelName") ?: "Default Channel"
                    val channelDescription = call.argument<String>("channelDescription") ?: "Default notification channel"

                    val notificationService = NotificationService(this)
                    notificationService.showNotification(title, body, channelId, channelName, channelDescription)

                    result.success("Notification sent")
                }
                "scheduleNotification" -> {
                    val title = call.argument<String>("title") ?: "Scheduled Notification"
                    val body = call.argument<String>("body") ?: "This is a scheduled notification"
                    val channelId = call.argument<String>("channelId") ?: "scheduled_channel"
                    val channelName = call.argument<String>("channelName") ?: "Scheduled Channel"
                    val channelDescription = call.argument<String>("channelDescription") ?: "Channel for scheduled notifications"
                    val delayInMillis = call.argument<Any>("delayInMillis") ?: 60000L // Default to 1 minute

                    val notificationService = NotificationService(this)
                    notificationService.scheduleNotification(title, body, channelId, channelName, channelDescription, delayInMillis)

                    result.success("Notification scheduled")
                }
                "cancelScheduledNotification" -> {
                    val title = call.argument<String>("title") ?: "Scheduled Notification"
                    val body = call.argument<String>("body") ?: "This is a scheduled notification"
                    val channelId = call.argument<String>("channelId") ?: "scheduled_channel"

                    val notificationService = NotificationService(this)
                    notificationService.cancelScheduledNotification(title, body, channelId)

                    result.success("Scheduled notification canceled")
                }
                "cancelAllScheduledNotifications" -> {
                    // Cancel all scheduled notifications
                    val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    notificationManager.cancelAll()

                    // Also cancel all alarms
                    val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

                    // We can't directly cancel all alarms, but we can cancel the ones we know about
                    val channelIds = listOf(
                        "unfinished_sentences_channel",
                        "morning_reminder_channel",
                        "new_day_motivation_channel",
                        "new_sentences_channel"
                    )

                    val notificationService = NotificationService(this)
                    for (channelId in channelIds) {
                        notificationService.cancelScheduledNotification(
                            "Notification",
                            "Notification body",
                            channelId
                        )
                    }

                    result.success("All scheduled notifications canceled")
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
