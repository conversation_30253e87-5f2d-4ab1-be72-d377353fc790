/// أنواع مجموعات الدروس
enum LessonType {
  sentenceBatch, // دفعة جمل
  conversation, // محادثة
  review // مراجعة
}

/// تحويل نوع الدرس من وإلى نص
extension LessonTypeExtension on LessonType {
  String toJson() {
    switch (this) {
      case LessonType.sentenceBatch:
        return 'sentenceBatch';
      case LessonType.conversation:
        return 'conversation';
      case LessonType.review:
        return 'review';
      default:
        return 'sentenceBatch'; // Default case to handle future enum values
    }
  }

  static LessonType fromJson(String json) {
    switch (json) {
      case 'sentenceBatch':
        return LessonType.sentenceBatch;
      case 'conversation':
        return LessonType.conversation;
      case 'review':
        return LessonType.review;
      default:
        throw ArgumentError('Invalid LessonType: $json');
    }
  }

  String get arabicName {
    switch (this) {
      case LessonType.sentenceBatch:
        return 'دفعة جمل';
      case LessonType.conversation:
        return 'محادثة';
      case LessonType.review:
        return 'مراجعة';
      default:
        return 'دفعة جمل'; // Default case to handle future enum values
    }
  }
}

/// نموذج مجموعة الدروس (دفعة أو محادثة)
class LessonGroup {
  final int id; // معرف المجموعة داخل الدورة (1-4)
  final int cycleId; // معرف الدورة التي تنتمي إليها المجموعة
  final int globalId; // معرف فريد عام للمجموعة في جميع المستويات والدورات
  final String title; // مثل "دفعة 1" أو "محادثة 1"
  final LessonType type; // نوع الدرس (دفعة أو محادثة)
  final int totalSentences; // إجمالي عدد الجمل
  final int completedSentences; // عدد الجمل المكتملة
  final double accuracy; // نسبة الدقة
  final bool isCompleted; // هل تم إكمال المجموعة
  final bool isLocked; // هل المجموعة مغلقة
  final String? routePath; // مسار الانتقال عند النقر على المجموعة

  LessonGroup({
    required this.id,
    required this.cycleId,
    required this.globalId,
    required this.title,
    required this.type,
    required this.totalSentences,
    required this.completedSentences,
    required this.accuracy,
    required this.isCompleted,
    required this.isLocked,
    this.routePath,
  });

  /// إنشاء نسخة من مجموعة الدروس من بيانات JSON
  factory LessonGroup.fromJson(Map<String, dynamic> json) {
    return LessonGroup(
      id: json['id'] as int,
      cycleId: json['cycleId'] as int,
      globalId: json['globalId'] as int,
      title: json['title'] as String,
      type: LessonTypeExtension.fromJson(json['type'] as String),
      totalSentences: json['totalSentences'] as int,
      completedSentences: json['completedSentences'] as int,
      accuracy: (json['accuracy'] as num).toDouble(),
      isCompleted: json['isCompleted'] as bool,
      isLocked: json['isLocked'] as bool,
      routePath: json['routePath'] as String?,
    );
  }

  /// تحويل مجموعة الدروس إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cycleId': cycleId,
      'globalId': globalId,
      'title': title,
      'type': type.toJson(),
      'totalSentences': totalSentences,
      'completedSentences': completedSentences,
      'accuracy': accuracy,
      'isCompleted': isCompleted,
      'isLocked': isLocked,
      'routePath': routePath,
    };
  }

  /// إنشاء نسخة جديدة من مجموعة الدروس مع تحديث بعض الخصائص
  LessonGroup copyWith({
    int? id,
    int? cycleId,
    int? globalId,
    String? title,
    LessonType? type,
    int? totalSentences,
    int? completedSentences,
    double? accuracy,
    bool? isCompleted,
    bool? isLocked,
    String? routePath,
  }) {
    return LessonGroup(
      id: id ?? this.id,
      cycleId: cycleId ?? this.cycleId,
      globalId: globalId ?? this.globalId,
      title: title ?? this.title,
      type: type ?? this.type,
      totalSentences: totalSentences ?? this.totalSentences,
      completedSentences: completedSentences ?? this.completedSentences,
      accuracy: accuracy ?? this.accuracy,
      isCompleted: isCompleted ?? this.isCompleted,
      isLocked: isLocked ?? this.isLocked,
      routePath: routePath ?? this.routePath,
    );
  }

  @override
  String toString() {
    return 'LessonGroup{id: $id, cycleId: $cycleId, globalId: $globalId, title: $title, type: $type, totalSentences: $totalSentences, completedSentences: $completedSentences, accuracy: $accuracy, isCompleted: $isCompleted, isLocked: $isLocked, routePath: $routePath}';
  }
}
