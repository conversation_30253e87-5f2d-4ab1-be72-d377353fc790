import 'package:flutter/material.dart';
import '../models/message_model.dart';
import '../theme/app_theme.dart';

class ChatBubble extends StatefulWidget {
  final MessageModel message;
  final bool isRead;
  final double testScore;
  final Function(int) onMarkAsRead;
  final Function(int) onOpenQuiz;
  final int index;
  final bool
      isActive; // Indica si esta burbuja es la activa (la que se debe leer ahora)

  const ChatBubble({
    Key? key,
    required this.message,
    required this.isRead,
    required this.testScore,
    required this.onMarkAsRead,
    required this.onOpenQuiz,
    required this.index,
    this.isActive = false, // Por defecto, no es activa
  }) : super(key: key);

  @override
  State<ChatBubble> createState() => _ChatBubbleState();
}

class _ChatBubbleState extends State<ChatBubble> {
  bool _showTranslation = false;

  @override
  Widget build(BuildContext context) {
    // Determinar el color de fondo de la burbuja según si está activa o leída
    Color bubbleColor;
    if (widget.isRead) {
      // Si está leída, color verde claro
      bubbleColor = Colors.green.withAlpha(50);
    } else if (widget.isActive) {
      // Si está activa pero no leída, color destacado
      bubbleColor = widget.message.isPersonA
          ? AppTheme.primaryColor.withAlpha(80) // Más intenso para la activa
          : Colors.amber.withAlpha(80); // Color ámbar para destacar
    } else {
      // Si no está activa ni leída, color normal
      bubbleColor = widget.message.isPersonA
          ? AppTheme.primaryColor.withAlpha(30)
          : Colors.grey.shade200;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: widget.message.isPersonA
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // Fila que contiene avatar y burbuja de chat
          Row(
            mainAxisAlignment: widget.message.isPersonA
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Avatar (solo para mensajes de persona B - izquierda)
              if (!widget.message.isPersonA)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey.shade300,
                    backgroundImage: const AssetImage('assets/images/avatar_b.png'),
                    child: !const AssetImage('assets/images/avatar_b.png').toString().contains('avatar_b.png')
                        ? const Icon(Icons.person, size: 16, color: Colors.white)
                        : null,
                  ),
                ),

              // Burbuja de chat
              Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.65, // Reducido para dejar espacio para el avatar
                ),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: bubbleColor,
                  borderRadius: BorderRadius.circular(16).copyWith(
                    bottomRight:
                        widget.message.isPersonA ? const Radius.circular(0) : null,
                    bottomLeft:
                        !widget.message.isPersonA ? const Radius.circular(0) : null,
                  ),
                  // Añadir sombra si está activa para destacarla más
                  boxShadow: widget.isActive
                      ? [
                          BoxShadow(
                            color: Colors.black.withAlpha(30),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          )
                        ]
                      : null,
                ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Texto en inglés (visible solo si está activa o leída)
                if (widget.isActive || widget.isRead)
                  Directionality(
                    textDirection: TextDirection.ltr,
                    child: Text(
                      widget.message.englishText,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        // Texto más oscuro si está activa para destacarlo
                        color: widget.isActive ? Colors.black : Colors.black87,
                      ),
                    ),
                  )
                else
                  // Placeholder para mensajes no activos ni leídos
                  Container(
                    width: double.infinity,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(30),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),

                // Botón para mostrar/ocultar traducción (solo visible si está activa o leída)
                if (widget.isActive || widget.isRead)
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _showTranslation = !_showTranslation;
                        });
                      },
                      icon: Icon(
                        _showTranslation
                            ? Icons.visibility_off
                            : Icons.visibility,
                        size: 16,
                      ),
                      label: Text(
                        _showTranslation ? 'إخفاء الترجمة' : 'إظهار الترجمة',
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  ),

                // Texto en árabe (visible solo si _showTranslation es true y está activa o leída)
                if (_showTranslation && (widget.isActive || widget.isRead)) ...[
                  const Divider(height: 8),
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: Text(
                      widget.message.arabicText,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ],
            ),

              // Avatar (solo para mensajes de persona A - derecha)
              if (widget.message.isPersonA)
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: Colors.grey.shade300,
                    backgroundImage: const AssetImage('assets/images/avatar_a.png'),
                    child: const Icon(Icons.person, size: 16, color: Colors.white),
                  ),
                ),
            ],
          ),

          // Botones de acción
          Padding(
            padding: EdgeInsets.only(
              right: widget.message.isPersonA ? 8 : 0,
              left: !widget.message.isPersonA ? 8 : 0,
              top: 4,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Botón de marcar como leído (solo habilitado si está activa y no leída)
                IconButton(
                  icon: Icon(
                    widget.isRead
                        ? Icons.check_circle
                        : Icons.check_circle_outline,
                    color: widget.isRead ? Colors.green : Colors.grey,
                    size: 20,
                  ),
                  onPressed: widget.isRead || !widget.isActive
                      ? null
                      : () => widget.onMarkAsRead(widget.index),
                  tooltip: widget.isRead ? 'تمت القراءة' : 'تحديد كمقروء',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                const SizedBox(width: 8),
                // Botón de prueba (solo habilitado si está activa o leída)
                IconButton(
                  icon: Icon(
                    Icons.mic,
                    color: widget.testScore > 0
                        ? Colors.orange
                        : (widget.isActive || widget.isRead)
                            ? Colors.grey
                            : Colors.grey.withAlpha(100),
                    size: 20,
                  ),
                  onPressed: (widget.isActive || widget.isRead)
                      ? () => widget.onOpenQuiz(widget.index)
                      : null,
                  tooltip: 'اختبار النطق',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                if (widget.testScore > 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '${(widget.testScore * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
