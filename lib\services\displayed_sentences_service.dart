import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../constants/hive_constants.dart';

/// خدمة إدارة الجمل المعروضة للمستخدم
class DisplayedSentencesService {
  // الحصول على صندوق الجمل المعروضة
  final Box<String> _displayedSentencesBox =
      Hive.box<String>(HiveConstants.displayedSentencesBox);

  /// إضافة جملة إلى قائمة الجمل المعروضة
  Future<void> addSentence(String sentenceId) async {
    try {
      if (!_displayedSentencesBox.containsKey(sentenceId)) {
        await _displayedSentencesBox.put(sentenceId, sentenceId);
        debugPrint('تم إضافة الجملة $sentenceId إلى قائمة الجمل المعروضة');
      }
    } catch (e) {
      debugPrint('خطأ في إضافة الجملة إلى قائمة الجمل المعروضة: $e');
    }
  }

  /// التحقق مما إذا كانت الجملة معروضة
  bool isSentenceDisplayed(String sentenceId) {
    try {
      return _displayedSentencesBox.containsKey(sentenceId);
    } catch (e) {
      debugPrint('خطأ في التحقق من عرض الجملة: $e');
      return false;
    }
  }

  /// الحصول على جميع معرفات الجمل المعروضة
  Set<String> getAllDisplayedSentenceIds() {
    try {
      return _displayedSentencesBox.keys.cast<String>().toSet();
    } catch (e) {
      debugPrint('خطأ في الحصول على معرفات الجمل المعروضة: $e');
      return {};
    }
  }

  /// مسح قائمة الجمل المعروضة
  Future<void> clearDisplayedSentences() async {
    try {
      await _displayedSentencesBox.clear();
      debugPrint('تم مسح قائمة الجمل المعروضة');
    } catch (e) {
      debugPrint('خطأ في مسح قائمة الجمل المعروضة: $e');
    }
  }

  /// الحصول على عدد الجمل المعروضة
  int getDisplayedSentencesCount() {
    try {
      return _displayedSentencesBox.length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الجمل المعروضة: $e');
      return 0;
    }
  }

  /// إزالة جملة من قائمة الجمل المعروضة
  Future<void> removeSentence(String sentenceId) async {
    try {
      if (_displayedSentencesBox.containsKey(sentenceId)) {
        await _displayedSentencesBox.delete(sentenceId);
        debugPrint('تم إزالة الجملة $sentenceId من قائمة الجمل المعروضة');
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الجملة من قائمة الجمل المعروضة: $e');
    }
  }
}
