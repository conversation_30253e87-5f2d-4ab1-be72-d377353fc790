import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class SentenceModel {
  final String id;
  final String arabicText;
  final String englishText;
  final String category;
  final DateTime createdAt;
  final Map<String, dynamic>
      readBy; // Will be deprecated - kept for backward compatibility
  final bool isFavorite; // Will be deprecated - kept for backward compatibility
  final String? audioUrl;
  final String? difficulty; // إضافة حقل مستوى الصعوبة

  // Fields to track read status and favorite status without modifying the sentence
  bool isReadByCurrentUser = false;
  bool isFavoriteByCurrentUser = false;
  bool isPronunciationTested = false; // إضافة حقل لتتبع ما إذا تم اختبار النطق
  bool isConversation = false; // إضافة حقل لتمييز المحادثات عن الجمل العادية

  SentenceModel({
    required this.id,
    required this.arabicText,
    required this.englishText,
    required this.category,
    required this.createdAt,
    required this.readBy, // Will be deprecated
    this.isFavorite = false, // Will be deprecated
    this.audioUrl,
    this.difficulty, // إضافة حقل مستوى الصعوبة
    this.isReadByCurrentUser = false,
    this.isFavoriteByCurrentUser = false,
    this.isPronunciationTested = false,
    this.isConversation = false, // إضافة حقل لتمييز المحادثات
  });

  factory SentenceModel.fromMap(
    Map<String, dynamic> map,
    String id, {
    bool isReadByCurrentUser = false,
    bool isFavoriteByCurrentUser = false,
    bool isPronunciationTested = false,
    bool isConversation = false,
  }) {
    // معالجة حقل createdAt بطريقة آمنة
    DateTime createdAt;
    final createdAtValue = map['createdAt'];

    if (createdAtValue == null) {
      createdAt = DateTime.now();
    } else if (createdAtValue is DateTime) {
      createdAt = createdAtValue;
    } else if (createdAtValue is String) {
      try {
        createdAt = DateTime.parse(createdAtValue);
      } catch (e) {
        createdAt = DateTime.now();
      }
    } else if (createdAtValue is Timestamp) {
      createdAt = createdAtValue.toDate();
    } else {
      try {
        // محاولة استدعاء toDate() إذا كان الكائن يدعم هذه الدالة
        createdAt = createdAtValue.toDate();
      } catch (e) {
        createdAt = DateTime.now();
      }
    }

    return SentenceModel(
      id: id,
      arabicText: map['arabicText'] ?? '',
      englishText: map['englishText'] ?? '',
      category: map['category'] ?? '',
      createdAt: createdAt,
      readBy: Map<String, dynamic>.from(map['readBy'] ?? {}),
      isFavorite: map['isFavorite'] ?? false,
      audioUrl: map['audioUrl'],
      difficulty: map['difficulty'] ??
          'medium', // إضافة حقل مستوى الصعوبة مع قيمة افتراضية
      isReadByCurrentUser: map['isReadByCurrentUser'] ?? isReadByCurrentUser,
      isFavoriteByCurrentUser:
          map['isFavoriteByCurrentUser'] ?? isFavoriteByCurrentUser,
      isPronunciationTested:
          map['isPronunciationTested'] ?? isPronunciationTested,
      isConversation: map['isConversation'] ?? isConversation,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'arabicText': arabicText,
      'englishText': englishText,
      'category': category,
      'createdAt': createdAt.toIso8601String(), // تحويل DateTime إلى سلسلة نصية
      'readBy': readBy, // Will be deprecated
      'isFavorite': isFavorite, // Will be deprecated
      'audioUrl': audioUrl,
      'difficulty': difficulty, // إضافة حقل مستوى الصعوبة
      'isReadByCurrentUser': isReadByCurrentUser, // إضافة حقل حالة القراءة
      'isFavoriteByCurrentUser':
          isFavoriteByCurrentUser, // إضافة حقل حالة المفضلة
      'isPronunciationTested':
          isPronunciationTested, // إضافة حقل حالة اختبار النطق
      'isConversation': isConversation, // إضافة حقل لتمييز المحادثات
    };
  }

  String toJson() => json.encode(toMap());

  factory SentenceModel.fromJson(String source) {
    final map = json.decode(source) as Map<String, dynamic>;
    final id = map['id'] ?? '';

    // تحويل سلسلة التاريخ إلى كائن DateTime
    DateTime createdAt;
    if (map['createdAt'] != null) {
      try {
        createdAt = DateTime.parse(map['createdAt']);
      } catch (e) {
        createdAt = DateTime.now();
      }
    } else {
      createdAt = DateTime.now();
    }

    // إنشاء نسخة جديدة من الخريطة مع استبدال حقل createdAt
    final newMap = Map<String, dynamic>.from(map);
    newMap['createdAt'] = createdAt;

    return SentenceModel.fromMap(
      newMap,
      id,
      isReadByCurrentUser: map['isReadByCurrentUser'] ?? false,
      isFavoriteByCurrentUser: map['isFavoriteByCurrentUser'] ?? false,
      isPronunciationTested: map['isPronunciationTested'] ?? false,
      isConversation: map['isConversation'] ?? false,
    );
  }

  SentenceModel copyWith({
    String? arabicText,
    String? englishText,
    String? category,
    DateTime? createdAt,
    Map<String, dynamic>? readBy,
    bool? isFavorite,
    String? audioUrl,
    String? difficulty, // إضافة حقل مستوى الصعوبة
    bool? isReadByCurrentUser,
    bool? isFavoriteByCurrentUser,
    bool? isPronunciationTested,
    bool? isConversation,
  }) {
    return SentenceModel(
      id: id,
      arabicText: arabicText ?? this.arabicText,
      englishText: englishText ?? this.englishText,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      readBy: readBy ?? this.readBy,
      isFavorite: isFavorite ?? this.isFavorite,
      audioUrl: audioUrl ?? this.audioUrl,
      difficulty: difficulty ?? this.difficulty, // إضافة حقل مستوى الصعوبة
      isReadByCurrentUser: isReadByCurrentUser ?? this.isReadByCurrentUser,
      isFavoriteByCurrentUser:
          isFavoriteByCurrentUser ?? this.isFavoriteByCurrentUser,
      isPronunciationTested:
          isPronunciationTested ?? this.isPronunciationTested,
      isConversation: isConversation ?? this.isConversation,
    );
  }

  // Legacy method - will be deprecated
  bool isReadBy(String userId) {
    // If this is the current user, use the new field
    if (isReadByCurrentUser) {
      return true;
    }
    // Otherwise, fall back to the legacy field
    return readBy.containsKey(userId);
  }

  // Legacy method - will be deprecated
  DateTime? getReadTime(String userId) {
    final timestamp = readBy[userId];
    return timestamp?.toDate();
  }

  // New method to check if a sentence is a favorite for the current user
  bool isFavoriteBy() {
    return isFavoriteByCurrentUser;
  }

  @override
  String toString() {
    return 'SentenceModel(id: $id, arabicText: $arabicText, englishText: $englishText, category: $category, readBy: $readBy, isFavorite: $isFavorite, createdAt: $createdAt, audioUrl: $audioUrl, difficulty: $difficulty, isReadByCurrentUser: $isReadByCurrentUser, isFavoriteByCurrentUser: $isFavoriteByCurrentUser, isPronunciationTested: $isPronunciationTested, isConversation: $isConversation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SentenceModel &&
        other.id == id &&
        other.arabicText == arabicText &&
        other.englishText == englishText &&
        other.category == category &&
        other.isFavorite == isFavorite &&
        other.audioUrl == audioUrl &&
        other.difficulty == difficulty &&
        other.isReadByCurrentUser == isReadByCurrentUser &&
        other.isFavoriteByCurrentUser == isFavoriteByCurrentUser &&
        other.isPronunciationTested == isPronunciationTested &&
        other.isConversation == isConversation;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        arabicText.hashCode ^
        englishText.hashCode ^
        category.hashCode ^
        isFavorite.hashCode ^
        audioUrl.hashCode ^
        difficulty.hashCode ^
        isReadByCurrentUser.hashCode ^
        isFavoriteByCurrentUser.hashCode ^
        isPronunciationTested.hashCode ^
        isConversation.hashCode;
  }

  // Use this getter to check if the sentence is read by the current user
  bool get isRead => isReadByCurrentUser;
}
