import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/level_provider.dart';
import '../models/level.dart';
import '../models/cycle.dart';
import '../models/lesson_group.dart';
import 'timeline_screen.dart';

class TestCyclesScreen extends StatefulWidget {
  static const routeName = '/test-cycles';

  const TestCyclesScreen({Key? key}) : super(key: key);

  @override
  State<TestCyclesScreen> createState() => _TestCyclesScreenState();
}

class _TestCyclesScreenState extends State<TestCyclesScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      await levelProvider.fetchLevels();
    } catch (e) {
      debugPrint('Error loading levels: $e');
      // إذا كان الخطأ بسبب عدم وجود المزود، قم بإعادة بناء الشاشة
      if (e.toString().contains('Provider<LevelProvider>')) {
        setState(() {}); // إعادة بناء الشاشة
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحقق من وجود المزود
    try {
      Provider.of<LevelProvider>(context, listen: false);
    } catch (e) {
      // إذا لم يكن المزود موجودًا، قم بإنشاء مزود محلي
      return ChangeNotifierProvider<LevelProvider>(
        create: (_) => LevelProvider(),
        child: _buildScaffold(context),
      );
    }

    // إذا كان المزود موجودًا، استخدمه مباشرة
    return _buildScaffold(context);
  }

  Widget _buildScaffold(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نموذج الدورات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Consumer<LevelProvider>(
              builder: (ctx, levelProvider, _) {
                if (levelProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('حدث خطأ: ${levelProvider.error}'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                final levels = levelProvider.levels;
                if (levels.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('لا توجد مستويات متاحة'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'نموذج الدورات الجديد',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const TimelineScreen(),
                            ),
                          );
                        },
                        child: const Text('عرض مسار التعلم الجديد'),
                      ),
                      const SizedBox(height: 24),
                      const Text(
                        'المستويات المتاحة:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...levels.map((level) => _buildLevelCard(level)),
                    ],
                  ),
                );
              },
            ),
    );
  }

  Widget _buildLevelCard(Level level) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: level.isLocked ? Colors.grey : Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${level.id}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المستوى ${level.id}: ${level.title}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'النقاط: ${level.earnedEducationalPoints}/${level.totalEducationalPoints}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        'الصعوبة: ${_getDifficultyText(level.difficulty)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: _getDifficultyColor(level.difficulty),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  level.isLocked ? Icons.lock : Icons.lock_open,
                  color: level.isLocked ? Colors.grey : Colors.green,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'الدورات:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...level.cycles.map((cycle) => _buildCycleItem(level, cycle)),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleItem(Level level, Cycle cycle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: cycle.isLocked ? Colors.grey.shade300 : Colors.blue.shade300,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: cycle.isLocked ? Colors.grey : Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${cycle.id}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cycle.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '${cycle.completedSentences}/${cycle.totalSentences}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                cycle.isLocked ? Icons.lock : Icons.lock_open,
                color: cycle.isLocked ? Colors.grey : Colors.green,
                size: 16,
              ),
            ],
          ),
          if (cycle.lessonGroups.isNotEmpty) ...[
            const SizedBox(height: 8),
            const Text(
              'المجموعات:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            ...cycle.lessonGroups
                .map((group) => _buildGroupItem(level, cycle, group)),
          ],
        ],
      ),
    );
  }

  // دالة مساعدة للحصول على نص مستوى الصعوبة
  String _getDifficultyText(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return 'سهل';
      case 'medium':
        return 'متوسط';
      case 'hard':
        return 'صعب';
      default:
        return 'متوسط';
    }
  }

  // دالة مساعدة للحصول على لون مستوى الصعوبة
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  Widget _buildGroupItem(Level level, Cycle cycle, LessonGroup group) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4, left: 16),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: group.isLocked ? Colors.grey.shade300 : Colors.blue.shade100,
        ),
      ),
      child: Row(
        children: [
          Text(
            '${group.id}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(group.title),
          ),
          Text(
            '${group.completedSentences}/${group.totalSentences}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          Icon(
            group.isLocked ? Icons.lock : Icons.lock_open,
            color: group.isLocked ? Colors.grey : Colors.green,
            size: 14,
          ),
        ],
      ),
    );
  }
}
