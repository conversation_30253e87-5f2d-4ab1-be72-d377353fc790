// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_sentence_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveSentenceModelAdapter extends TypeAdapter<HiveSentenceModel> {
  @override
  final int typeId = 1;

  @override
  HiveSentenceModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveSentenceModel(
      id: fields[0] as String,
      arabicText: fields[1] as String,
      englishText: fields[2] as String,
      category: fields[3] as String,
      createdAt: fields[4] as DateTime,
      audioUrl: fields[5] as String?,
      difficulty: fields[6] as String?,
      isReadByCurrentUser: fields[7] as bool,
      isFavoriteByCurrentUser: fields[8] as bool,
      lastModified: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveSentenceModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.arabicText)
      ..writeByte(2)
      ..write(obj.englishText)
      ..writeByte(3)
      ..write(obj.category)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.audioUrl)
      ..writeByte(6)
      ..write(obj.difficulty)
      ..writeByte(7)
      ..write(obj.isReadByCurrentUser)
      ..writeByte(8)
      ..write(obj.isFavoriteByCurrentUser)
      ..writeByte(9)
      ..write(obj.lastModified);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveSentenceModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
