# تحليل الملفات المسؤولة عن صفحة المراجعة

## الملف الرئيسي لصفحة المراجعة:

### 1. `lib/screens/review_screen.dart`
**هذا هو الملف الوحيد والرئيسي المسؤول عن صفحة المراجعة**

**الوظائف الرئيسية:**
- عرض بطاقات المجموعات عند `showGroups: true`
- عرض الجمل للمراجعة عند `showGroups: false`
- جلب المحادثات من `/users/{userId}/readConversations/{groupId}`
- جلب الجمل العادية من `/users/{userId}/readSentences`

**المعاملات:**
```dart
const ReviewScreen({
  required this.levelId,
  required this.cycleId,
  required this.groupId,
  required this.title,
  this.showGroups = false, // القيمة الافتراضية false
});
```

## الملفات التي تستدعي صفحة المراجعة:

### 2. `lib/screens/timeline_screen.dart`
**يستدعي ReviewScreen مع `showGroups: true`**
```dart
case LessonType.review:
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ReviewScreen(
        levelId: level.id,
        cycleId: group.cycleId,
        groupId: group.id,
        title: group.title,
        showGroups: true, // ✅ يعرض البطاقات
      ),
    ),
  );
```

### 3. `lib/screens/timeline_screen_new.dart`
**نسخة جديدة من timeline_screen - نفس الاستدعاء**
```dart
case LessonType.review:
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ReviewScreen(
        levelId: level.id,
        cycleId: cycle.id,
        groupId: group.id,
        title: group.title,
        showGroups: true, // ✅ يعرض البطاقات
      ),
    ),
  );
```

### 4. `lib/main.dart`
**يحتوي على route افتراضي للمراجعة**
```dart
'/review': (context) => const ReviewScreen(
  levelId: 1,
  cycleId: 1,
  groupId: 1,
  title: 'مراجعة',
  showGroups: false, // ❌ لا يعرض البطاقات
),
```

### 5. داخل `review_screen.dart` نفسه
**عند النقر على بطاقة مجموعة**
```dart
Navigator.pushReplacement(
  context,
  MaterialPageRoute(
    builder: (context) => ReviewScreen(
      levelId: widget.levelId,
      cycleId: cycle.id,
      groupId: group.id,
      title: group.title,
      showGroups: false, // ✅ يعرض الجمل
    ),
  ),
);
```

## تحليل المشكلة:

### المشكلة المحتملة:
إذا كانت البطاقات "معادة" (مكررة)، فقد يكون السبب:

1. **استدعاء خاطئ:** قد يتم استدعاء ReviewScreen من مكان آخر مع `showGroups: false`
2. **مشكلة في البيانات:** قد تكون هناك مجموعات مكررة في `_groupsToReview`
3. **مشكلة في العرض:** قد تكون هناك مشكلة في دالة `_buildGroupsList()`

### الملفات ذات الصلة الأخرى:

#### 6. `lib/models/lesson_group.dart`
**يحتوي على نموذج LessonGroup المستخدم في البطاقات**

#### 7. `lib/models/cycle.dart`
**يحتوي على نموذج Cycle المستخدم في البطاقات**

#### 8. `lib/providers/level_provider.dart`
**يوفر بيانات المستويات والدورات والمجموعات**

## لا توجد ملفات أخرى لصفحة المراجعة:

✅ **تأكيد:** لا يوجد ملف آخر يحتوي على تطبيق بديل لصفحة المراجعة
✅ **تأكيد:** `review_screen.dart` هو الملف الوحيد المسؤول عن صفحة المراجعة
✅ **تأكيد:** لا توجد widgets منفصلة للمراجعة

## التوصيات للتشخيص:

### 1. فحص استدعاء الصفحة:
```dart
// تحقق من كيفية الوصول لصفحة المراجعة
// هل تم الدخول من Timeline مع showGroups: true؟
```

### 2. فحص البيانات:
```dart
// تحقق من محتوى _groupsToReview
// هل هناك مجموعات مكررة؟
debugPrint('عدد المجموعات: ${_groupsToReview.length}');
for (var group in _groupsToReview) {
  debugPrint('المجموعة: ${group['group'].title}');
}
```

### 3. فحص دالة _loadGroups():
```dart
// تحقق من منطق جلب المجموعات
// هل يتم إضافة نفس المجموعة أكثر من مرة؟
```

## الخلاصة:

- **الملف الرئيسي:** `lib/screens/review_screen.dart` فقط
- **لا توجد ملفات بديلة** لصفحة المراجعة
- **المشكلة محتملة في:** منطق جلب البيانات أو طريقة الاستدعاء
- **الحل:** فحص دالة `_loadGroups()` ومحتوى `_groupsToReview`
